const { exec } = require('child_process');

function revertAllMigrations() {
    exec('ts-node ./node_modules/typeorm/cli.js migration:show -d ./src/configs/data-source.conf.ts', (error, stdout, stderr) => {
        if (error) {
            console.error(`exec error: ${error}`);
            return;
        }

        // Kiểm tra xem có bất kỳ migration nào chưa được thực hiện không
        console.log(stdout)
        const hasPendingMigrations = stdout.includes('[X]');

        if (hasPendingMigrations) {
            exec('npm run migrate:down', (error, stdout, stderr) => {
                if (error) {
                    console.log('An error occurred while reverting the migration');
                } else {
                    console.log('A migration has been reverted');
                    revertAllMigrations();
                }
            });
        } else {
            console.log('All migrations have been reverted');
        }
    });
}

revertAllMigrations();
