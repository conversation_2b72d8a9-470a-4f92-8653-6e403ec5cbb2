const { spawn } = require('child_process');
const readline = require('readline');
const path = require('path');
const fs = require('fs').promises; // Dùng fs.promises để sử dụng async/await
const { Table, QueryRunner } = require('typeorm'); // ✅ Import Table

const rl = readline.createInterface({
    input: process.stdin,
    output: process.stdout,
});

const { AppDataSource } = require('./src/configs/data-source.conf');

rl.question('Enter migration name: ', async (migrationName) => {
    const migrationsDir = path.resolve(__dirname, AppDataSource.options.migrations[0].replace('/*{.ts,.js}', ''));

    console.log(`Using migrations directory: ${migrationsDir}`);

    // Kiểm tra format create-{table}-table
    const match = migrationName.match(/^create-(\w+)-table$/);
    const isCreateTableMigration = !!match;
    const tableName = match ? match[1] : null;

    try {
        // Chạy TypeORM command để tạo migration
        await runCommand('npx', ['typeorm', 'migration:create', `${migrationsDir}/${migrationName}`]);

        console.log(`Migration created successfully.`);

        // Nếu không phải create-{table}-table thì bỏ qua việc chỉnh sửa file
        if (!isCreateTableMigration) {
            console.log(`Skipping file update because migration does not follow create-{table}-table format.`);
            process.exit(0);
        }

        console.log(`Detected table name: ${tableName}. Updating migration file...`);

        // Tìm migration file mới nhất
        const migrationFilePath = await findMigrationFile(migrationsDir, migrationName);

        if (!migrationFilePath) {
            console.error(`Migration file not found!`);
            process.exit(1);
        }

        console.log(`Detected migration file: ${migrationFilePath}`);

        // Đọc file và cập nhật nội dung
        let data = await fs.readFile(migrationFilePath, 'utf8');

        data = data
            .replace(/import { MigrationInterface, QueryRunner } from "typeorm";/, `import { MigrationInterface, QueryRunner, Table } from "typeorm";`)
            .replace(
                /async up\(queryRunner: QueryRunner\): Promise<void> {[\s\S]*?}/,
                `async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.createTable(
            new Table({
                name: '${tableName}',
                columns: [
                    { name: 'id', type: 'int', isPrimary: true, isGenerated: true, generationStrategy: 'increment' },
                    { name: 'created_at', type: 'timestamptz', default: 'now()' },
                ],
            })
        );
    }`
            )
            .replace(
                /async down\(queryRunner: QueryRunner\): Promise<void> {[\s\S]*?}/,
                `async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.dropTable('${tableName}');
    }`
            );

        // Ghi đè file migration với nội dung mới
        await fs.writeFile(migrationFilePath, data, 'utf8');

        console.log(`Migration file updated with table: ${tableName}`);
        process.exit(0);
    } catch (error) {
        console.error(`Error: ${error}`);
        process.exit(1);
    }
});

/**
 * Chạy lệnh trong child process và chờ hoàn thành
 */
function runCommand(command, args) {
    return new Promise((resolve, reject) => {
        const process = spawn(command, args, { stdio: 'inherit', shell: true });

        process.on('close', (code) => {
            if (code === 0) resolve();
            else reject(`Command failed with exit code ${code}`);
        });
    });
}

/**
 * Tìm file migration mới nhất chứa migrationName
 */
async function findMigrationFile(migrationsDir, migrationName) {
    try {
        const files = await fs.readdir(migrationsDir);
        return files
            .filter((file) => file.includes(migrationName) && file.endsWith('.ts'))
            .sort((a, b) => b.localeCompare(a)) // Sắp xếp theo timestamp mới nhất
            .map((file) => path.join(migrationsDir, file))[0];
    } catch (err) {
        console.error(`Error reading migration directory: ${err}`);
        return null;
    }
}
