import { MigrationInterface, QueryRunner } from 'typeorm';

export class CreateFolderUsersTable1751940462526 implements MigrationInterface {
    name = 'CreateFolderUsersTable1751940462526';

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(
            `CREATE TABLE "folder_users" ("folder_id" integer NOT NULL, "user_id" integer NOT NULL, CONSTRAINT "PK_540414720684be337ac24064cee" PRIMARY KEY ("folder_id", "user_id"))`
        );
        await queryRunner.query(`CREATE INDEX "IDX_2577ae5e598a7b222742ea59f1" ON "folder_users" ("folder_id") `);
        await queryRunner.query(`CREATE INDEX "IDX_dd3e3494e5ddc71dab59e00b5d" ON "folder_users" ("user_id") `);
        await queryRunner.query(
            `ALTER TABLE "folder_users" ADD CONSTRAINT "FK_2577ae5e598a7b222742ea59f13" FOREIGN KEY ("folder_id") REFERENCES "folders"("id") ON DELETE CASCADE ON UPDATE CASCADE`
        );
        await queryRunner.query(
            `ALTER TABLE "folder_users" ADD CONSTRAINT "FK_dd3e3494e5ddc71dab59e00b5d1" FOREIGN KEY ("user_id") REFERENCES "users"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`
        );
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "folder_users" DROP CONSTRAINT "FK_dd3e3494e5ddc71dab59e00b5d1"`);
        await queryRunner.query(`ALTER TABLE "folder_users" DROP CONSTRAINT "FK_2577ae5e598a7b222742ea59f13"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_dd3e3494e5ddc71dab59e00b5d"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_2577ae5e598a7b222742ea59f1"`);
        await queryRunner.query(`DROP TABLE "folder_users"`);
    }
}
