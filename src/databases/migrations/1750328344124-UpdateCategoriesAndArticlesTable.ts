import { MigrationInterface, QueryRunner } from 'typeorm';

export class UpdateCategoriesAndArticlesTable1750328344124 implements MigrationInterface {
    name = 'UpdateCategoriesAndArticlesTable1750328344124';

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "articles" DROP CONSTRAINT "FK_9c36a2b5a85ec7464169eec22e0"`);
        await queryRunner.query(`ALTER TABLE "categories" DROP CONSTRAINT "FK_a68e0427b1638747fa2110dd67d"`);
        await queryRunner.query(`ALTER TABLE "articles" DROP COLUMN "layout_id"`);
        await queryRunner.query(`ALTER TABLE "categories" DROP COLUMN "layout_id"`);
        await queryRunner.query(`ALTER TABLE "articles" ADD "web_layout_id" integer`);
        await queryRunner.query(`ALTER TABLE "articles" ADD "mobile_layout_id" integer`);
        await queryRunner.query(`ALTER TABLE "categories" ADD "web_layout_id" integer`);
        await queryRunner.query(`ALTER TABLE "categories" ADD "mobile_layout_id" integer`);
        await queryRunner.query(`ALTER TABLE "categories" ADD "article_web_layout_id" integer`);
        await queryRunner.query(`ALTER TABLE "categories" ADD "article_mobile_layout_id" integer`);
        await queryRunner.query(
            `ALTER TABLE "articles" ADD CONSTRAINT "FK_0579c4d7c0b9addc83b70858bd4" FOREIGN KEY ("web_layout_id") REFERENCES "layouts"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`
        );
        await queryRunner.query(
            `ALTER TABLE "articles" ADD CONSTRAINT "FK_516422ab2b095a9b0ffab3541ed" FOREIGN KEY ("mobile_layout_id") REFERENCES "layouts"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`
        );
        await queryRunner.query(
            `ALTER TABLE "categories" ADD CONSTRAINT "FK_af1ea6580fa1445b61699a6f4f7" FOREIGN KEY ("web_layout_id") REFERENCES "layouts"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`
        );
        await queryRunner.query(
            `ALTER TABLE "categories" ADD CONSTRAINT "FK_523257b4cc998ae83ddc8240834" FOREIGN KEY ("mobile_layout_id") REFERENCES "layouts"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`
        );
        await queryRunner.query(
            `ALTER TABLE "categories" ADD CONSTRAINT "FK_228d4d0b2d0a24300ad06f82217" FOREIGN KEY ("article_web_layout_id") REFERENCES "layouts"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`
        );
        await queryRunner.query(
            `ALTER TABLE "categories" ADD CONSTRAINT "FK_a452e1e2a5a8c268b2f2c9d0619" FOREIGN KEY ("article_mobile_layout_id") REFERENCES "layouts"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`
        );
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "categories" DROP CONSTRAINT "FK_a452e1e2a5a8c268b2f2c9d0619"`);
        await queryRunner.query(`ALTER TABLE "categories" DROP CONSTRAINT "FK_228d4d0b2d0a24300ad06f82217"`);
        await queryRunner.query(`ALTER TABLE "categories" DROP CONSTRAINT "FK_523257b4cc998ae83ddc8240834"`);
        await queryRunner.query(`ALTER TABLE "categories" DROP CONSTRAINT "FK_af1ea6580fa1445b61699a6f4f7"`);
        await queryRunner.query(`ALTER TABLE "articles" DROP CONSTRAINT "FK_516422ab2b095a9b0ffab3541ed"`);
        await queryRunner.query(`ALTER TABLE "articles" DROP CONSTRAINT "FK_0579c4d7c0b9addc83b70858bd4"`);
        await queryRunner.query(`ALTER TABLE "categories" DROP COLUMN "article_mobile_layout_id"`);
        await queryRunner.query(`ALTER TABLE "categories" DROP COLUMN "article_web_layout_id"`);
        await queryRunner.query(`ALTER TABLE "categories" DROP COLUMN "mobile_layout_id"`);
        await queryRunner.query(`ALTER TABLE "categories" DROP COLUMN "web_layout_id"`);
        await queryRunner.query(`ALTER TABLE "articles" DROP COLUMN "mobile_layout_id"`);
        await queryRunner.query(`ALTER TABLE "articles" DROP COLUMN "web_layout_id"`);
        await queryRunner.query(`ALTER TABLE "categories" ADD "layout_id" integer`);
        await queryRunner.query(`ALTER TABLE "articles" ADD "layout_id" integer`);
        await queryRunner.query(
            `ALTER TABLE "categories" ADD CONSTRAINT "FK_a68e0427b1638747fa2110dd67d" FOREIGN KEY ("layout_id") REFERENCES "layouts"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`
        );
        await queryRunner.query(
            `ALTER TABLE "articles" ADD CONSTRAINT "FK_9c36a2b5a85ec7464169eec22e0" FOREIGN KEY ("layout_id") REFERENCES "layouts"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`
        );
    }
}
