import { MigrationInterface, QueryRunner } from 'typeorm';

export class CreateNotificationsTable1748249940415 implements MigrationInterface {
    name = 'CreateNotificationsTable1748249940415';

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(
            `CREATE TABLE "notifications" ("id" SERIAL NOT NULL, "created_by" integer, "updated_by" integer, "deleted_by" integer, "created_at" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(), "updated_at" TIMESTAMP WITH TIME ZONE DEFAULT now(), "deleted_at" TIMESTAMP WITH TIME ZONE, "from_user_id" integer, "to_user_id" integer NOT NULL, "title" character varying NOT NULL, "content" character varying NOT NULL, "is_read" boolean NOT NULL DEFAULT false, "department_id" integer NOT NULL, CONSTRAINT "PK_6a72c3c0f683f6462415e653c3a" PRIMARY KEY ("id"))`
        );
        await queryRunner.query(
            `ALTER TABLE "notifications" ADD CONSTRAINT "FK_19629e8eb1e6023c4c73e661c82" FOREIGN KEY ("created_by") REFERENCES "users"("id") ON DELETE RESTRICT ON UPDATE NO ACTION`
        );
        await queryRunner.query(
            `ALTER TABLE "notifications" ADD CONSTRAINT "FK_e0517903116b233d60423efa296" FOREIGN KEY ("updated_by") REFERENCES "users"("id") ON DELETE RESTRICT ON UPDATE NO ACTION`
        );
        await queryRunner.query(
            `ALTER TABLE "notifications" ADD CONSTRAINT "FK_7ba5465905330f0ee176ebc3b16" FOREIGN KEY ("deleted_by") REFERENCES "users"("id") ON DELETE RESTRICT ON UPDATE NO ACTION`
        );
        await queryRunner.query(
            `ALTER TABLE "notifications" ADD CONSTRAINT "FK_44842273b97d2094bc2e3a31d4a" FOREIGN KEY ("from_user_id") REFERENCES "users"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`
        );
        await queryRunner.query(
            `ALTER TABLE "notifications" ADD CONSTRAINT "FK_a28a6b5c3c0f3cedfef35d6d6bf" FOREIGN KEY ("to_user_id") REFERENCES "users"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`
        );
        await queryRunner.query(
            `ALTER TABLE "notifications" ADD CONSTRAINT "FK_bbc25a84e379ec7d80ada3d0dcb" FOREIGN KEY ("department_id") REFERENCES "departments"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`
        );
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "notifications" DROP CONSTRAINT "FK_bbc25a84e379ec7d80ada3d0dcb"`);
        await queryRunner.query(`ALTER TABLE "notifications" DROP CONSTRAINT "FK_a28a6b5c3c0f3cedfef35d6d6bf"`);
        await queryRunner.query(`ALTER TABLE "notifications" DROP CONSTRAINT "FK_44842273b97d2094bc2e3a31d4a"`);
        await queryRunner.query(`ALTER TABLE "notifications" DROP CONSTRAINT "FK_7ba5465905330f0ee176ebc3b16"`);
        await queryRunner.query(`ALTER TABLE "notifications" DROP CONSTRAINT "FK_e0517903116b233d60423efa296"`);
        await queryRunner.query(`ALTER TABLE "notifications" DROP CONSTRAINT "FK_19629e8eb1e6023c4c73e661c82"`);
        await queryRunner.query(`DROP TABLE "notifications"`);
    }
}
