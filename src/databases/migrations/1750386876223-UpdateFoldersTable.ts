import { MigrationInterface, QueryRunner } from 'typeorm';

export class UpdateFoldersTable1750386876223 implements MigrationInterface {
    name = 'UpdateFoldersTable1750386876223';

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "folders" ADD "is_newsroom" boolean NOT NULL DEFAULT false`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "folders" DROP COLUMN "is_newsroom"`);
    }
}
