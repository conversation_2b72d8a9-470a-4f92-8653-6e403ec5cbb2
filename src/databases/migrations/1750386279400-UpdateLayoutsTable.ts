import { MigrationInterface, QueryRunner } from 'typeorm';

export class UpdateLayoutsTable1750386279400 implements MigrationInterface {
    name = 'UpdateLayoutsTable1750386279400';

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "layouts" ADD "is_default" boolean NOT NULL DEFAULT false`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "layouts" DROP COLUMN "is_default"`);
    }
}
