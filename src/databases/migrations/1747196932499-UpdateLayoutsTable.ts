import { MigrationInterface, QueryRunner } from 'typeorm';

export class UpdateLayoutsTable1747196932499 implements MigrationInterface {
    name = 'UpdateLayoutsTable1747196932499';

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "layouts" ADD "department_id" integer NOT NULL`);
        await queryRunner.query(
            `ALTER TABLE "layouts" ADD CONSTRAINT "FK_4758f3cd22a682cf86db5ad0363" FOREIGN KEY ("department_id") REFERENCES "departments"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`
        );
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "layouts" DROP CONSTRAINT "FK_4758f3cd22a682cf86db5ad0363"`);
        await queryRunner.query(`ALTER TABLE "layouts" DROP COLUMN "department_id"`);
    }
}
