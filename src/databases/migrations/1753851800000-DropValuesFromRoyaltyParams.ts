import { MigrationInterface, QueryRunner } from 'typeorm';

export class DropValuesFromRoyaltyParams1753851800000 implements MigrationInterface {
    name = 'DropValuesFromRoyaltyParams1753851800000';

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "royalty_params" DROP COLUMN "values"`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "royalty_params" ADD "values" json NOT NULL`);
    }
}
