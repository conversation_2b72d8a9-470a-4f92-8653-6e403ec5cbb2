import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddPageSizeIdToPressPublications1752738123663 implements MigrationInterface {
    name = 'AddPageSizeIdToPressPublications1752738123663';

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "press_publications" ADD "page_size_id" smallint`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "press_publications" DROP COLUMN "page_size_id"`);
    }
}
