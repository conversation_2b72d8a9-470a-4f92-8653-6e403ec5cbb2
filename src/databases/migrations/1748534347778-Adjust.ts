import { MigrationInterface, QueryRunner } from 'typeorm';

export class Adjust1748534347778 implements MigrationInterface {
    name = 'Adjust1748534347778';

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "login_devices" DROP CONSTRAINT "FK_7d0c295b73018ba4d44230acdb0"`);
        await queryRunner.query(`ALTER TABLE "login_devices" DROP COLUMN "userId"`);
        await queryRunner.query(`ALTER TABLE "articles" ALTER COLUMN "is_sync" SET NOT NULL`);
        await queryRunner.query(
            `ALTER TABLE "login_devices" ADD CONSTRAINT "FK_b51e4b6054bd64fb7d5c4983eb0" FOREIGN KEY ("user_id") REFERENCES "users"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`
        );
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "login_devices" DROP CONSTRAINT "FK_b51e4b6054bd64fb7d5c4983eb0"`);
        await queryRunner.query(`ALTER TABLE "articles" ALTER COLUMN "is_sync" DROP NOT NULL`);
        await queryRunner.query(`ALTER TABLE "login_devices" ADD "userId" integer`);
        await queryRunner.query(
            `ALTER TABLE "login_devices" ADD CONSTRAINT "FK_7d0c295b73018ba4d44230acdb0" FOREIGN KEY ("userId") REFERENCES "users"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`
        );
    }
}
