import { MigrationInterface, QueryRunner } from 'typeorm';

export class CreatePseudonymsTable1745833811691 implements MigrationInterface {
    name = 'CreatePseudonymsTable1745833811691';

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(
            `CREATE TABLE "pseudonyms" ("id" SERIAL NOT NULL, "created_by" integer, "updated_by" integer, "deleted_by" integer, "created_at" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(), "updated_at" TIMESTAMP WITH TIME ZONE DEFAULT now(), "deleted_at" TIMESTAMP WITH TIME ZONE, "name" character varying NOT NULL, "status_id" smallint NOT NULL, "is_default" boolean NOT NULL DEFAULT false, "user_id" integer NOT NULL, CONSTRAINT "PK_2206f97076b35cb2c644e26949f" PRIMARY KEY ("id"))`
        );
        await queryRunner.query(
            `ALTER TABLE "pseudonyms" ADD CONSTRAINT "FK_0ddc28c3455dbf88e3153474dbf" FOREIGN KEY ("created_by") REFERENCES "users"("id") ON DELETE RESTRICT ON UPDATE NO ACTION`
        );
        await queryRunner.query(
            `ALTER TABLE "pseudonyms" ADD CONSTRAINT "FK_c707b88a4ba2da84877bb871655" FOREIGN KEY ("updated_by") REFERENCES "users"("id") ON DELETE RESTRICT ON UPDATE NO ACTION`
        );
        await queryRunner.query(
            `ALTER TABLE "pseudonyms" ADD CONSTRAINT "FK_7ccbd1cf741f5ee7c066e94bf1b" FOREIGN KEY ("deleted_by") REFERENCES "users"("id") ON DELETE RESTRICT ON UPDATE NO ACTION`
        );
        await queryRunner.query(
            `ALTER TABLE "pseudonyms" ADD CONSTRAINT "FK_cbb45da82eabc51ebf1d407cac8" FOREIGN KEY ("user_id") REFERENCES "users"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`
        );
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "pseudonyms" DROP CONSTRAINT "FK_cbb45da82eabc51ebf1d407cac8"`);
        await queryRunner.query(`ALTER TABLE "pseudonyms" DROP CONSTRAINT "FK_7ccbd1cf741f5ee7c066e94bf1b"`);
        await queryRunner.query(`ALTER TABLE "pseudonyms" DROP CONSTRAINT "FK_c707b88a4ba2da84877bb871655"`);
        await queryRunner.query(`ALTER TABLE "pseudonyms" DROP CONSTRAINT "FK_0ddc28c3455dbf88e3153474dbf"`);
        await queryRunner.query(`DROP TABLE "pseudonyms"`);
    }
}
