import { MigrationInterface, QueryRunner } from 'typeorm';

export class UpdateGroupEntity1748536082712 implements MigrationInterface {
    name = 'UpdateGroupEntity1748536082712';

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "groups" ADD "parent_id" integer`);
        await queryRunner.query(`ALTER TABLE "groups" ADD "desc" character varying`);
        await queryRunner.query(`ALTER TABLE "groups" ADD "type_id" integer DEFAULT '0'`);
        await queryRunner.query(
            `ALTER TABLE "groups" ADD CONSTRAINT "FK_d768ea35a407c2ba9c0b038b613" FOREIGN KEY ("parent_id") REFERENCES "groups"("id") ON DELETE CASCADE ON UPDATE NO ACTION`
        );
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "groups" DROP CONSTRAINT "FK_d768ea35a407c2ba9c0b038b613"`);
        await queryRunner.query(`ALTER TABLE "groups" DROP COLUMN "type_id"`);
        await queryRunner.query(`ALTER TABLE "groups" DROP COLUMN "desc"`);
        await queryRunner.query(`ALTER TABLE "groups" DROP COLUMN "parent_id"`);
    }
}
