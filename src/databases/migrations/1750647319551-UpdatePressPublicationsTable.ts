import { MigrationInterface, QueryRunner } from 'typeorm';

export class UpdatePressPublicationsTable1750647319551 implements MigrationInterface {
    name = 'UpdatePressPublicationsTable1750647319551';

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "press_publications" ADD "page_count" smallint NOT NULL DEFAULT '0'`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "press_publications" DROP COLUMN "page_count"`);
    }
}
