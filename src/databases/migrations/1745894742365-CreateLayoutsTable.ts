import { MigrationInterface, QueryRunner } from 'typeorm';

export class CreateLayoutsTable1745894742365 implements MigrationInterface {
    name = 'CreateLayoutsTable1745894742365';

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(
            `CREATE TABLE "layouts" ("id" SERIAL NOT NULL, "created_by" integer, "updated_by" integer, "deleted_by" integer, "created_at" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(), "updated_at" TIMESTAMP WITH TIME ZONE DEFAULT now(), "deleted_at" TIMESTAMP WITH TIME ZONE, "name" character varying NOT NULL, "desc" character varying, "content" text NOT NULL, "status_id" smallint NOT NULL, "layout_type_id" smallint NOT NULL, CONSTRAINT "PK_f2331b0ed523c5c349652fffc7e" PRIMARY KEY ("id"))`
        );
        await queryRunner.query(
            `ALTER TABLE "layouts" ADD CONSTRAINT "FK_171c2d1800b740f2c64e8d3c2c8" FOREIGN KEY ("created_by") REFERENCES "users"("id") ON DELETE RESTRICT ON UPDATE NO ACTION`
        );
        await queryRunner.query(
            `ALTER TABLE "layouts" ADD CONSTRAINT "FK_b7c71d4401e0919f9cc323f38c5" FOREIGN KEY ("updated_by") REFERENCES "users"("id") ON DELETE RESTRICT ON UPDATE NO ACTION`
        );
        await queryRunner.query(
            `ALTER TABLE "layouts" ADD CONSTRAINT "FK_c3434bd73269d92ebb8fbee0718" FOREIGN KEY ("deleted_by") REFERENCES "users"("id") ON DELETE RESTRICT ON UPDATE NO ACTION`
        );
        await queryRunner.query(
            `ALTER TABLE "categories" ADD CONSTRAINT "FK_a68e0427b1638747fa2110dd67d" FOREIGN KEY ("layout_id") REFERENCES "layouts"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`
        );
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "categories" DROP CONSTRAINT "FK_a68e0427b1638747fa2110dd67d"`);
        await queryRunner.query(`ALTER TABLE "layouts" DROP CONSTRAINT "FK_c3434bd73269d92ebb8fbee0718"`);
        await queryRunner.query(`ALTER TABLE "layouts" DROP CONSTRAINT "FK_b7c71d4401e0919f9cc323f38c5"`);
        await queryRunner.query(`ALTER TABLE "layouts" DROP CONSTRAINT "FK_171c2d1800b740f2c64e8d3c2c8"`);
        await queryRunner.query(`DROP TABLE "layouts"`);
    }
}
