import { MigrationInterface, QueryRunner } from 'typeorm';

export class UpdateConfigsTable1750321647051 implements MigrationInterface {
    name = 'UpdateConfigsTable1750321647051';

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "configs" ADD "config_type_id" smallint NOT NULL DEFAULT '1'`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "configs" DROP COLUMN "config_type_id"`);
    }
}
