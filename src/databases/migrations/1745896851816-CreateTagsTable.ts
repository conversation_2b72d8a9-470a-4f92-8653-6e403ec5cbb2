import { MigrationInterface, QueryRunner } from 'typeorm';

export class CreateTagsTable1745896851816 implements MigrationInterface {
    name = 'CreateTagsTable1745896851816';

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(
            `CREATE TABLE "tags" ("id" SERIAL NOT NULL, "created_by" integer, "updated_by" integer, "deleted_by" integer, "created_at" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(), "updated_at" TIMESTAMP WITH TIME ZONE DEFAULT now(), "deleted_at" TIMESTAMP WITH TIME ZONE, "name" character varying NOT NULL, "slug" character varying NOT NULL, "status_id" smallint NOT NULL, "language_id" smallint NOT NULL, "department_id" integer NOT NULL, CONSTRAINT "PK_e7dc17249a1148a1970748eda99" PRIMARY KEY ("id"))`
        );
        await queryRunner.query(
            `ALTER TABLE "tags" ADD CONSTRAINT "FK_32f027a90ce9c91c9b8ff830d22" FOREIGN KEY ("created_by") REFERENCES "users"("id") ON DELETE RESTRICT ON UPDATE NO ACTION`
        );
        await queryRunner.query(
            `ALTER TABLE "tags" ADD CONSTRAINT "FK_f670cc12e5c02adf1b4c86ee3e0" FOREIGN KEY ("updated_by") REFERENCES "users"("id") ON DELETE RESTRICT ON UPDATE NO ACTION`
        );
        await queryRunner.query(
            `ALTER TABLE "tags" ADD CONSTRAINT "FK_7444a7c7e1029888bcf5e3ef8ea" FOREIGN KEY ("deleted_by") REFERENCES "users"("id") ON DELETE RESTRICT ON UPDATE NO ACTION`
        );
        await queryRunner.query(
            `ALTER TABLE "tags" ADD CONSTRAINT "FK_ad2060d86e9d99bbb4cd05afcee" FOREIGN KEY ("department_id") REFERENCES "departments"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`
        );
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "tags" DROP CONSTRAINT "FK_ad2060d86e9d99bbb4cd05afcee"`);
        await queryRunner.query(`ALTER TABLE "tags" DROP CONSTRAINT "FK_7444a7c7e1029888bcf5e3ef8ea"`);
        await queryRunner.query(`ALTER TABLE "tags" DROP CONSTRAINT "FK_f670cc12e5c02adf1b4c86ee3e0"`);
        await queryRunner.query(`ALTER TABLE "tags" DROP CONSTRAINT "FK_32f027a90ce9c91c9b8ff830d22"`);
        await queryRunner.query(`DROP TABLE "tags"`);
    }
}
