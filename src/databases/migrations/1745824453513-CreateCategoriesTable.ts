import { MigrationInterface, QueryRunner } from 'typeorm';

export class CreateCategoriesTable1745824453513 implements MigrationInterface {
    name = 'CreateCategoriesTable1745824453513';

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(
            `CREATE TABLE "categories" ("id" SERIAL NOT NULL, "created_by" integer, "updated_by" integer, "deleted_by" integer, "created_at" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(), "updated_at" TIMESTAMP WITH TIME ZONE DEFAULT now(), "deleted_at" TIMESTAMP WITH TIME ZONE, "name" character varying NOT NULL, "status_id" smallint NOT NULL, "desc" character varying, "article_type_id" smallint NOT NULL, "category_type_id" smallint NOT NULL, "language_id" smallint NOT NULL, "department_id" integer NOT NULL, "parent_id" integer, "display_order" smallint NOT NULL, CONSTRAINT "PK_24dbc6126a28ff948da33e97d3b" PRIMARY KEY ("id"))`
        );
        await queryRunner.query(
            `ALTER TABLE "categories" ADD CONSTRAINT "FK_23ad9291e0e22cdf46ae7ec5461" FOREIGN KEY ("created_by") REFERENCES "users"("id") ON DELETE RESTRICT ON UPDATE NO ACTION`
        );
        await queryRunner.query(
            `ALTER TABLE "categories" ADD CONSTRAINT "FK_971f81500b65c577edd00dd2687" FOREIGN KEY ("updated_by") REFERENCES "users"("id") ON DELETE RESTRICT ON UPDATE NO ACTION`
        );
        await queryRunner.query(
            `ALTER TABLE "categories" ADD CONSTRAINT "FK_5f3e42e64bd478c794584ca1cf3" FOREIGN KEY ("deleted_by") REFERENCES "users"("id") ON DELETE RESTRICT ON UPDATE NO ACTION`
        );
        await queryRunner.query(
            `ALTER TABLE "categories" ADD CONSTRAINT "FK_61c512b80008be608f1b4b34831" FOREIGN KEY ("department_id") REFERENCES "departments"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`
        );
        await queryRunner.query(
            `ALTER TABLE "categories" ADD CONSTRAINT "FK_88cea2dc9c31951d06437879b40" FOREIGN KEY ("parent_id") REFERENCES "categories"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`
        );
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "categories" DROP CONSTRAINT "FK_88cea2dc9c31951d06437879b40"`);
        await queryRunner.query(`ALTER TABLE "categories" DROP CONSTRAINT "FK_61c512b80008be608f1b4b34831"`);
        await queryRunner.query(`ALTER TABLE "categories" DROP CONSTRAINT "FK_5f3e42e64bd478c794584ca1cf3"`);
        await queryRunner.query(`ALTER TABLE "categories" DROP CONSTRAINT "FK_971f81500b65c577edd00dd2687"`);
        await queryRunner.query(`ALTER TABLE "categories" DROP CONSTRAINT "FK_23ad9291e0e22cdf46ae7ec5461"`);
        await queryRunner.query(`DROP TABLE "categories"`);
    }
}
