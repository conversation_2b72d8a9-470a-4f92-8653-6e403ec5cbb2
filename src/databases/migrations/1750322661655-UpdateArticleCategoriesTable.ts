import { MigrationInterface, QueryRunner } from 'typeorm';

export class UpdateArticleCategoriesTable1750322661655 implements MigrationInterface {
    name = 'UpdateArticleCategoriesTable1750322661655';

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "article_categories" ADD "is_major" boolean NOT NULL DEFAULT false`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "article_categories" DROP COLUMN "is_major"`);
    }
}
