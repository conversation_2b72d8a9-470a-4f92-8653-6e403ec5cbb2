import { MigrationInterface, QueryRunner } from 'typeorm';

export class UpdateArticlesTable1751342428753 implements MigrationInterface {
    name = 'UpdateArticlesTable1751342428753';

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "articles" ADD "typesetting_status_id" smallint`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "articles" DROP COLUMN "typesetting_status_id"`);
    }
}
