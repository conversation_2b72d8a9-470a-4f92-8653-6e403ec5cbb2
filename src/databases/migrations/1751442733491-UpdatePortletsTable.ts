import { MigrationInterface, QueryRunner } from 'typeorm';

export class UpdatePortletsTable1751442733491 implements MigrationInterface {
    name = 'UpdatePortletsTable1751442733491';

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "portlets" ADD "is_client" boolean NOT NULL DEFAULT false`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "portlets" DROP COLUMN "is_client"`);
    }
}
