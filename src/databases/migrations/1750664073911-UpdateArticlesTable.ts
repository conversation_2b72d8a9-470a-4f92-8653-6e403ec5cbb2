import { MigrationInterface, QueryRunner } from 'typeorm';

export class UpdateArticlesTable1750664073911 implements MigrationInterface {
    name = 'UpdateArticlesTable1750664073911';

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "articles" ADD "press_publication_id" integer`);
        await queryRunner.query(`ALTER TABLE "articles" ADD "issue_id" integer`);
        await queryRunner.query(`ALTER TABLE "articles" ADD "issue_page_id" integer`);
        await queryRunner.query(
            `ALTER TABLE "articles" ADD CONSTRAINT "FK_3c1a2a8826ac83dee1a69b98756" FOREIGN KEY ("press_publication_id") REFERENCES "press_publications"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`
        );
        await queryRunner.query(
            `ALTER TABLE "articles" ADD CONSTRAINT "FK_da07782815f05eec48e2cf6c026" FOREIGN KEY ("issue_id") REFERENCES "issues"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`
        );
        await queryRunner.query(
            `ALTER TABLE "articles" ADD CONSTRAINT "FK_1df99adda643979e7f448223b51" FOREIGN KEY ("issue_page_id") REFERENCES "issue_pages"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`
        );
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "articles" DROP CONSTRAINT "FK_1df99adda643979e7f448223b51"`);
        await queryRunner.query(`ALTER TABLE "articles" DROP CONSTRAINT "FK_da07782815f05eec48e2cf6c026"`);
        await queryRunner.query(`ALTER TABLE "articles" DROP CONSTRAINT "FK_3c1a2a8826ac83dee1a69b98756"`);
        await queryRunner.query(`ALTER TABLE "articles" DROP COLUMN "issue_page_id"`);
        await queryRunner.query(`ALTER TABLE "articles" DROP COLUMN "issue_id"`);
        await queryRunner.query(`ALTER TABLE "articles" DROP COLUMN "press_publication_id"`);
    }
}
