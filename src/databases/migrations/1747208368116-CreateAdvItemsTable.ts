import { MigrationInterface, QueryRunner } from 'typeorm';

export class CreateAdvItemsTable1747208368116 implements MigrationInterface {
    name = 'CreateAdvItemsTable1747208368116';

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(
            `CREATE TABLE "adv_items" ("id" SERIAL NOT NULL, "created_by" integer, "updated_by" integer, "deleted_by" integer, "created_at" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(), "updated_at" TIMESTAMP WITH TIME ZONE DEFAULT now(), "deleted_at" TIMESTAMP WITH TIME ZONE, "name" character varying NOT NULL, "desc" character varying, "content" character varying NOT NULL, "type_id" smallint NOT NULL, "file_id" integer, "status_id" smallint NOT NULL, "width" smallint NOT NULL, "height" smallint NOT NULL, "start_date" date NOT NULL, "end_date" date NOT NULL, "department_id" integer NOT NULL, CONSTRAINT "REL_d9461d56799a9a935afc88cec3" UNIQUE ("file_id"), CONSTRAINT "PK_cc3361a47451ceebda648e88e4d" PRIMARY KEY ("id"))`
        );
        await queryRunner.query(
            `CREATE TABLE "advertise_adv_items" ("id" SERIAL NOT NULL, "created_by" integer, "updated_by" integer, "deleted_by" integer, "created_at" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(), "updated_at" TIMESTAMP WITH TIME ZONE DEFAULT now(), "deleted_at" TIMESTAMP WITH TIME ZONE, "advertise_id" integer NOT NULL, "adv_item_id" integer NOT NULL, "display_order" smallint NOT NULL, CONSTRAINT "PK_e61efa97bc9f3060d6e11838300" PRIMARY KEY ("id"))`
        );
        await queryRunner.query(
            `CREATE TABLE "advertise_categories" ("advertise_id" integer NOT NULL, "category_id" integer NOT NULL, CONSTRAINT "PK_817c7b4f82d701a8d6469fbd82e" PRIMARY KEY ("advertise_id", "category_id"))`
        );
        await queryRunner.query(
            `CREATE INDEX "IDX_eecbd4e44db73429d457549b46" ON "advertise_categories" ("advertise_id") `
        );
        await queryRunner.query(
            `CREATE INDEX "IDX_e3f955fa104970ab0c4fa7ef28" ON "advertise_categories" ("category_id") `
        );
        await queryRunner.query(
            `ALTER TABLE "adv_items" ADD CONSTRAINT "FK_1faafb1fc8055058bad051ceb51" FOREIGN KEY ("created_by") REFERENCES "users"("id") ON DELETE RESTRICT ON UPDATE NO ACTION`
        );
        await queryRunner.query(
            `ALTER TABLE "adv_items" ADD CONSTRAINT "FK_47aedcf2f4e0bc732265b399dad" FOREIGN KEY ("updated_by") REFERENCES "users"("id") ON DELETE RESTRICT ON UPDATE NO ACTION`
        );
        await queryRunner.query(
            `ALTER TABLE "adv_items" ADD CONSTRAINT "FK_1fe38cb3886902b4699a796000f" FOREIGN KEY ("deleted_by") REFERENCES "users"("id") ON DELETE RESTRICT ON UPDATE NO ACTION`
        );
        await queryRunner.query(
            `ALTER TABLE "adv_items" ADD CONSTRAINT "FK_4d94d440ed2680035e4aa450d82" FOREIGN KEY ("department_id") REFERENCES "departments"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`
        );
        await queryRunner.query(
            `ALTER TABLE "adv_items" ADD CONSTRAINT "FK_d9461d56799a9a935afc88cec3f" FOREIGN KEY ("file_id") REFERENCES "files"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`
        );
        await queryRunner.query(
            `ALTER TABLE "advertise_adv_items" ADD CONSTRAINT "FK_a326bc61eef73e122961b4c20af" FOREIGN KEY ("created_by") REFERENCES "users"("id") ON DELETE RESTRICT ON UPDATE NO ACTION`
        );
        await queryRunner.query(
            `ALTER TABLE "advertise_adv_items" ADD CONSTRAINT "FK_824851467af890dd21c37a97062" FOREIGN KEY ("updated_by") REFERENCES "users"("id") ON DELETE RESTRICT ON UPDATE NO ACTION`
        );
        await queryRunner.query(
            `ALTER TABLE "advertise_adv_items" ADD CONSTRAINT "FK_664e56a53bd08cbc8ccc6a9341c" FOREIGN KEY ("deleted_by") REFERENCES "users"("id") ON DELETE RESTRICT ON UPDATE NO ACTION`
        );
        await queryRunner.query(
            `ALTER TABLE "advertise_adv_items" ADD CONSTRAINT "FK_48cbe55ba3667ad74d60d096846" FOREIGN KEY ("advertise_id") REFERENCES "advertises"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`
        );
        await queryRunner.query(
            `ALTER TABLE "advertise_adv_items" ADD CONSTRAINT "FK_9370772cbfe448fe04d616fb772" FOREIGN KEY ("adv_item_id") REFERENCES "adv_items"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`
        );
        await queryRunner.query(
            `ALTER TABLE "advertise_categories" ADD CONSTRAINT "FK_eecbd4e44db73429d457549b467" FOREIGN KEY ("advertise_id") REFERENCES "advertises"("id") ON DELETE CASCADE ON UPDATE CASCADE`
        );
        await queryRunner.query(
            `ALTER TABLE "advertise_categories" ADD CONSTRAINT "FK_e3f955fa104970ab0c4fa7ef288" FOREIGN KEY ("category_id") REFERENCES "categories"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`
        );
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "advertise_categories" DROP CONSTRAINT "FK_e3f955fa104970ab0c4fa7ef288"`);
        await queryRunner.query(`ALTER TABLE "advertise_categories" DROP CONSTRAINT "FK_eecbd4e44db73429d457549b467"`);
        await queryRunner.query(`ALTER TABLE "advertise_adv_items" DROP CONSTRAINT "FK_9370772cbfe448fe04d616fb772"`);
        await queryRunner.query(`ALTER TABLE "advertise_adv_items" DROP CONSTRAINT "FK_48cbe55ba3667ad74d60d096846"`);
        await queryRunner.query(`ALTER TABLE "advertise_adv_items" DROP CONSTRAINT "FK_664e56a53bd08cbc8ccc6a9341c"`);
        await queryRunner.query(`ALTER TABLE "advertise_adv_items" DROP CONSTRAINT "FK_824851467af890dd21c37a97062"`);
        await queryRunner.query(`ALTER TABLE "advertise_adv_items" DROP CONSTRAINT "FK_a326bc61eef73e122961b4c20af"`);
        await queryRunner.query(`ALTER TABLE "adv_items" DROP CONSTRAINT "FK_d9461d56799a9a935afc88cec3f"`);
        await queryRunner.query(`ALTER TABLE "adv_items" DROP CONSTRAINT "FK_4d94d440ed2680035e4aa450d82"`);
        await queryRunner.query(`ALTER TABLE "adv_items" DROP CONSTRAINT "FK_1fe38cb3886902b4699a796000f"`);
        await queryRunner.query(`ALTER TABLE "adv_items" DROP CONSTRAINT "FK_47aedcf2f4e0bc732265b399dad"`);
        await queryRunner.query(`ALTER TABLE "adv_items" DROP CONSTRAINT "FK_1faafb1fc8055058bad051ceb51"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_e3f955fa104970ab0c4fa7ef28"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_eecbd4e44db73429d457549b46"`);
        await queryRunner.query(`DROP TABLE "advertise_categories"`);
        await queryRunner.query(`DROP TABLE "advertise_adv_items"`);
        await queryRunner.query(`DROP TABLE "adv_items"`);
    }
}
