import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddRoyaltyFieldsToArticleFiles1752738123666 implements MigrationInterface {
    name = 'AddRoyaltyFieldsToArticleFiles1752738123666';

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "article_files" ADD "is_royalty" boolean NOT NULL DEFAULT false`);
        await queryRunner.query(`ALTER TABLE "article_files" ADD "author_id" integer`);
        await queryRunner.query(`ALTER TABLE "article_files" ADD "royalty_cost" integer`);
        await queryRunner.query(
            `ALTER TABLE "article_files" ADD CONSTRAINT "FK_article_files_author_id" FOREIGN KEY ("author_id") REFERENCES "users"("id") ON DELETE SET NULL ON UPDATE NO ACTION`
        );
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "article_files" DROP CONSTRAINT "FK_article_files_author_id"`);
        await queryRunner.query(`ALTER TABLE "article_files" DROP COLUMN "royalty_cost"`);
        await queryRunner.query(`ALTER TABLE "article_files" DROP COLUMN "author_id"`);
        await queryRunner.query(`ALTER TABLE "article_files" DROP COLUMN "is_royalty"`);
    }
}
