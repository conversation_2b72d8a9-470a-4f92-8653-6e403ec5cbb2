import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddLoginDevice1748533961002 implements MigrationInterface {
    name = 'AddLoginDevice1748533961002';

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(
            `CREATE TABLE "login_devices" ("id" SERIAL NOT NULL, "created_by" integer, "updated_by" integer, "deleted_by" integer, "created_at" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(), "updated_at" TIMESTAMP WITH TIME ZONE DEFAULT now(), "deleted_at" TIMESTAMP WITH TIME ZONE, "user_id" integer NOT NULL, "ip_address" character varying NOT NULL, "platform" character varying NOT NULL, "os" character varying NOT NULL, "browser" character varying NOT NULL, "user_agent" character varying NOT NULL, "raw" json NOT NULL, "userId" integer, CONSTRAINT "UQ_d2819e8f1fb95428677c833bd9e" UNIQUE ("user_id", "user_agent"), CONSTRAINT "PK_bc16579b5e7e6cb70759302fbfc" PRIMARY KEY ("id"))`
        );
        await queryRunner.query(`ALTER TABLE "articles" ALTER COLUMN "is_sync" SET NOT NULL`);
        await queryRunner.query(
            `ALTER TABLE "login_devices" ADD CONSTRAINT "FK_9903d51f902623cda5e318214f1" FOREIGN KEY ("created_by") REFERENCES "users"("id") ON DELETE RESTRICT ON UPDATE NO ACTION`
        );
        await queryRunner.query(
            `ALTER TABLE "login_devices" ADD CONSTRAINT "FK_86fb15833a304999a64d180dbc0" FOREIGN KEY ("updated_by") REFERENCES "users"("id") ON DELETE RESTRICT ON UPDATE NO ACTION`
        );
        await queryRunner.query(
            `ALTER TABLE "login_devices" ADD CONSTRAINT "FK_1343263da577405df8bd0cfa84d" FOREIGN KEY ("deleted_by") REFERENCES "users"("id") ON DELETE RESTRICT ON UPDATE NO ACTION`
        );
        await queryRunner.query(
            `ALTER TABLE "login_devices" ADD CONSTRAINT "FK_7d0c295b73018ba4d44230acdb0" FOREIGN KEY ("userId") REFERENCES "users"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`
        );
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "login_devices" DROP CONSTRAINT "FK_7d0c295b73018ba4d44230acdb0"`);
        await queryRunner.query(`ALTER TABLE "login_devices" DROP CONSTRAINT "FK_1343263da577405df8bd0cfa84d"`);
        await queryRunner.query(`ALTER TABLE "login_devices" DROP CONSTRAINT "FK_86fb15833a304999a64d180dbc0"`);
        await queryRunner.query(`ALTER TABLE "login_devices" DROP CONSTRAINT "FK_9903d51f902623cda5e318214f1"`);
        await queryRunner.query(`ALTER TABLE "articles" ALTER COLUMN "is_sync" DROP NOT NULL`);
        await queryRunner.query(`DROP TABLE "login_devices"`);
    }
}
