import { MigrationInterface, QueryRunner } from 'typeorm';

export class CreateAdvertisesTable1746762631392 implements MigrationInterface {
    name = 'CreateAdvertisesTable1746762631392';

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(
            `CREATE TABLE "advertises" ("id" SERIAL NOT NULL, "created_by" integer, "updated_by" integer, "deleted_by" integer, "created_at" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(), "updated_at" TIMESTAMP WITH TIME ZONE DEFAULT now(), "deleted_at" TIMESTAMP WITH TIME ZONE, "code" character varying NOT NULL, "name" character varying NOT NULL, "desc" character varying, "type_id" smallint NOT NULL, "speed" smallint NOT NULL, "display_type_id" smallint NOT NULL, "status_id" smallint NOT NULL, "department_id" integer NOT NULL, CONSTRAINT "PK_eb3c682fb6f1f3f6305989419fe" PRIMARY KEY ("id"))`
        );
        await queryRunner.query(
            `ALTER TABLE "advertises" ADD CONSTRAINT "FK_fffdaca7867646166fd161b4e90" FOREIGN KEY ("created_by") REFERENCES "users"("id") ON DELETE RESTRICT ON UPDATE NO ACTION`
        );
        await queryRunner.query(
            `ALTER TABLE "advertises" ADD CONSTRAINT "FK_a9591e81155350bbfed80afdbd5" FOREIGN KEY ("updated_by") REFERENCES "users"("id") ON DELETE RESTRICT ON UPDATE NO ACTION`
        );
        await queryRunner.query(
            `ALTER TABLE "advertises" ADD CONSTRAINT "FK_bbcf97bbc021532f188671a41ef" FOREIGN KEY ("deleted_by") REFERENCES "users"("id") ON DELETE RESTRICT ON UPDATE NO ACTION`
        );
        await queryRunner.query(
            `ALTER TABLE "advertises" ADD CONSTRAINT "FK_d351aecc7d13f81ec04d9ed9d2b" FOREIGN KEY ("department_id") REFERENCES "departments"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`
        );
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "advertises" DROP CONSTRAINT "FK_d351aecc7d13f81ec04d9ed9d2b"`);
        await queryRunner.query(`ALTER TABLE "advertises" DROP CONSTRAINT "FK_bbcf97bbc021532f188671a41ef"`);
        await queryRunner.query(`ALTER TABLE "advertises" DROP CONSTRAINT "FK_a9591e81155350bbfed80afdbd5"`);
        await queryRunner.query(`ALTER TABLE "advertises" DROP CONSTRAINT "FK_fffdaca7867646166fd161b4e90"`);
        await queryRunner.query(`DROP TABLE "advertises"`);
    }
}
