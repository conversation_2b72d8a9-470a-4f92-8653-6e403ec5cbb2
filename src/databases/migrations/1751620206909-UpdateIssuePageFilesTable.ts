import { MigrationInterface, QueryRunner } from 'typeorm';
import { ItemStatus } from '../../commons/enums.common';

export class UpdateIssuePageFilesTable1751620206909 implements MigrationInterface {
    name = 'UpdateIssuePageFilesTable1751620206909';

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(
            `ALTER TABLE "issue_page_files" ADD "status_id" smallint NOT NULL DEFAULT '${ItemStatus.PENDING}'`
        );
        await queryRunner.query(`ALTER TABLE "issue_page_files" ADD "name" character varying`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "issue_page_files" DROP COLUMN "name"`);
        await queryRunner.query(`ALTER TABLE "issue_page_files" DROP COLUMN "status_id"`);
    }
}
