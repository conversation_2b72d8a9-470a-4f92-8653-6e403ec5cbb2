import { MigrationInterface, QueryRunner } from 'typeorm';

export class UpdateFilesTable1750320186194 implements MigrationInterface {
    name = 'UpdateFilesTable1750320186194';

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(
            `CREATE TABLE "file_tags" ("file_id" integer NOT NULL, "tag_id" integer NOT NULL, CONSTRAINT "PK_1f87c8da8bf4af5cf7671611b73" PRIMARY KEY ("file_id", "tag_id"))`
        );
        await queryRunner.query(`CREATE INDEX "IDX_f5b3ac1a9bc5f37ccf2d3d1334" ON "file_tags" ("file_id") `);
        await queryRunner.query(`CREATE INDEX "IDX_e835ce23c5762ac0e54e01b2d5" ON "file_tags" ("tag_id") `);
        await queryRunner.query(`ALTER TABLE "files" ADD "parent_id" integer`);
        await queryRunner.query(`ALTER TABLE "files" ADD "is_newsroom" boolean NOT NULL DEFAULT false`);
        await queryRunner.query(
            `ALTER TABLE "files" ADD CONSTRAINT "FK_3dbef7bd126cb8d3d1140c674a3" FOREIGN KEY ("parent_id") REFERENCES "files"("id") ON DELETE CASCADE ON UPDATE NO ACTION`
        );
        await queryRunner.query(
            `ALTER TABLE "file_tags" ADD CONSTRAINT "FK_f5b3ac1a9bc5f37ccf2d3d13341" FOREIGN KEY ("file_id") REFERENCES "files"("id") ON DELETE CASCADE ON UPDATE CASCADE`
        );
        await queryRunner.query(
            `ALTER TABLE "file_tags" ADD CONSTRAINT "FK_e835ce23c5762ac0e54e01b2d54" FOREIGN KEY ("tag_id") REFERENCES "tags"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`
        );
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "file_tags" DROP CONSTRAINT "FK_e835ce23c5762ac0e54e01b2d54"`);
        await queryRunner.query(`ALTER TABLE "file_tags" DROP CONSTRAINT "FK_f5b3ac1a9bc5f37ccf2d3d13341"`);
        await queryRunner.query(`ALTER TABLE "files" DROP CONSTRAINT "FK_3dbef7bd126cb8d3d1140c674a3"`);
        await queryRunner.query(`ALTER TABLE "files" DROP COLUMN "is_newsroom"`);
        await queryRunner.query(`ALTER TABLE "files" DROP COLUMN "parent_id"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_e835ce23c5762ac0e54e01b2d5"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_f5b3ac1a9bc5f37ccf2d3d1334"`);
        await queryRunner.query(`DROP TABLE "file_tags"`);
    }
}
