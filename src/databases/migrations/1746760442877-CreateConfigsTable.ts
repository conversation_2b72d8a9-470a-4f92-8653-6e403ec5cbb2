import { MigrationInterface, QueryRunner } from 'typeorm';

export class CreateConfigsTable1746760442877 implements MigrationInterface {
    name = 'CreateConfigsTable1746760442877';

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(
            `CREATE TABLE "configs" ("id" SERIAL NOT NULL, "created_by" integer, "updated_by" integer, "deleted_by" integer, "created_at" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(), "updated_at" TIMESTAMP WITH TIME ZONE DEFAULT now(), "deleted_at" TIMESTAMP WITH TIME ZONE, "code" character varying NOT NULL, "name" character varying NOT NULL, "content" character varying NOT NULL, "department_id" integer NOT NULL, CONSTRAINT "PK_002b633ec0d45f5c6f928fea292" PRIMARY KEY ("id"))`
        );
        await queryRunner.query(
            `ALTER TABLE "configs" ADD CONSTRAINT "FK_f1b4ee887ed7001a704c21e3ca4" FOREIGN KEY ("created_by") REFERENCES "users"("id") ON DELETE RESTRICT ON UPDATE NO ACTION`
        );
        await queryRunner.query(
            `ALTER TABLE "configs" ADD CONSTRAINT "FK_3538c09ac4b50f566843153ff80" FOREIGN KEY ("updated_by") REFERENCES "users"("id") ON DELETE RESTRICT ON UPDATE NO ACTION`
        );
        await queryRunner.query(
            `ALTER TABLE "configs" ADD CONSTRAINT "FK_b9a353331129e2fe27b9684c2f4" FOREIGN KEY ("deleted_by") REFERENCES "users"("id") ON DELETE RESTRICT ON UPDATE NO ACTION`
        );
        await queryRunner.query(
            `ALTER TABLE "configs" ADD CONSTRAINT "FK_2ba23ab8ca9222606adf26a63b3" FOREIGN KEY ("department_id") REFERENCES "departments"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`
        );
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "configs" DROP CONSTRAINT "FK_2ba23ab8ca9222606adf26a63b3"`);
        await queryRunner.query(`ALTER TABLE "configs" DROP CONSTRAINT "FK_b9a353331129e2fe27b9684c2f4"`);
        await queryRunner.query(`ALTER TABLE "configs" DROP CONSTRAINT "FK_3538c09ac4b50f566843153ff80"`);
        await queryRunner.query(`ALTER TABLE "configs" DROP CONSTRAINT "FK_f1b4ee887ed7001a704c21e3ca4"`);
        await queryRunner.query(`DROP TABLE "configs"`);
    }
}
