import { MigrationInterface, QueryRunner } from 'typeorm';

export class UpdateDBTable1747197362349 implements MigrationInterface {
    name = 'UpdateDBTable1747197362349';

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(
            `ALTER TABLE "workflow_permission_article_types" ADD "user_department_id" integer NOT NULL`
        );
        await queryRunner.query(
            `ALTER TABLE "workflow_permission_article_types" DROP CONSTRAINT "UQ_3b3d49cb576ebfb225fe3535a68"`
        );
        await queryRunner.query(
            `ALTER TABLE "workflow_permission_article_types" ADD CONSTRAINT "FK_5adf3c378b94f7fe89a4d12882e" FOREIGN KEY ("user_department_id") REFERENCES "user_departments"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`
        );
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(
            `ALTER TABLE "workflow_permission_article_types" DROP CONSTRAINT "FK_5adf3c378b94f7fe89a4d12882e"`
        );
        await queryRunner.query(
            `ALTER TABLE "workflow_permission_article_types" ADD CONSTRAINT "UQ_3b3d49cb576ebfb225fe3535a68" UNIQUE ("article_type_id")`
        );
        await queryRunner.query(`ALTER TABLE "workflow_permission_article_types" DROP COLUMN "user_department_id"`);
    }
}
