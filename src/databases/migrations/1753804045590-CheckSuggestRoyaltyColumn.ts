import { MigrationInterface, QueryRunner } from 'typeorm';

export class CheckSuggestRoyaltyColumn1753804045590 implements MigrationInterface {
    name = 'CheckSuggestRoyaltyColumn1753804045590';

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(
            `CREATE TABLE "royalty_param_options" ("id" SERIAL NOT NULL, "created_by" integer, "updated_by" integer, "deleted_by" integer, "created_at" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(), "updated_at" TIMESTAMP WITH TIME ZONE DEFAULT now(), "deleted_at" TIMESTAMP WITH TIME ZONE, "name" character varying NOT NULL, "value" character varying NOT NULL, "ratio" smallint NOT NULL, "display_order" smallint NOT NULL DEFAULT '0', "user_ids" json, "royalty_param_id" integer NOT NULL, CONSTRAINT "PK_8105c7d7b54f954b7061dd7e3cb" PRIMARY KEY ("id"))`
        );
        await queryRunner.query(`ALTER TABLE "article_royalty_users" ADD "suggest_royalty" integer DEFAULT '0'`);
        await queryRunner.query(`ALTER TABLE "article_royalty_users" ADD "file_id" integer`);
        await queryRunner.query(
            `ALTER TABLE "article_royalty_users" ADD CONSTRAINT "FK_ba58808d810e0c705e8238d319c" FOREIGN KEY ("file_id") REFERENCES "files"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`
        );
        await queryRunner.query(
            `ALTER TABLE "royalty_param_options" ADD CONSTRAINT "FK_ba3ad08f432deea16b74a2c3691" FOREIGN KEY ("created_by") REFERENCES "users"("id") ON DELETE RESTRICT ON UPDATE NO ACTION`
        );
        await queryRunner.query(
            `ALTER TABLE "royalty_param_options" ADD CONSTRAINT "FK_9ee3232fda8dc040ae79a858651" FOREIGN KEY ("updated_by") REFERENCES "users"("id") ON DELETE RESTRICT ON UPDATE NO ACTION`
        );
        await queryRunner.query(
            `ALTER TABLE "royalty_param_options" ADD CONSTRAINT "FK_0be953e531956800fb8d70c4ce4" FOREIGN KEY ("deleted_by") REFERENCES "users"("id") ON DELETE RESTRICT ON UPDATE NO ACTION`
        );
        await queryRunner.query(
            `ALTER TABLE "royalty_param_options" ADD CONSTRAINT "FK_7a6dbc2243404c222d33dbf620b" FOREIGN KEY ("royalty_param_id") REFERENCES "royalty_params"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`
        );
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "royalty_param_options" DROP CONSTRAINT "FK_7a6dbc2243404c222d33dbf620b"`);
        await queryRunner.query(`ALTER TABLE "royalty_param_options" DROP CONSTRAINT "FK_0be953e531956800fb8d70c4ce4"`);
        await queryRunner.query(`ALTER TABLE "royalty_param_options" DROP CONSTRAINT "FK_9ee3232fda8dc040ae79a858651"`);
        await queryRunner.query(`ALTER TABLE "royalty_param_options" DROP CONSTRAINT "FK_ba3ad08f432deea16b74a2c3691"`);
        await queryRunner.query(`ALTER TABLE "article_royalty_users" DROP CONSTRAINT "FK_ba58808d810e0c705e8238d319c"`);
        await queryRunner.query(`ALTER TABLE "article_royalty_users" DROP COLUMN "file_id"`);
        await queryRunner.query(`ALTER TABLE "article_royalty_users" DROP COLUMN "suggest_royalty"`);
        await queryRunner.query(`DROP TABLE "royalty_param_options"`);
    }
}
