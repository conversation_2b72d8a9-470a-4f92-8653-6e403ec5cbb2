import { MigrationInterface, QueryRunner } from 'typeorm';

export class CreateTemplatesTable1745894742367 implements MigrationInterface {
    name = 'CreateTemplatesTable1745894742367';

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(
            `CREATE TABLE "templates" ("id" SERIAL NOT NULL, "created_by" integer, "updated_by" integer, "deleted_by" integer, "created_at" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(), "updated_at" TIMESTAMP WITH TIME ZONE DEFAULT now(), "deleted_at" TIMESTAMP WITH TIME ZONE, "name" character varying NOT NULL, "desc" character varying, "content" text NOT NULL, "status_id" smallint NOT NULL, "avatar_id" integer, "department_id" integer NOT NULL, CONSTRAINT "PK_templates_id" PRIMARY KEY ("id"))`
        );
        await queryRunner.query(
            `ALTER TABLE "templates" ADD CONSTRAINT "FK_templates_created_by" FOREIGN KEY ("created_by") REFERENCES "users"("id") ON DELETE RESTRICT ON UPDATE NO ACTION`
        );
        await queryRunner.query(
            `ALTER TABLE "templates" ADD CONSTRAINT "FK_templates_updated_by" FOREIGN KEY ("updated_by") REFERENCES "users"("id") ON DELETE RESTRICT ON UPDATE NO ACTION`
        );
        await queryRunner.query(
            `ALTER TABLE "templates" ADD CONSTRAINT "FK_templates_deleted_by" FOREIGN KEY ("deleted_by") REFERENCES "users"("id") ON DELETE RESTRICT ON UPDATE NO ACTION`
        );
        await queryRunner.query(
            `ALTER TABLE "templates" ADD CONSTRAINT "FK_templates_avatar_id" FOREIGN KEY ("avatar_id") REFERENCES "files"("id") ON DELETE SET NULL ON UPDATE NO ACTION`
        );
        await queryRunner.query(
            `ALTER TABLE "templates" ADD CONSTRAINT "FK_templates_department_id" FOREIGN KEY ("department_id") REFERENCES "departments"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`
        );
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "templates" DROP CONSTRAINT "FK_templates_department_id"`);
        await queryRunner.query(`ALTER TABLE "templates" DROP CONSTRAINT "FK_templates_avatar_id"`);
        await queryRunner.query(`ALTER TABLE "templates" DROP CONSTRAINT "FK_templates_deleted_by"`);
        await queryRunner.query(`ALTER TABLE "templates" DROP CONSTRAINT "FK_templates_updated_by"`);
        await queryRunner.query(`ALTER TABLE "templates" DROP CONSTRAINT "FK_templates_created_by"`);
        await queryRunner.query(`DROP TABLE "templates"`);
    }
}
