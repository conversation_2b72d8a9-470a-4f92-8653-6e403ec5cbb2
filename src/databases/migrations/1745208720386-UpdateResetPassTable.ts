import { MigrationInterface, QueryRunner } from 'typeorm';

export class UpdateResetPassTable1745208720386 implements MigrationInterface {
    name = 'UpdateResetPassTable1745208720386';

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "password_resets" ADD "otp" character varying NOT NULL`);
        await queryRunner.query(`ALTER TABLE "password_resets" ALTER COLUMN "token" SET NOT NULL`);
        await queryRunner.query(
            `ALTER TABLE "groups" ADD CONSTRAINT "FK_59a5caf58073e782a8ee5138be7" FOREIGN KEY ("department_id") REFERENCES "departments"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`
        );
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "groups" DROP CONSTRAINT "FK_59a5caf58073e782a8ee5138be7"`);
        await queryRunner.query(`ALTER TABLE "password_resets" ALTER COLUMN "token" DROP NOT NULL`);
        await queryRunner.query(`ALTER TABLE "password_resets" DROP COLUMN "otp"`);
    }
}
