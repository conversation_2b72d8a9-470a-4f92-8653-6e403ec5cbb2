import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddStatusIdToArticleRoyaltyTypes1752826344184 implements MigrationInterface {
    name = 'AddStatusIdToArticleRoyaltyTypes1752826344184';

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "article_royalty_types" ADD "status_id" smallint NOT NULL DEFAULT 1`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "article_royalty_types" DROP COLUMN "status_id"`);
    }
}
