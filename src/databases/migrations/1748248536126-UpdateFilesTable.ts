import { MigrationInterface, QueryRunner } from 'typeorm';

export class UpdateFilesTable1748248536126 implements MigrationInterface {
    name = 'UpdateFilesTable1748248536126';

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "files" ADD "department_id" integer`);
        const departments = await queryRunner.query(`SELECT "id" FROM "departments" LIMIT 1`);
        if (departments.length > 0)
            await queryRunner.query(
                `UPDATE "files" SET "department_id" = ${departments[0].id} WHERE "department_id" IS NULL`
            );
        await queryRunner.query(`ALTER TABLE "files" ALTER COLUMN "department_id" SET NOT NULL`);
        await queryRunner.query(
            `ALTER TABLE "files" ADD CONSTRAINT "FK_fe49ddbcf2436d7a6fbbbc71293" FOREIGN KEY ("department_id") REFERENCES "departments"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`
        );
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "files" DROP CONSTRAINT "FK_fe49ddbcf2436d7a6fbbbc71293"`);
        await queryRunner.query(`ALTER TABLE "files" DROP COLUMN "department_id"`);
    }
}
