import { MigrationInterface, QueryRunner } from 'typeorm';

export class UpdateLayoutsTable1750481377913 implements MigrationInterface {
    name = 'UpdateLayoutsTable1750481377913';

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "layouts" ADD "is_mobile" boolean NOT NULL DEFAULT false`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "layouts" DROP COLUMN "is_mobile"`);
    }
}
