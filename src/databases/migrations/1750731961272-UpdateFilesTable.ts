import { MigrationInterface, QueryRunner } from 'typeorm';

export class UpdateFilesTable1750731961272 implements MigrationInterface {
    name = 'UpdateFilesTable1750731961272';

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "files" ADD "file_title" character varying`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "files" DROP COLUMN "file_title"`);
    }
}
