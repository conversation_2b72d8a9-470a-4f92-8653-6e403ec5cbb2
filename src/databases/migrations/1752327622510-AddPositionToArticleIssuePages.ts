import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddPositionToArticleIssuePages1752327622510 implements MigrationInterface {
    name = 'AddPositionToArticleIssuePages1752327622510';

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "article_issue_pages" ADD "position" jsonb`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "article_issue_pages" DROP COLUMN "position"`);
    }
}
