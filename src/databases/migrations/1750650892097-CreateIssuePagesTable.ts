import { MigrationInterface, QueryRunner } from 'typeorm';

export class CreateIssuePagesTable1750650892097 implements MigrationInterface {
    name = 'CreateIssuePagesTable1750650892097';

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(
            `CREATE TABLE "issue_pages" ("id" SERIAL NOT NULL, "created_by" integer, "updated_by" integer, "deleted_by" integer, "created_at" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(), "updated_at" TIMESTAMP WITH TIME ZONE DEFAULT now(), "deleted_at" TIMESTAMP WITH TIME ZONE, "name" character varying NOT NULL, "page_number" smallint NOT NULL, "status_id" smallint NOT NULL, "press_publication_id" integer NOT NULL, "issue_id" integer NOT NULL, "department_id" integer NOT NULL, CONSTRAINT "PK_cca35eda387d87bd5b242ba3e57" PRIMARY KEY ("id"))`
        );
        await queryRunner.query(
            `ALTER TABLE "issue_pages" ADD CONSTRAINT "FK_1b5dadb9444afb7323fe1c1cb05" FOREIGN KEY ("created_by") REFERENCES "users"("id") ON DELETE RESTRICT ON UPDATE NO ACTION`
        );
        await queryRunner.query(
            `ALTER TABLE "issue_pages" ADD CONSTRAINT "FK_e2ede4246a4abded4da437da08b" FOREIGN KEY ("updated_by") REFERENCES "users"("id") ON DELETE RESTRICT ON UPDATE NO ACTION`
        );
        await queryRunner.query(
            `ALTER TABLE "issue_pages" ADD CONSTRAINT "FK_718fb139103ea7a7b518b522277" FOREIGN KEY ("deleted_by") REFERENCES "users"("id") ON DELETE RESTRICT ON UPDATE NO ACTION`
        );
        await queryRunner.query(
            `ALTER TABLE "issue_pages" ADD CONSTRAINT "FK_58fed66fa021eed1408710bdad4" FOREIGN KEY ("press_publication_id") REFERENCES "press_publications"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`
        );
        await queryRunner.query(
            `ALTER TABLE "issue_pages" ADD CONSTRAINT "FK_57fb178a1e0311c41180ebf3d5b" FOREIGN KEY ("issue_id") REFERENCES "issues"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`
        );
        await queryRunner.query(
            `ALTER TABLE "issue_pages" ADD CONSTRAINT "FK_547c202ca025b1974d5a563c432" FOREIGN KEY ("department_id") REFERENCES "departments"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`
        );
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "issue_pages" DROP CONSTRAINT "FK_547c202ca025b1974d5a563c432"`);
        await queryRunner.query(`ALTER TABLE "issue_pages" DROP CONSTRAINT "FK_57fb178a1e0311c41180ebf3d5b"`);
        await queryRunner.query(`ALTER TABLE "issue_pages" DROP CONSTRAINT "FK_58fed66fa021eed1408710bdad4"`);
        await queryRunner.query(`ALTER TABLE "issue_pages" DROP CONSTRAINT "FK_718fb139103ea7a7b518b522277"`);
        await queryRunner.query(`ALTER TABLE "issue_pages" DROP CONSTRAINT "FK_e2ede4246a4abded4da437da08b"`);
        await queryRunner.query(`ALTER TABLE "issue_pages" DROP CONSTRAINT "FK_1b5dadb9444afb7323fe1c1cb05"`);
        await queryRunner.query(`DROP TABLE "issue_pages"`);
    }
}
