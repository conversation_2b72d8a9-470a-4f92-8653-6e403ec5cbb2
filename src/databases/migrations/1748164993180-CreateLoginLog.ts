import { MigrationInterface, QueryRunner } from 'typeorm';

export class CreateLoginLog1748164993180 implements MigrationInterface {
    name = 'CreateLoginLog1748164993180';

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(
            `CREATE TABLE "login_logs" ("id" SERIAL NOT NULL, "user_id" integer NOT NULL, "ip_address" character varying, "user_agent" character varying, "created_at" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(), CONSTRAINT "PK_15f7b02ad55d5ba905b2962ebab" PRIMARY KEY ("id"))`
        );
        await queryRunner.query(
            `ALTER TABLE "login_logs" ADD CONSTRAINT "FK_e2dffa109d0d3dbd94a0a51669c" FOREIGN KEY ("user_id") REFERENCES "users"("id") ON DELETE CASCADE ON UPDATE NO ACTION`
        );
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "login_logs" DROP CONSTRAINT "FK_e2dffa109d0d3dbd94a0a51669c"`);
        await queryRunner.query(`DROP TABLE "login_logs"`);
    }
}
