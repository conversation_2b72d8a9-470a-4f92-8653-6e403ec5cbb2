import { MigrationInterface, QueryRunner } from 'typeorm';

export class CreateArticlesTableGroup1746603113847 implements MigrationInterface {
    name = 'CreateArticlesTableGroup1746603113847';

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "templates" DROP CONSTRAINT "FK_templates_deleted_by"`);
        await queryRunner.query(`ALTER TABLE "templates" DROP CONSTRAINT "FK_templates_updated_by"`);
        await queryRunner.query(`ALTER TABLE "templates" DROP CONSTRAINT "FK_templates_created_by"`);
        await queryRunner.query(`ALTER TABLE "templates" DROP CONSTRAINT "FK_templates_avatar_id"`);
        await queryRunner.query(`ALTER TABLE "templates" DROP CONSTRAINT "FK_templates_department_id"`);
        await queryRunner.query(
            `CREATE TABLE "article_article_kinds" ("article_id" integer NOT NULL, "article_kind_id" smallint NOT NULL, CONSTRAINT "PK_0c550213a1deba497a5d3a0c34f" PRIMARY KEY ("article_id", "article_kind_id"))`
        );
        await queryRunner.query(
            `CREATE TABLE "article_categories" ("id" SERIAL NOT NULL, "created_by" integer, "updated_by" integer, "deleted_by" integer, "created_at" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(), "updated_at" TIMESTAMP WITH TIME ZONE DEFAULT now(), "deleted_at" TIMESTAMP WITH TIME ZONE, "article_id" integer NOT NULL, "category_id" integer NOT NULL, "display_order" smallint NOT NULL, CONSTRAINT "PK_eca1ad880e57e2860d7f7a20bc5" PRIMARY KEY ("id"))`
        );
        await queryRunner.query(
            `CREATE TABLE "article_tags" ("id" SERIAL NOT NULL, "created_by" integer, "updated_by" integer, "deleted_by" integer, "created_at" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(), "updated_at" TIMESTAMP WITH TIME ZONE DEFAULT now(), "deleted_at" TIMESTAMP WITH TIME ZONE, "article_id" integer NOT NULL, "tag_id" integer NOT NULL, CONSTRAINT "PK_75f74d8cce8a559622dffcc5ae2" PRIMARY KEY ("id"))`
        );
        await queryRunner.query(
            `CREATE TABLE "article_files" ("id" SERIAL NOT NULL, "created_by" integer, "updated_by" integer, "deleted_by" integer, "created_at" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(), "updated_at" TIMESTAMP WITH TIME ZONE DEFAULT now(), "deleted_at" TIMESTAMP WITH TIME ZONE, "article_id" integer NOT NULL, "file_id" integer NOT NULL, CONSTRAINT "PK_f4e12845c7bf97fe17238375210" PRIMARY KEY ("id"))`
        );
        await queryRunner.query(
            `CREATE TABLE "article_notes" ("id" SERIAL NOT NULL, "created_by" integer, "updated_by" integer, "deleted_by" integer, "created_at" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(), "updated_at" TIMESTAMP WITH TIME ZONE DEFAULT now(), "deleted_at" TIMESTAMP WITH TIME ZONE, "article_id" integer NOT NULL, "content" character varying NOT NULL, CONSTRAINT "PK_b0e794254508c048882e061f7c5" PRIMARY KEY ("id"))`
        );
        await queryRunner.query(
            `CREATE TABLE "article_comments" ("id" SERIAL NOT NULL, "created_by" integer, "updated_by" integer, "deleted_by" integer, "created_at" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(), "updated_at" TIMESTAMP WITH TIME ZONE DEFAULT now(), "deleted_at" TIMESTAMP WITH TIME ZONE, "article_id" integer NOT NULL, "sender_name" character varying NOT NULL, "sender_email" character varying NOT NULL, "content" character varying NOT NULL, "status_id" smallint NOT NULL, "ip_address" character varying NOT NULL, "user_agent" character varying NOT NULL, CONSTRAINT "PK_76305985dc2ec48641fdbd44c76" PRIMARY KEY ("id"))`
        );
        await queryRunner.query(
            `CREATE TABLE "article_logs" ("id" SERIAL NOT NULL, "created_by" integer, "updated_by" integer, "deleted_by" integer, "created_at" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(), "updated_at" TIMESTAMP WITH TIME ZONE DEFAULT now(), "deleted_at" TIMESTAMP WITH TIME ZONE, "article_id" integer NOT NULL, "content" json NOT NULL, CONSTRAINT "PK_700df37970df19c025c65e9334f" PRIMARY KEY ("id"))`
        );
        await queryRunner.query(
            `CREATE TABLE "articles" ("id" SERIAL NOT NULL, "created_by" integer, "updated_by" integer, "deleted_by" integer, "created_at" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(), "updated_at" TIMESTAMP WITH TIME ZONE DEFAULT now(), "deleted_at" TIMESTAMP WITH TIME ZONE, "title" character varying NOT NULL, "sub_title" character varying, "brief_title" character varying, "slug" character varying NOT NULL, "desc" text NOT NULL, "content" text NOT NULL, "avatar1_id" integer, "avatar2_id" integer, "workflow_id" integer NOT NULL, "article_type_id" smallint NOT NULL, "language_id" smallint NOT NULL, "pseudonym_id" integer NOT NULL, "layout_id" integer, "department_id" integer NOT NULL, "publish_date" TIMESTAMP WITH TIME ZONE, "source" character varying, "file_id" integer, "template_id" integer, "root_article_id" integer, "lock_user_id" integer, "lock_at" TIMESTAMP WITH TIME ZONE, CONSTRAINT "REL_7506d84ac797f560a774d656f6" UNIQUE ("avatar1_id"), CONSTRAINT "REL_69137c6e208109e5c53dc61f15" UNIQUE ("avatar2_id"), CONSTRAINT "REL_e0f9073f4cd67950680da9f475" UNIQUE ("file_id"), CONSTRAINT "PK_0a6e2c450d83e0b6052c2793334" PRIMARY KEY ("id"))`
        );
        await queryRunner.query(
            `CREATE TABLE "related_articles" ("id" SERIAL NOT NULL, "created_by" integer, "updated_by" integer, "deleted_by" integer, "created_at" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(), "updated_at" TIMESTAMP WITH TIME ZONE DEFAULT now(), "deleted_at" TIMESTAMP WITH TIME ZONE, "article_id" integer NOT NULL, "related_article_id" integer NOT NULL, CONSTRAINT "PK_25b75b9aec8d23288ccb237599e" PRIMARY KEY ("id"))`
        );
        await queryRunner.query(
            `ALTER TABLE "templates" ADD CONSTRAINT "UQ_0d2455504aba28130cac3239c3e" UNIQUE ("avatar_id")`
        );
        await queryRunner.query(
            `ALTER TABLE "templates" ADD CONSTRAINT "FK_e0753fa8d20f4eae08ebec1fef9" FOREIGN KEY ("created_by") REFERENCES "users"("id") ON DELETE RESTRICT ON UPDATE NO ACTION`
        );
        await queryRunner.query(
            `ALTER TABLE "templates" ADD CONSTRAINT "FK_18fc25f0cfc9d0bbfc6fe04046c" FOREIGN KEY ("updated_by") REFERENCES "users"("id") ON DELETE RESTRICT ON UPDATE NO ACTION`
        );
        await queryRunner.query(
            `ALTER TABLE "templates" ADD CONSTRAINT "FK_df101c0b573e3d8f7fb07daaafa" FOREIGN KEY ("deleted_by") REFERENCES "users"("id") ON DELETE RESTRICT ON UPDATE NO ACTION`
        );
        await queryRunner.query(
            `ALTER TABLE "templates" ADD CONSTRAINT "FK_0d2455504aba28130cac3239c3e" FOREIGN KEY ("avatar_id") REFERENCES "files"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`
        );
        await queryRunner.query(
            `ALTER TABLE "templates" ADD CONSTRAINT "FK_f60bcbb5fe6be82cb16239e90f2" FOREIGN KEY ("department_id") REFERENCES "departments"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`
        );
        await queryRunner.query(
            `ALTER TABLE "article_article_kinds" ADD CONSTRAINT "FK_8fac4adebd08bf1cdddab2c51de" FOREIGN KEY ("article_id") REFERENCES "articles"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`
        );
        await queryRunner.query(
            `ALTER TABLE "article_categories" ADD CONSTRAINT "FK_a4de191d12add976f05de5bf526" FOREIGN KEY ("created_by") REFERENCES "users"("id") ON DELETE RESTRICT ON UPDATE NO ACTION`
        );
        await queryRunner.query(
            `ALTER TABLE "article_categories" ADD CONSTRAINT "FK_2c25f227629fc472cbcf412a05c" FOREIGN KEY ("updated_by") REFERENCES "users"("id") ON DELETE RESTRICT ON UPDATE NO ACTION`
        );
        await queryRunner.query(
            `ALTER TABLE "article_categories" ADD CONSTRAINT "FK_7f0e84dc5d50a356dd2edc951a0" FOREIGN KEY ("deleted_by") REFERENCES "users"("id") ON DELETE RESTRICT ON UPDATE NO ACTION`
        );
        await queryRunner.query(
            `ALTER TABLE "article_categories" ADD CONSTRAINT "FK_6919cd26646fd24f7aac8166d46" FOREIGN KEY ("article_id") REFERENCES "articles"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`
        );
        await queryRunner.query(
            `ALTER TABLE "article_categories" ADD CONSTRAINT "FK_2074448c3764e149b3b0541c2a7" FOREIGN KEY ("category_id") REFERENCES "categories"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`
        );
        await queryRunner.query(
            `ALTER TABLE "article_tags" ADD CONSTRAINT "FK_a80c51d60d5683fecf3603ad259" FOREIGN KEY ("created_by") REFERENCES "users"("id") ON DELETE RESTRICT ON UPDATE NO ACTION`
        );
        await queryRunner.query(
            `ALTER TABLE "article_tags" ADD CONSTRAINT "FK_093df3c76d68b98efb6295e92dd" FOREIGN KEY ("updated_by") REFERENCES "users"("id") ON DELETE RESTRICT ON UPDATE NO ACTION`
        );
        await queryRunner.query(
            `ALTER TABLE "article_tags" ADD CONSTRAINT "FK_1077796aa25b040a0bc908ca992" FOREIGN KEY ("deleted_by") REFERENCES "users"("id") ON DELETE RESTRICT ON UPDATE NO ACTION`
        );
        await queryRunner.query(
            `ALTER TABLE "article_tags" ADD CONSTRAINT "FK_f8c9234a4c4cb37806387f0c9e9" FOREIGN KEY ("article_id") REFERENCES "articles"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`
        );
        await queryRunner.query(
            `ALTER TABLE "article_tags" ADD CONSTRAINT "FK_1325dd0b98ee0f8f673db6ce194" FOREIGN KEY ("tag_id") REFERENCES "tags"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`
        );
        await queryRunner.query(
            `ALTER TABLE "article_files" ADD CONSTRAINT "FK_a864692d634e5246ac2ac8be25c" FOREIGN KEY ("created_by") REFERENCES "users"("id") ON DELETE RESTRICT ON UPDATE NO ACTION`
        );
        await queryRunner.query(
            `ALTER TABLE "article_files" ADD CONSTRAINT "FK_27716bbd4171c75263fa74bdf22" FOREIGN KEY ("updated_by") REFERENCES "users"("id") ON DELETE RESTRICT ON UPDATE NO ACTION`
        );
        await queryRunner.query(
            `ALTER TABLE "article_files" ADD CONSTRAINT "FK_b262ca426f773975e137f0bda0f" FOREIGN KEY ("deleted_by") REFERENCES "users"("id") ON DELETE RESTRICT ON UPDATE NO ACTION`
        );
        await queryRunner.query(
            `ALTER TABLE "article_files" ADD CONSTRAINT "FK_c1c110a4cc08441c443fda9779c" FOREIGN KEY ("article_id") REFERENCES "articles"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`
        );
        await queryRunner.query(
            `ALTER TABLE "article_files" ADD CONSTRAINT "FK_db7ae42c36aae1bd1a03b79890a" FOREIGN KEY ("file_id") REFERENCES "files"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`
        );
        await queryRunner.query(
            `ALTER TABLE "article_notes" ADD CONSTRAINT "FK_3564fe898d2bf95f51886902526" FOREIGN KEY ("created_by") REFERENCES "users"("id") ON DELETE RESTRICT ON UPDATE NO ACTION`
        );
        await queryRunner.query(
            `ALTER TABLE "article_notes" ADD CONSTRAINT "FK_27de3ee7c4b9d3578747bba1998" FOREIGN KEY ("updated_by") REFERENCES "users"("id") ON DELETE RESTRICT ON UPDATE NO ACTION`
        );
        await queryRunner.query(
            `ALTER TABLE "article_notes" ADD CONSTRAINT "FK_1d50b0f87de5db9bb7ddcf34746" FOREIGN KEY ("deleted_by") REFERENCES "users"("id") ON DELETE RESTRICT ON UPDATE NO ACTION`
        );
        await queryRunner.query(
            `ALTER TABLE "article_notes" ADD CONSTRAINT "FK_ae99dac0118230697fbf99f8774" FOREIGN KEY ("article_id") REFERENCES "articles"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`
        );
        await queryRunner.query(
            `ALTER TABLE "article_comments" ADD CONSTRAINT "FK_f066f4c031639b0e3b78ab836f5" FOREIGN KEY ("created_by") REFERENCES "users"("id") ON DELETE RESTRICT ON UPDATE NO ACTION`
        );
        await queryRunner.query(
            `ALTER TABLE "article_comments" ADD CONSTRAINT "FK_50fe4a030cf572a490610daff68" FOREIGN KEY ("updated_by") REFERENCES "users"("id") ON DELETE RESTRICT ON UPDATE NO ACTION`
        );
        await queryRunner.query(
            `ALTER TABLE "article_comments" ADD CONSTRAINT "FK_47d9973d3f65d95bf284feac0c7" FOREIGN KEY ("deleted_by") REFERENCES "users"("id") ON DELETE RESTRICT ON UPDATE NO ACTION`
        );
        await queryRunner.query(
            `ALTER TABLE "article_comments" ADD CONSTRAINT "FK_4842e344aefdd5cea5d7c87d9df" FOREIGN KEY ("article_id") REFERENCES "articles"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`
        );
        await queryRunner.query(
            `ALTER TABLE "article_logs" ADD CONSTRAINT "FK_54ee1a563dac190e6fd5272ccc1" FOREIGN KEY ("created_by") REFERENCES "users"("id") ON DELETE RESTRICT ON UPDATE NO ACTION`
        );
        await queryRunner.query(
            `ALTER TABLE "article_logs" ADD CONSTRAINT "FK_03c2ec4e48f1312ae0fa0b12420" FOREIGN KEY ("updated_by") REFERENCES "users"("id") ON DELETE RESTRICT ON UPDATE NO ACTION`
        );
        await queryRunner.query(
            `ALTER TABLE "article_logs" ADD CONSTRAINT "FK_61508974bbf5f091fdb74043782" FOREIGN KEY ("deleted_by") REFERENCES "users"("id") ON DELETE RESTRICT ON UPDATE NO ACTION`
        );
        await queryRunner.query(
            `ALTER TABLE "article_logs" ADD CONSTRAINT "FK_361adc823497a4846eaa7de24fb" FOREIGN KEY ("article_id") REFERENCES "articles"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`
        );
        await queryRunner.query(
            `ALTER TABLE "articles" ADD CONSTRAINT "FK_93c112ea984ff2e43deaa5dd202" FOREIGN KEY ("created_by") REFERENCES "users"("id") ON DELETE RESTRICT ON UPDATE NO ACTION`
        );
        await queryRunner.query(
            `ALTER TABLE "articles" ADD CONSTRAINT "FK_bdb780e06671ab00c66102c7997" FOREIGN KEY ("updated_by") REFERENCES "users"("id") ON DELETE RESTRICT ON UPDATE NO ACTION`
        );
        await queryRunner.query(
            `ALTER TABLE "articles" ADD CONSTRAINT "FK_02abd198ebb78860ab71386f524" FOREIGN KEY ("deleted_by") REFERENCES "users"("id") ON DELETE RESTRICT ON UPDATE NO ACTION`
        );
        await queryRunner.query(
            `ALTER TABLE "articles" ADD CONSTRAINT "FK_7506d84ac797f560a774d656f68" FOREIGN KEY ("avatar1_id") REFERENCES "files"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`
        );
        await queryRunner.query(
            `ALTER TABLE "articles" ADD CONSTRAINT "FK_69137c6e208109e5c53dc61f155" FOREIGN KEY ("avatar2_id") REFERENCES "files"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`
        );
        await queryRunner.query(
            `ALTER TABLE "articles" ADD CONSTRAINT "FK_2bdb7dfc2ddbd43ee8bdceab0ef" FOREIGN KEY ("workflow_id") REFERENCES "workflows"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`
        );
        await queryRunner.query(
            `ALTER TABLE "articles" ADD CONSTRAINT "FK_07b3094c3d85e378e100f00fea8" FOREIGN KEY ("pseudonym_id") REFERENCES "pseudonyms"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`
        );
        await queryRunner.query(
            `ALTER TABLE "articles" ADD CONSTRAINT "FK_9c36a2b5a85ec7464169eec22e0" FOREIGN KEY ("layout_id") REFERENCES "layouts"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`
        );
        await queryRunner.query(
            `ALTER TABLE "articles" ADD CONSTRAINT "FK_c030ec8190027271c06fbeaae32" FOREIGN KEY ("department_id") REFERENCES "departments"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`
        );
        await queryRunner.query(
            `ALTER TABLE "articles" ADD CONSTRAINT "FK_e0f9073f4cd67950680da9f4758" FOREIGN KEY ("file_id") REFERENCES "files"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`
        );
        await queryRunner.query(
            `ALTER TABLE "articles" ADD CONSTRAINT "FK_47551f1d13a71e6b3f3c84d8827" FOREIGN KEY ("template_id") REFERENCES "templates"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`
        );
        await queryRunner.query(
            `ALTER TABLE "articles" ADD CONSTRAINT "FK_88c6a09d96be49dc0b0232e8f9f" FOREIGN KEY ("root_article_id") REFERENCES "articles"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`
        );
        await queryRunner.query(
            `ALTER TABLE "articles" ADD CONSTRAINT "FK_02c4fd75f947b3d8daaf67ae041" FOREIGN KEY ("lock_user_id") REFERENCES "users"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`
        );
        await queryRunner.query(
            `ALTER TABLE "related_articles" ADD CONSTRAINT "FK_a3a5912acd48616d4a3ebe59860" FOREIGN KEY ("created_by") REFERENCES "users"("id") ON DELETE RESTRICT ON UPDATE NO ACTION`
        );
        await queryRunner.query(
            `ALTER TABLE "related_articles" ADD CONSTRAINT "FK_48533e2f24ece6d93cb813c98ae" FOREIGN KEY ("updated_by") REFERENCES "users"("id") ON DELETE RESTRICT ON UPDATE NO ACTION`
        );
        await queryRunner.query(
            `ALTER TABLE "related_articles" ADD CONSTRAINT "FK_ef8dac3fea0dfa356706261fc51" FOREIGN KEY ("deleted_by") REFERENCES "users"("id") ON DELETE RESTRICT ON UPDATE NO ACTION`
        );
        await queryRunner.query(
            `ALTER TABLE "related_articles" ADD CONSTRAINT "FK_58c9719837e2308777751bb90ab" FOREIGN KEY ("article_id") REFERENCES "articles"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`
        );
        await queryRunner.query(
            `ALTER TABLE "related_articles" ADD CONSTRAINT "FK_9765ab13a7c21382e9b829a80d3" FOREIGN KEY ("related_article_id") REFERENCES "articles"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`
        );
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "related_articles" DROP CONSTRAINT "FK_9765ab13a7c21382e9b829a80d3"`);
        await queryRunner.query(`ALTER TABLE "related_articles" DROP CONSTRAINT "FK_58c9719837e2308777751bb90ab"`);
        await queryRunner.query(`ALTER TABLE "related_articles" DROP CONSTRAINT "FK_ef8dac3fea0dfa356706261fc51"`);
        await queryRunner.query(`ALTER TABLE "related_articles" DROP CONSTRAINT "FK_48533e2f24ece6d93cb813c98ae"`);
        await queryRunner.query(`ALTER TABLE "related_articles" DROP CONSTRAINT "FK_a3a5912acd48616d4a3ebe59860"`);
        await queryRunner.query(`ALTER TABLE "articles" DROP CONSTRAINT "FK_02c4fd75f947b3d8daaf67ae041"`);
        await queryRunner.query(`ALTER TABLE "articles" DROP CONSTRAINT "FK_88c6a09d96be49dc0b0232e8f9f"`);
        await queryRunner.query(`ALTER TABLE "articles" DROP CONSTRAINT "FK_47551f1d13a71e6b3f3c84d8827"`);
        await queryRunner.query(`ALTER TABLE "articles" DROP CONSTRAINT "FK_e0f9073f4cd67950680da9f4758"`);
        await queryRunner.query(`ALTER TABLE "articles" DROP CONSTRAINT "FK_c030ec8190027271c06fbeaae32"`);
        await queryRunner.query(`ALTER TABLE "articles" DROP CONSTRAINT "FK_9c36a2b5a85ec7464169eec22e0"`);
        await queryRunner.query(`ALTER TABLE "articles" DROP CONSTRAINT "FK_07b3094c3d85e378e100f00fea8"`);
        await queryRunner.query(`ALTER TABLE "articles" DROP CONSTRAINT "FK_2bdb7dfc2ddbd43ee8bdceab0ef"`);
        await queryRunner.query(`ALTER TABLE "articles" DROP CONSTRAINT "FK_69137c6e208109e5c53dc61f155"`);
        await queryRunner.query(`ALTER TABLE "articles" DROP CONSTRAINT "FK_7506d84ac797f560a774d656f68"`);
        await queryRunner.query(`ALTER TABLE "articles" DROP CONSTRAINT "FK_02abd198ebb78860ab71386f524"`);
        await queryRunner.query(`ALTER TABLE "articles" DROP CONSTRAINT "FK_bdb780e06671ab00c66102c7997"`);
        await queryRunner.query(`ALTER TABLE "articles" DROP CONSTRAINT "FK_93c112ea984ff2e43deaa5dd202"`);
        await queryRunner.query(`ALTER TABLE "article_logs" DROP CONSTRAINT "FK_361adc823497a4846eaa7de24fb"`);
        await queryRunner.query(`ALTER TABLE "article_logs" DROP CONSTRAINT "FK_61508974bbf5f091fdb74043782"`);
        await queryRunner.query(`ALTER TABLE "article_logs" DROP CONSTRAINT "FK_03c2ec4e48f1312ae0fa0b12420"`);
        await queryRunner.query(`ALTER TABLE "article_logs" DROP CONSTRAINT "FK_54ee1a563dac190e6fd5272ccc1"`);
        await queryRunner.query(`ALTER TABLE "article_comments" DROP CONSTRAINT "FK_4842e344aefdd5cea5d7c87d9df"`);
        await queryRunner.query(`ALTER TABLE "article_comments" DROP CONSTRAINT "FK_47d9973d3f65d95bf284feac0c7"`);
        await queryRunner.query(`ALTER TABLE "article_comments" DROP CONSTRAINT "FK_50fe4a030cf572a490610daff68"`);
        await queryRunner.query(`ALTER TABLE "article_comments" DROP CONSTRAINT "FK_f066f4c031639b0e3b78ab836f5"`);
        await queryRunner.query(`ALTER TABLE "article_notes" DROP CONSTRAINT "FK_ae99dac0118230697fbf99f8774"`);
        await queryRunner.query(`ALTER TABLE "article_notes" DROP CONSTRAINT "FK_1d50b0f87de5db9bb7ddcf34746"`);
        await queryRunner.query(`ALTER TABLE "article_notes" DROP CONSTRAINT "FK_27de3ee7c4b9d3578747bba1998"`);
        await queryRunner.query(`ALTER TABLE "article_notes" DROP CONSTRAINT "FK_3564fe898d2bf95f51886902526"`);
        await queryRunner.query(`ALTER TABLE "article_files" DROP CONSTRAINT "FK_db7ae42c36aae1bd1a03b79890a"`);
        await queryRunner.query(`ALTER TABLE "article_files" DROP CONSTRAINT "FK_c1c110a4cc08441c443fda9779c"`);
        await queryRunner.query(`ALTER TABLE "article_files" DROP CONSTRAINT "FK_b262ca426f773975e137f0bda0f"`);
        await queryRunner.query(`ALTER TABLE "article_files" DROP CONSTRAINT "FK_27716bbd4171c75263fa74bdf22"`);
        await queryRunner.query(`ALTER TABLE "article_files" DROP CONSTRAINT "FK_a864692d634e5246ac2ac8be25c"`);
        await queryRunner.query(`ALTER TABLE "article_tags" DROP CONSTRAINT "FK_1325dd0b98ee0f8f673db6ce194"`);
        await queryRunner.query(`ALTER TABLE "article_tags" DROP CONSTRAINT "FK_f8c9234a4c4cb37806387f0c9e9"`);
        await queryRunner.query(`ALTER TABLE "article_tags" DROP CONSTRAINT "FK_1077796aa25b040a0bc908ca992"`);
        await queryRunner.query(`ALTER TABLE "article_tags" DROP CONSTRAINT "FK_093df3c76d68b98efb6295e92dd"`);
        await queryRunner.query(`ALTER TABLE "article_tags" DROP CONSTRAINT "FK_a80c51d60d5683fecf3603ad259"`);
        await queryRunner.query(`ALTER TABLE "article_categories" DROP CONSTRAINT "FK_2074448c3764e149b3b0541c2a7"`);
        await queryRunner.query(`ALTER TABLE "article_categories" DROP CONSTRAINT "FK_6919cd26646fd24f7aac8166d46"`);
        await queryRunner.query(`ALTER TABLE "article_categories" DROP CONSTRAINT "FK_7f0e84dc5d50a356dd2edc951a0"`);
        await queryRunner.query(`ALTER TABLE "article_categories" DROP CONSTRAINT "FK_2c25f227629fc472cbcf412a05c"`);
        await queryRunner.query(`ALTER TABLE "article_categories" DROP CONSTRAINT "FK_a4de191d12add976f05de5bf526"`);
        await queryRunner.query(`ALTER TABLE "article_article_kinds" DROP CONSTRAINT "FK_8fac4adebd08bf1cdddab2c51de"`);
        await queryRunner.query(`ALTER TABLE "templates" DROP CONSTRAINT "FK_f60bcbb5fe6be82cb16239e90f2"`);
        await queryRunner.query(`ALTER TABLE "templates" DROP CONSTRAINT "FK_0d2455504aba28130cac3239c3e"`);
        await queryRunner.query(`ALTER TABLE "templates" DROP CONSTRAINT "FK_df101c0b573e3d8f7fb07daaafa"`);
        await queryRunner.query(`ALTER TABLE "templates" DROP CONSTRAINT "FK_18fc25f0cfc9d0bbfc6fe04046c"`);
        await queryRunner.query(`ALTER TABLE "templates" DROP CONSTRAINT "FK_e0753fa8d20f4eae08ebec1fef9"`);
        await queryRunner.query(`ALTER TABLE "templates" DROP CONSTRAINT "UQ_0d2455504aba28130cac3239c3e"`);
        await queryRunner.query(`DROP TABLE "related_articles"`);
        await queryRunner.query(`DROP TABLE "articles"`);
        await queryRunner.query(`DROP TABLE "article_logs"`);
        await queryRunner.query(`DROP TABLE "article_comments"`);
        await queryRunner.query(`DROP TABLE "article_notes"`);
        await queryRunner.query(`DROP TABLE "article_files"`);
        await queryRunner.query(`DROP TABLE "article_tags"`);
        await queryRunner.query(`DROP TABLE "article_categories"`);
        await queryRunner.query(`DROP TABLE "article_article_kinds"`);
        await queryRunner.query(
            `ALTER TABLE "templates" ADD CONSTRAINT "FK_templates_department_id" FOREIGN KEY ("department_id") REFERENCES "departments"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`
        );
        await queryRunner.query(
            `ALTER TABLE "templates" ADD CONSTRAINT "FK_templates_avatar_id" FOREIGN KEY ("avatar_id") REFERENCES "files"("id") ON DELETE SET NULL ON UPDATE NO ACTION`
        );
        await queryRunner.query(
            `ALTER TABLE "templates" ADD CONSTRAINT "FK_templates_created_by" FOREIGN KEY ("created_by") REFERENCES "users"("id") ON DELETE RESTRICT ON UPDATE NO ACTION`
        );
        await queryRunner.query(
            `ALTER TABLE "templates" ADD CONSTRAINT "FK_templates_updated_by" FOREIGN KEY ("updated_by") REFERENCES "users"("id") ON DELETE RESTRICT ON UPDATE NO ACTION`
        );
        await queryRunner.query(
            `ALTER TABLE "templates" ADD CONSTRAINT "FK_templates_deleted_by" FOREIGN KEY ("deleted_by") REFERENCES "users"("id") ON DELETE RESTRICT ON UPDATE NO ACTION`
        );
    }
}
