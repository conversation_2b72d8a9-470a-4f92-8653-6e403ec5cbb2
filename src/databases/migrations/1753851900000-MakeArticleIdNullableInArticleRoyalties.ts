import { MigrationInterface, QueryRunner } from "typeorm";

export class MakeArticleIdNullableInArticleRoyalties1753851900000 implements MigrationInterface {
    name = 'MakeArticleIdNullableInArticleRoyalties1753851900000'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "article_royalties" ALTER COLUMN "article_id" DROP NOT NULL`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "article_royalties" ALTER COLUMN "article_id" SET NOT NULL`);
    }
}