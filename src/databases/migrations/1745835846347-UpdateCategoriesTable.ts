import { MigrationInterface, QueryRunner } from 'typeorm';

export class UpdateCategoriesTable1745835846347 implements MigrationInterface {
    name = 'UpdateCategoriesTable1745835846347';

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "categories" ADD "slug" character varying NOT NULL`);
        await queryRunner.query(`ALTER TABLE "categories" ADD "layout_id" integer`);
        await queryRunner.query(`ALTER TABLE "categories" ADD "is_major" boolean NOT NULL DEFAULT true`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "categories" DROP COLUMN "is_major"`);
        await queryRunner.query(`ALTER TABLE "categories" DROP COLUMN "layout_id"`);
        await queryRunner.query(`ALTER TABLE "categories" DROP COLUMN "slug"`);
    }
}
