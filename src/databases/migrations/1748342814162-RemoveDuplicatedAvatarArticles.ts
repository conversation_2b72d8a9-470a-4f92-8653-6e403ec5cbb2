import { MigrationInterface, QueryRunner } from 'typeorm';

export class RemoveDuplicatedAvatarArticles1748342814162 implements MigrationInterface {
    name = 'RemoveDuplicatedAvatarArticles1748342814162';

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "articles" DROP CONSTRAINT "FK_7506d84ac797f560a774d656f68"`);
        await queryRunner.query(`ALTER TABLE "articles" DROP CONSTRAINT "FK_69137c6e208109e5c53dc61f155"`);
        await queryRunner.query(`ALTER TABLE "articles" DROP CONSTRAINT "REL_7506d84ac797f560a774d656f6"`);
        await queryRunner.query(`ALTER TABLE "articles" DROP CONSTRAINT "REL_69137c6e208109e5c53dc61f15"`);
        await queryRunner.query(
            `ALTER TABLE "articles" ADD CONSTRAINT "FK_7506d84ac797f560a774d656f68" FOREIGN KEY ("avatar1_id") REFERENCES "files"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`
        );
        await queryRunner.query(
            `ALTER TABLE "articles" ADD CONSTRAINT "FK_69137c6e208109e5c53dc61f155" FOREIGN KEY ("avatar2_id") REFERENCES "files"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`
        );
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "articles" DROP CONSTRAINT "FK_69137c6e208109e5c53dc61f155"`);
        await queryRunner.query(`ALTER TABLE "articles" DROP CONSTRAINT "FK_7506d84ac797f560a774d656f68"`);
        await queryRunner.query(
            `ALTER TABLE "articles" ADD CONSTRAINT "REL_69137c6e208109e5c53dc61f15" UNIQUE ("avatar2_id")`
        );
        await queryRunner.query(
            `ALTER TABLE "articles" ADD CONSTRAINT "REL_7506d84ac797f560a774d656f6" UNIQUE ("avatar1_id")`
        );
        await queryRunner.query(
            `ALTER TABLE "articles" ADD CONSTRAINT "FK_69137c6e208109e5c53dc61f155" FOREIGN KEY ("avatar2_id") REFERENCES "files"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`
        );
        await queryRunner.query(
            `ALTER TABLE "articles" ADD CONSTRAINT "FK_7506d84ac797f560a774d656f68" FOREIGN KEY ("avatar1_id") REFERENCES "files"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`
        );
    }
}
