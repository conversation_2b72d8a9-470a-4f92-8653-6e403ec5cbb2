import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddIsDefaultToArticleRoyaltyTypes1752738123665 implements MigrationInterface {
    name = 'AddIsDefaultToArticleRoyaltyTypes1752738123665';

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "article_royalty_types" ADD "is_default" boolean NOT NULL DEFAULT false`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "article_royalty_types" DROP COLUMN "is_default"`);
    }
}
