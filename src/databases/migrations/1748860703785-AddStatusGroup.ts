import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddStatusGroup1748860703785 implements MigrationInterface {
    name = 'AddStatusGroup1748860703785';

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "groups" ADD "status_id" smallint DEFAULT '1'`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "groups" DROP COLUMN "status_id"`);
    }
}
