import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddArticleTypeIdsToRoyaltyParams1753851597976 implements MigrationInterface {
    name = 'AddArticleTypeIdsToRoyaltyParams1753851597976';

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "royalty_params" ADD "article_type_ids" json`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "royalty_params" DROP COLUMN "article_type_ids"`);
    }
}
