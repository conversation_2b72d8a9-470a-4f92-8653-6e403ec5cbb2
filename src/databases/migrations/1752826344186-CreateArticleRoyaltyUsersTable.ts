import { MigrationInterface, QueryRunner } from 'typeorm';

export class CreateArticleRoyaltyUsersTable1752826344186 implements MigrationInterface {
    name = 'CreateArticleRoyaltyUsersTable1752826344186';

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(
            `CREATE TABLE "article_royalty_users" ("id" SERIAL NOT NULL, "created_by" integer, "updated_by" integer, "deleted_by" integer, "created_at" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(), "updated_at" TIMESTAMP WITH TIME ZONE DEFAULT now(), "deleted_at" TIMESTAMP WITH TIME ZONE, "article_royalty_id" integer NOT NULL, "user_id" integer NOT NULL, "percent" smallint NOT NULL, "comment" character varying, CONSTRAINT "PK_article_royalty_users_id" PRIMARY KEY ("id"))`
        );
        await queryRunner.query(
            `ALTER TABLE "article_royalty_users" ADD CONSTRAINT "FK_article_royalty_users_created_by" FOREIGN KEY ("created_by") REFERENCES "users"("id") ON DELETE RESTRICT ON UPDATE NO ACTION`
        );
        await queryRunner.query(
            `ALTER TABLE "article_royalty_users" ADD CONSTRAINT "FK_article_royalty_users_updated_by" FOREIGN KEY ("updated_by") REFERENCES "users"("id") ON DELETE RESTRICT ON UPDATE NO ACTION`
        );
        await queryRunner.query(
            `ALTER TABLE "article_royalty_users" ADD CONSTRAINT "FK_article_royalty_users_deleted_by" FOREIGN KEY ("deleted_by") REFERENCES "users"("id") ON DELETE RESTRICT ON UPDATE NO ACTION`
        );
        await queryRunner.query(
            `ALTER TABLE "article_royalty_users" ADD CONSTRAINT "FK_article_royalty_users_article_royalty_id" FOREIGN KEY ("article_royalty_id") REFERENCES "article_royalties"("id") ON DELETE CASCADE ON UPDATE NO ACTION`
        );
        await queryRunner.query(
            `ALTER TABLE "article_royalty_users" ADD CONSTRAINT "FK_article_royalty_users_user_id" FOREIGN KEY ("user_id") REFERENCES "users"("id") ON DELETE CASCADE ON UPDATE NO ACTION`
        );
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(
            `ALTER TABLE "article_royalty_users" DROP CONSTRAINT "FK_article_royalty_users_user_id"`
        );
        await queryRunner.query(
            `ALTER TABLE "article_royalty_users" DROP CONSTRAINT "FK_article_royalty_users_article_royalty_id"`
        );
        await queryRunner.query(
            `ALTER TABLE "article_royalty_users" DROP CONSTRAINT "FK_article_royalty_users_deleted_by"`
        );
        await queryRunner.query(
            `ALTER TABLE "article_royalty_users" DROP CONSTRAINT "FK_article_royalty_users_updated_by"`
        );
        await queryRunner.query(
            `ALTER TABLE "article_royalty_users" DROP CONSTRAINT "FK_article_royalty_users_created_by"`
        );
        await queryRunner.query(`DROP TABLE "article_royalty_users"`);
    }
}
