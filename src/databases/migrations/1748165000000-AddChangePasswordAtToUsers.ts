import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddChangePasswordAtToUsers1748165000000 implements MigrationInterface {
    name = 'AddChangePasswordAtToUsers1748165000000';

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "users" ADD "change_password_at" TIMESTAMP WITH TIME ZONE`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "users" DROP COLUMN "change_password_at"`);
    }
}
