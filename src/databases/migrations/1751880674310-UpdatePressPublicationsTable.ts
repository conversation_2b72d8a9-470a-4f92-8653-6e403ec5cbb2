import { MigrationInterface, QueryRunner } from 'typeorm';

export class UpdatePressPublicationsTable1751880674310 implements MigrationInterface {
    name = 'UpdatePressPublicationsTable1751880674310';

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(
            `ALTER TABLE "press_publications" ADD "issue_pre_created" smallint NOT NULL DEFAULT '0'`
        );
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "press_publications" DROP COLUMN "issue_pre_created"`);
    }
}
