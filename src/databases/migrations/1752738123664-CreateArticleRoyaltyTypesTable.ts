import { MigrationInterface, QueryRunner } from 'typeorm';

export class CreateArticleRoyaltyTypesTable1752738123664 implements MigrationInterface {
    name = 'CreateArticleRoyaltyTypesTable1752738123664';

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(
            `CREATE TABLE "article_royalty_types" ("id" SERIAL NOT NULL, "created_by" integer, "updated_by" integer, "deleted_by" integer, "created_at" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(), "updated_at" TIMESTAMP WITH TIME ZONE DEFAULT now(), "deleted_at" TIMESTAMP WITH TIME ZONE, "name" character varying(255) NOT NULL, "desc" character varying(255), "article_type_id" smallint NOT NULL, "from_royalty" integer NOT NULL, "to_royalty" integer NOT NULL, CONSTRAINT "PK_article_royalty_types_id" PRIMARY KEY ("id"))`
        );
        await queryRunner.query(
            `ALTER TABLE "article_royalty_types" ADD CONSTRAINT "FK_article_royalty_types_created_by" FOREIGN KEY ("created_by") REFERENCES "users"("id") ON DELETE RESTRICT ON UPDATE NO ACTION`
        );
        await queryRunner.query(
            `ALTER TABLE "article_royalty_types" ADD CONSTRAINT "FK_article_royalty_types_updated_by" FOREIGN KEY ("updated_by") REFERENCES "users"("id") ON DELETE RESTRICT ON UPDATE NO ACTION`
        );
        await queryRunner.query(
            `ALTER TABLE "article_royalty_types" ADD CONSTRAINT "FK_article_royalty_types_deleted_by" FOREIGN KEY ("deleted_by") REFERENCES "users"("id") ON DELETE RESTRICT ON UPDATE NO ACTION`
        );
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(
            `ALTER TABLE "article_royalty_types" DROP CONSTRAINT "FK_article_royalty_types_deleted_by"`
        );
        await queryRunner.query(
            `ALTER TABLE "article_royalty_types" DROP CONSTRAINT "FK_article_royalty_types_updated_by"`
        );
        await queryRunner.query(
            `ALTER TABLE "article_royalty_types" DROP CONSTRAINT "FK_article_royalty_types_created_by"`
        );
        await queryRunner.query(`DROP TABLE "article_royalty_types"`);
    }
}
