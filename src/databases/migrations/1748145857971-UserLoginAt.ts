import { MigrationInterface, QueryRunner } from 'typeorm';

export class UserLoginAt1748145857971 implements MigrationInterface {
    name = 'UserLoginAt1748145857971';

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "users" ADD "login_at" TIMESTAMP WITH TIME ZONE`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "users" DROP COLUMN "login_at"`);
    }
}
