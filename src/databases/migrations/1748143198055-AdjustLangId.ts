import { MigrationInterface, QueryRunner } from 'typeorm';

export class AdjustLangId1748143198055 implements MigrationInterface {
    name = 'AdjustLangId1748143198055';

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(
            `CREATE TABLE "user_department_groups" ("user_department_id" integer NOT NULL, "group_id" integer NOT NULL, CONSTRAINT "PK_bc945a107e94272793c60ab08a1" PRIMARY KEY ("user_department_id", "group_id"))`
        );
        await queryRunner.query(
            `CREATE INDEX "IDX_87e26a5291023a24e19931bbba" ON "user_department_groups" ("user_department_id") `
        );
        await queryRunner.query(
            `CREATE INDEX "IDX_69518f7cef3cfc187caf8db8bb" ON "user_department_groups" ("group_id") `
        );
        await queryRunner.query(`ALTER TABLE "categories" DROP COLUMN "language_id"`);
        await queryRunner.query(`ALTER TABLE "tags" DROP COLUMN "language_id"`);
        await queryRunner.query(`ALTER TABLE "articles" DROP COLUMN "language_id"`);
        await queryRunner.query(`ALTER TABLE "departments" ADD "language_id" smallint`);
        await queryRunner.query(`ALTER TABLE "users" ALTER COLUMN "phone" SET NOT NULL`);
        await queryRunner.query(`ALTER TABLE "users" ALTER COLUMN "email" SET NOT NULL`);
        await queryRunner.query(
            `ALTER TABLE "user_department_groups" ADD CONSTRAINT "FK_87e26a5291023a24e19931bbba7" FOREIGN KEY ("user_department_id") REFERENCES "user_departments"("id") ON DELETE CASCADE ON UPDATE CASCADE`
        );
        await queryRunner.query(
            `ALTER TABLE "user_department_groups" ADD CONSTRAINT "FK_69518f7cef3cfc187caf8db8bbf" FOREIGN KEY ("group_id") REFERENCES "groups"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`
        );
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(
            `ALTER TABLE "user_department_groups" DROP CONSTRAINT "FK_69518f7cef3cfc187caf8db8bbf"`
        );
        await queryRunner.query(
            `ALTER TABLE "user_department_groups" DROP CONSTRAINT "FK_87e26a5291023a24e19931bbba7"`
        );
        await queryRunner.query(`ALTER TABLE "users" ALTER COLUMN "email" DROP NOT NULL`);
        await queryRunner.query(`ALTER TABLE "users" ALTER COLUMN "phone" DROP NOT NULL`);
        await queryRunner.query(`ALTER TABLE "departments" DROP COLUMN "language_id"`);
        await queryRunner.query(`ALTER TABLE "articles" ADD "language_id" smallint NOT NULL`);
        await queryRunner.query(`ALTER TABLE "tags" ADD "language_id" smallint NOT NULL`);
        await queryRunner.query(`ALTER TABLE "categories" ADD "language_id" smallint NOT NULL`);
        await queryRunner.query(`DROP INDEX "public"."IDX_69518f7cef3cfc187caf8db8bb"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_87e26a5291023a24e19931bbba"`);
        await queryRunner.query(`DROP TABLE "user_department_groups"`);
    }
}
