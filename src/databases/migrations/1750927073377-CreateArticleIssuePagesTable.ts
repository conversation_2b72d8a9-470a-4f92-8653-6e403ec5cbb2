import { MigrationInterface, QueryRunner } from 'typeorm';

export class CreateArticleIssuePagesTable1750927073377 implements MigrationInterface {
    name = 'CreateArticleIssuePagesTable1750927073377';

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "articles" DROP CONSTRAINT "FK_1df99adda643979e7f448223b51"`);
        await queryRunner.query(
            `CREATE TABLE "article_issue_pages" ("id" SERIAL NOT NULL, "created_by" integer, "updated_by" integer, "deleted_by" integer, "created_at" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(), "updated_at" TIMESTAMP WITH TIME ZONE DEFAULT now(), "deleted_at" TIMESTAMP WITH TIME ZONE, "article_id" integer NOT NULL, "issue_page_id" integer, "display_order" smallint NOT NULL, "comment" character varying, CONSTRAINT "PK_bb860e62cc554dedc35ac0de352" PRIMARY KEY ("id"))`
        );
        await queryRunner.query(`ALTER TABLE "articles" DROP COLUMN "issue_page_id"`);
        await queryRunner.query(
            `ALTER TABLE "article_issue_pages" ADD CONSTRAINT "FK_26794ac799d0fbbc44cacd945fb" FOREIGN KEY ("created_by") REFERENCES "users"("id") ON DELETE RESTRICT ON UPDATE NO ACTION`
        );
        await queryRunner.query(
            `ALTER TABLE "article_issue_pages" ADD CONSTRAINT "FK_5a585274ec89cb202bd5bd68faa" FOREIGN KEY ("updated_by") REFERENCES "users"("id") ON DELETE RESTRICT ON UPDATE NO ACTION`
        );
        await queryRunner.query(
            `ALTER TABLE "article_issue_pages" ADD CONSTRAINT "FK_ac25ffe3a03a54242a034d1cfc9" FOREIGN KEY ("deleted_by") REFERENCES "users"("id") ON DELETE RESTRICT ON UPDATE NO ACTION`
        );
        await queryRunner.query(
            `ALTER TABLE "article_issue_pages" ADD CONSTRAINT "FK_79428db5795802f2b9d9fb428e3" FOREIGN KEY ("article_id") REFERENCES "articles"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`
        );
        await queryRunner.query(
            `ALTER TABLE "article_issue_pages" ADD CONSTRAINT "FK_0a8227d5db2f3bc1150f62c3502" FOREIGN KEY ("issue_page_id") REFERENCES "issue_pages"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`
        );
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "article_issue_pages" DROP CONSTRAINT "FK_0a8227d5db2f3bc1150f62c3502"`);
        await queryRunner.query(`ALTER TABLE "article_issue_pages" DROP CONSTRAINT "FK_79428db5795802f2b9d9fb428e3"`);
        await queryRunner.query(`ALTER TABLE "article_issue_pages" DROP CONSTRAINT "FK_ac25ffe3a03a54242a034d1cfc9"`);
        await queryRunner.query(`ALTER TABLE "article_issue_pages" DROP CONSTRAINT "FK_5a585274ec89cb202bd5bd68faa"`);
        await queryRunner.query(`ALTER TABLE "article_issue_pages" DROP CONSTRAINT "FK_26794ac799d0fbbc44cacd945fb"`);
        await queryRunner.query(`ALTER TABLE "articles" ADD "issue_page_id" integer`);
        await queryRunner.query(`DROP TABLE "article_issue_pages"`);
        await queryRunner.query(
            `ALTER TABLE "articles" ADD CONSTRAINT "FK_1df99adda643979e7f448223b51" FOREIGN KEY ("issue_page_id") REFERENCES "issue_pages"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`
        );
    }
}
