import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddTitleStatisticToArticleRoyalties1753851700000 implements MigrationInterface {
    name = 'AddTitleStatisticToArticleRoyalties1753851700000';

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "article_royalties" ADD "title" character varying`);
        await queryRunner.query(`ALTER TABLE "article_royalties" ADD "article_statistic" integer DEFAULT '0'`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "article_royalties" DROP COLUMN "article_statistic"`);
        await queryRunner.query(`ALTER TABLE "article_royalties" DROP COLUMN "title"`);
    }
}
