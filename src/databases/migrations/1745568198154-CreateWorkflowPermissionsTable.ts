import { MigrationInterface, QueryRunner } from 'typeorm';

export class CreateWorkflowPermissionsTable1745568198154 implements MigrationInterface {
    name = 'CreateWorkflowPermissionsTable1745568198154';

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(
            `CREATE TABLE "workflow_permission_article_types" ("id" SERIAL NOT NULL, "workflow_permission_id" integer NOT NULL, "article_type_id" smallint NOT NULL, CONSTRAINT "UQ_3b3d49cb576ebfb225fe3535a68" UNIQUE ("article_type_id"), CONSTRAINT "PK_0b2bf83d7e0f2c63d5d1a2a7434" PRIMARY KEY ("id"))`
        );
        await queryRunner.query(
            `CREATE TABLE "workflow_permissions" ("id" SERIAL NOT NULL, "created_by" integer, "updated_by" integer, "deleted_by" integer, "created_at" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(), "updated_at" TIMESTAMP WITH TIME ZONE DEFAULT now(), "deleted_at" TIMESTAMP WITH TIME ZONE, "name" character varying NOT NULL, "status_id" smallint NOT NULL, "desc" character varying, "department_id" integer NOT NULL, "workflow_ids" jsonb NOT NULL, "workflow_transitions" jsonb NOT NULL, CONSTRAINT "PK_4a0caca4d6a7d2b1583e55cafc5" PRIMARY KEY ("id"))`
        );
        await queryRunner.query(`ALTER TABLE "workflows" DROP COLUMN "article_type_id"`);
        await queryRunner.query(
            `ALTER TABLE "workflow_permission_article_types" ADD CONSTRAINT "FK_390b8c36587e4acb9c71ae4a9c7" FOREIGN KEY ("workflow_permission_id") REFERENCES "workflow_permissions"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`
        );
        await queryRunner.query(
            `ALTER TABLE "workflow_permissions" ADD CONSTRAINT "FK_f9a8b6faaf9ce7ba769f75d52fa" FOREIGN KEY ("created_by") REFERENCES "users"("id") ON DELETE RESTRICT ON UPDATE NO ACTION`
        );
        await queryRunner.query(
            `ALTER TABLE "workflow_permissions" ADD CONSTRAINT "FK_5af179bf3d0e7ee789726637eae" FOREIGN KEY ("updated_by") REFERENCES "users"("id") ON DELETE RESTRICT ON UPDATE NO ACTION`
        );
        await queryRunner.query(
            `ALTER TABLE "workflow_permissions" ADD CONSTRAINT "FK_dfc13894c39656e4cadc2946ed4" FOREIGN KEY ("deleted_by") REFERENCES "users"("id") ON DELETE RESTRICT ON UPDATE NO ACTION`
        );
        await queryRunner.query(
            `ALTER TABLE "workflow_permissions" ADD CONSTRAINT "FK_c0cb28daeee60b081e4275b04cc" FOREIGN KEY ("department_id") REFERENCES "departments"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`
        );
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "workflow_permissions" DROP CONSTRAINT "FK_c0cb28daeee60b081e4275b04cc"`);
        await queryRunner.query(`ALTER TABLE "workflow_permissions" DROP CONSTRAINT "FK_dfc13894c39656e4cadc2946ed4"`);
        await queryRunner.query(`ALTER TABLE "workflow_permissions" DROP CONSTRAINT "FK_5af179bf3d0e7ee789726637eae"`);
        await queryRunner.query(`ALTER TABLE "workflow_permissions" DROP CONSTRAINT "FK_f9a8b6faaf9ce7ba769f75d52fa"`);
        await queryRunner.query(
            `ALTER TABLE "workflow_permission_article_types" DROP CONSTRAINT "FK_390b8c36587e4acb9c71ae4a9c7"`
        );
        await queryRunner.query(`ALTER TABLE "workflows" ADD "article_type_id" smallint NOT NULL`);
        await queryRunner.query(`DROP TABLE "workflow_permissions"`);
        await queryRunner.query(`DROP TABLE "workflow_permission_article_types"`);
    }
}
