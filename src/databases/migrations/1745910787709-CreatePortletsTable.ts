import { MigrationInterface, QueryRunner } from 'typeorm';

export class CreatePortletsTable1745910787709 implements MigrationInterface {
    name = 'CreatePortletsTable1745910787709';

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(
            `CREATE TABLE "portlets" ("id" SERIAL NOT NULL, "created_by" integer, "updated_by" integer, "deleted_by" integer, "created_at" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(), "updated_at" TIMESTAMP WITH TIME ZONE DEFAULT now(), "deleted_at" TIMESTAMP WITH TIME ZONE, "name" character varying NOT NULL, "code" character varying NOT NULL, "sql" character varying NOT NULL, "desc" character varying, "content" text NOT NULL, "status_id" smallint NOT NULL, "layout_type_id" smallint NOT NULL, "department_id" integer NOT NULL, CONSTRAINT "PK_c7319b2b87d01cc72f5cf44e4b9" PRIMARY KEY ("id"))`
        );
        await queryRunner.query(
            `ALTER TABLE "portlets" ADD CONSTRAINT "FK_e58036ae57348e99cfe5a0cb1dc" FOREIGN KEY ("created_by") REFERENCES "users"("id") ON DELETE RESTRICT ON UPDATE NO ACTION`
        );
        await queryRunner.query(
            `ALTER TABLE "portlets" ADD CONSTRAINT "FK_93988d6d521193f08cb4d8ebc34" FOREIGN KEY ("updated_by") REFERENCES "users"("id") ON DELETE RESTRICT ON UPDATE NO ACTION`
        );
        await queryRunner.query(
            `ALTER TABLE "portlets" ADD CONSTRAINT "FK_9f35f3870dcc018c57f87d8f262" FOREIGN KEY ("deleted_by") REFERENCES "users"("id") ON DELETE RESTRICT ON UPDATE NO ACTION`
        );
        await queryRunner.query(
            `ALTER TABLE "portlets" ADD CONSTRAINT "FK_3f6f60f3216e7fae474bcc09644" FOREIGN KEY ("department_id") REFERENCES "departments"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`
        );
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "portlets" DROP CONSTRAINT "FK_3f6f60f3216e7fae474bcc09644"`);
        await queryRunner.query(`ALTER TABLE "portlets" DROP CONSTRAINT "FK_9f35f3870dcc018c57f87d8f262"`);
        await queryRunner.query(`ALTER TABLE "portlets" DROP CONSTRAINT "FK_93988d6d521193f08cb4d8ebc34"`);
        await queryRunner.query(`ALTER TABLE "portlets" DROP CONSTRAINT "FK_e58036ae57348e99cfe5a0cb1dc"`);
        await queryRunner.query(`DROP TABLE "portlets"`);
    }
}
