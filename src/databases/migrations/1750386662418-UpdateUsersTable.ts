import { MigrationInterface, QueryRunner } from 'typeorm';

export class UpdateUsersTable1750386662418 implements MigrationInterface {
    name = 'UpdateUsersTable1750386662418';

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "users" ADD "require_change_password" boolean NOT NULL DEFAULT false`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "users" DROP COLUMN "require_change_password"`);
    }
}
