import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddFileIdsToIssuePageFiles1751775726956 implements MigrationInterface {
    name = 'AddFileIdsToIssuePageFiles1751775726956';

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "issue_page_files" ADD "file_ids" jsonb`);

        //-- Update existing trigger function to also check file_ids
        await queryRunner.query(`
            CREATE OR REPLACE FUNCTION check_issue_page_ids()
            RETURNS TRIGGER AS $$
            BEGIN
                -- Check issue_page_id array
                IF NEW.issue_page_id IS NOT NULL THEN
                    PERFORM 1 FROM jsonb_array_elements(NEW.issue_page_id) AS elem(value)
                    WHERE (value)::int NOT IN (
                        SELECT id FROM issue_pages WHERE issue_id = NEW.issue_id
                    );

                    IF FOUND THEN
                        RAISE EXCEPTION 'Issue page ids not found';
                    END IF;
                END IF;

                -- Check file_ids array
                IF NEW.file_ids IS NOT NULL THEN
                    PERFORM 1 FROM jsonb_array_elements(NEW.file_ids) AS elem(value)
                    WHERE (value)::int NOT IN (
                        SELECT id FROM files
                    );

                    IF FOUND THEN
                        RAISE EXCEPTION 'File ids not found';
                    END IF;
                END IF;

                RETURN NEW;
            END;
            $$ LANGUAGE plpgsql;
        `);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "issue_page_files" DROP COLUMN "file_ids"`);

        //-- Restore original trigger function without file_ids check
        await queryRunner.query(`
            CREATE OR REPLACE FUNCTION check_issue_page_ids()
            RETURNS TRIGGER AS $$
            BEGIN
                IF NEW.issue_page_id IS NOT NULL THEN
                    PERFORM 1 FROM jsonb_array_elements(NEW.issue_page_id) AS elem(value)
                    WHERE (value)::int NOT IN (
                        SELECT id FROM issue_pages WHERE issue_id = NEW.issue_id
                    );

                    IF FOUND THEN
                        RAISE EXCEPTION 'Issue page ids not found';
                    END IF;
                END IF;
                RETURN NEW;
            END;
            $$ LANGUAGE plpgsql;
        `);
    }
}
