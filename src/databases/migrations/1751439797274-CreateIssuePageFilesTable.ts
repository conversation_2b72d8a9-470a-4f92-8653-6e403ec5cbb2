import { MigrationInterface, QueryRunner } from 'typeorm';

export class CreateIssuePageFilesTable1751439797274 implements MigrationInterface {
    name = 'CreateIssuePageFilesTable1751439797274';

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(
            `CREATE TABLE "issue_page_files" ("id" SERIAL NOT NULL, "created_by" integer, "updated_by" integer, "deleted_by" integer, "created_at" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(), "updated_at" TIMESTAMP WITH TIME ZONE DEFAULT now(), "deleted_at" TIMESTAMP WITH TIME ZONE, "issue_id" integer NOT NULL, "issue_page_id" jsonb, "file_id" integer, "comment" character varying, "file_comments" jsonb, CONSTRAINT "PK_bf77e5cec9497d4b8e1ef70a7a4" PRIMARY KEY ("id"))`
        );
        await queryRunner.query(
            `ALTER TABLE "issue_page_files" ADD CONSTRAINT "FK_624a3cab1b9d66b8cdc9e771843" FOREIGN KEY ("created_by") REFERENCES "users"("id") ON DELETE RESTRICT ON UPDATE NO ACTION`
        );
        await queryRunner.query(
            `ALTER TABLE "issue_page_files" ADD CONSTRAINT "FK_298742f9b1b423d0b97ddcc4709" FOREIGN KEY ("updated_by") REFERENCES "users"("id") ON DELETE RESTRICT ON UPDATE NO ACTION`
        );
        await queryRunner.query(
            `ALTER TABLE "issue_page_files" ADD CONSTRAINT "FK_00a204cf921dd6c1a397e7dd1fa" FOREIGN KEY ("deleted_by") REFERENCES "users"("id") ON DELETE RESTRICT ON UPDATE NO ACTION`
        );
        await queryRunner.query(
            `ALTER TABLE "issue_page_files" ADD CONSTRAINT "FK_b5319274f78cabecc746a23f450" FOREIGN KEY ("issue_id") REFERENCES "issues"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`
        );
        await queryRunner.query(
            `ALTER TABLE "issue_page_files" ADD CONSTRAINT "FK_1d249c8b722f603886995e891fc" FOREIGN KEY ("file_id") REFERENCES "files"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`
        );
        //-- Trigger để kiểm tra issue_page_ids tồn tại
        await queryRunner.query(`
            CREATE OR REPLACE FUNCTION check_issue_page_ids()
            RETURNS TRIGGER AS $$
            BEGIN
                IF NEW.issue_page_id IS NOT NULL THEN
                    PERFORM 1 FROM jsonb_array_elements(NEW.issue_page_id) AS elem(value)
                    WHERE (value)::int NOT IN (
                        SELECT id FROM issue_pages WHERE issue_id = NEW.issue_id
                    );

                    IF FOUND THEN
                        RAISE EXCEPTION 'Issue page ids not found';
                    END IF;
                END IF;
                RETURN NEW;
            END;
            $$ LANGUAGE plpgsql;
        `);

        await queryRunner.query(`
            CREATE TRIGGER check_issue_page_ids
            BEFORE INSERT OR UPDATE ON issue_page_files
            FOR EACH ROW EXECUTE PROCEDURE check_issue_page_ids();
        `);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        // Xóa trigger và function
        await queryRunner.query(`DROP TRIGGER IF EXISTS check_issue_page_ids ON issue_page_files`);
        await queryRunner.query(`DROP FUNCTION IF EXISTS check_issue_page_ids`);
        // Xóa bảng
        await queryRunner.query(`ALTER TABLE "issue_page_files" DROP CONSTRAINT "FK_1d249c8b722f603886995e891fc"`);
        await queryRunner.query(`ALTER TABLE "issue_page_files" DROP CONSTRAINT "FK_b5319274f78cabecc746a23f450"`);
        await queryRunner.query(`ALTER TABLE "issue_page_files" DROP CONSTRAINT "FK_00a204cf921dd6c1a397e7dd1fa"`);
        await queryRunner.query(`ALTER TABLE "issue_page_files" DROP CONSTRAINT "FK_298742f9b1b423d0b97ddcc4709"`);
        await queryRunner.query(`ALTER TABLE "issue_page_files" DROP CONSTRAINT "FK_624a3cab1b9d66b8cdc9e771843"`);
        await queryRunner.query(`DROP TABLE "issue_page_files"`);
    }
}
