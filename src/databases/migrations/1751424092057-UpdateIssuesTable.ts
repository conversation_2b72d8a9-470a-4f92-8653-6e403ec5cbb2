import { MigrationInterface, QueryRunner } from 'typeorm';

export class UpdateIssuesTable1751424092057 implements MigrationInterface {
    name = 'UpdateIssuesTable1751424092057';

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "issues" ADD "publish_date" TIMESTAMP WITH TIME ZONE`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "issues" DROP COLUMN "publish_date"`);
    }
}
