import { MigrationInterface, QueryRunner } from 'typeorm';

export class CreateFoldersTable1748245538978 implements MigrationInterface {
    name = 'CreateFoldersTable1748245538978';

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(
            `CREATE TABLE "folders" ("id" SERIAL NOT NULL, "created_by" integer, "updated_by" integer, "deleted_by" integer, "created_at" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(), "updated_at" TIMESTAMP WITH TIME ZONE DEFAULT now(), "deleted_at" TIMESTAMP WITH TIME ZONE, "name" character varying NOT NULL, "parent_id" integer, "department_id" integer NOT NULL, CONSTRAINT "PK_8578bd31b0e7f6d6c2480dbbca8" PRIMARY KEY ("id"))`
        );
        await queryRunner.query(`ALTER TABLE "files" ADD "folder_id" integer`);
        await queryRunner.query(
            `ALTER TABLE "folders" ADD CONSTRAINT "FK_dc9b0a1095e7d48ca27df340faa" FOREIGN KEY ("created_by") REFERENCES "users"("id") ON DELETE RESTRICT ON UPDATE NO ACTION`
        );
        await queryRunner.query(
            `ALTER TABLE "folders" ADD CONSTRAINT "FK_1028720cd998ada727c1042ed30" FOREIGN KEY ("updated_by") REFERENCES "users"("id") ON DELETE RESTRICT ON UPDATE NO ACTION`
        );
        await queryRunner.query(
            `ALTER TABLE "folders" ADD CONSTRAINT "FK_c7617ed8a30f89e196e3f9918fc" FOREIGN KEY ("deleted_by") REFERENCES "users"("id") ON DELETE RESTRICT ON UPDATE NO ACTION`
        );
        await queryRunner.query(
            `ALTER TABLE "folders" ADD CONSTRAINT "FK_938a930768697b6ece215667d8e" FOREIGN KEY ("parent_id") REFERENCES "folders"("id") ON DELETE CASCADE ON UPDATE NO ACTION`
        );
        await queryRunner.query(
            `ALTER TABLE "folders" ADD CONSTRAINT "FK_d12db6e12473e6954caebb8a269" FOREIGN KEY ("department_id") REFERENCES "departments"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`
        );
        await queryRunner.query(
            `ALTER TABLE "files" ADD CONSTRAINT "FK_27bc84e6954d2fa309a4f61326f" FOREIGN KEY ("folder_id") REFERENCES "folders"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`
        );
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "files" DROP CONSTRAINT "FK_27bc84e6954d2fa309a4f61326f"`);
        await queryRunner.query(`ALTER TABLE "folders" DROP CONSTRAINT "FK_d12db6e12473e6954caebb8a269"`);
        await queryRunner.query(`ALTER TABLE "folders" DROP CONSTRAINT "FK_938a930768697b6ece215667d8e"`);
        await queryRunner.query(`ALTER TABLE "folders" DROP CONSTRAINT "FK_c7617ed8a30f89e196e3f9918fc"`);
        await queryRunner.query(`ALTER TABLE "folders" DROP CONSTRAINT "FK_1028720cd998ada727c1042ed30"`);
        await queryRunner.query(`ALTER TABLE "folders" DROP CONSTRAINT "FK_dc9b0a1095e7d48ca27df340faa"`);
        await queryRunner.query(`ALTER TABLE "files" DROP COLUMN "folder_id"`);
        await queryRunner.query(`DROP TABLE "folders"`);
    }
}
