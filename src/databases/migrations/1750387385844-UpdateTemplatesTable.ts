import { MigrationInterface, QueryRunner } from 'typeorm';

export class UpdateTemplatesTable1750387385844 implements MigrationInterface {
    name = 'UpdateTemplatesTable1750387385844';

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "templates" DROP CONSTRAINT "FK_f60bcbb5fe6be82cb16239e90f2"`);
        await queryRunner.query(`ALTER TABLE "templates" DROP COLUMN "department_id"`);
        await queryRunner.query(`ALTER TABLE "templates" ADD "type_id" smallint NOT NULL DEFAULT '1'`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "templates" DROP COLUMN "type_id"`);
        await queryRunner.query(`ALTER TABLE "templates" ADD "department_id" integer NOT NULL`);
        await queryRunner.query(
            `ALTER TABLE "templates" ADD CONSTRAINT "FK_f60bcbb5fe6be82cb16239e90f2" FOREIGN KEY ("department_id") REFERENCES "departments"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`
        );
    }
}
