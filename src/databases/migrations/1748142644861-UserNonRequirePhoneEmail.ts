import { MigrationInterface, QueryRunner } from 'typeorm';

export class UserNonRequirePhoneEmail1748142644861 implements MigrationInterface {
    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE users ALTER COLUMN phone DROP NOT NULL`);
        await queryRunner.query(`ALTER TABLE users ALTER COLUMN email DROP NOT NULL`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE users ALTER COLUMN phone SET NOT NULL`);
        await queryRunner.query(`ALTER TABLE users ALTER COLUMN email SET NOT NULL`);
    }
}
