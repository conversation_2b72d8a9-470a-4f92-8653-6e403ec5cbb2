import { MigrationInterface, QueryRunner } from 'typeorm';
import { ItemStatus } from '../../commons/enums.common';

export class UpdateIssuePagesTable1751352741056 implements MigrationInterface {
    name = 'UpdateIssuePagesTable1751352741056';

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(
            `ALTER TABLE "issue_pages" ADD "approve_status_id" smallint NOT NULL DEFAULT '${ItemStatus.PENDING}'`
        );
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "issue_pages" DROP COLUMN "approve_status_id"`);
    }
}
