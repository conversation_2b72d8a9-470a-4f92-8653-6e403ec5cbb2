import { MigrationInterface, QueryRunner } from 'typeorm';

export class CreateArticleRoyaltiesTable1752826344185 implements MigrationInterface {
    name = 'CreateArticleRoyaltiesTable1752826344185';

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(
            `CREATE TABLE "article_royalties" ("id" SERIAL NOT NULL, "created_by" integer, "updated_by" integer, "deleted_by" integer, "created_at" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(), "updated_at" TIMESTAMP WITH TIME ZONE DEFAULT now(), "deleted_at" TIMESTAMP WITH TIME ZONE, "article_id" integer NOT NULL, "type_id" smallint NOT NULL, "royalty_type_id" integer, "suggest_royalty" integer NOT NULL, CONSTRAINT "PK_article_royalties_id" PRIMARY KEY ("id"))`
        );
        await queryRunner.query(
            `ALTER TABLE "article_royalties" ADD CONSTRAINT "FK_article_royalties_created_by" FOREIGN KEY ("created_by") REFERENCES "users"("id") ON DELETE RESTRICT ON UPDATE NO ACTION`
        );
        await queryRunner.query(
            `ALTER TABLE "article_royalties" ADD CONSTRAINT "FK_article_royalties_updated_by" FOREIGN KEY ("updated_by") REFERENCES "users"("id") ON DELETE RESTRICT ON UPDATE NO ACTION`
        );
        await queryRunner.query(
            `ALTER TABLE "article_royalties" ADD CONSTRAINT "FK_article_royalties_deleted_by" FOREIGN KEY ("deleted_by") REFERENCES "users"("id") ON DELETE RESTRICT ON UPDATE NO ACTION`
        );
        await queryRunner.query(
            `ALTER TABLE "article_royalties" ADD CONSTRAINT "FK_article_royalties_article_id" FOREIGN KEY ("article_id") REFERENCES "articles"("id") ON DELETE CASCADE ON UPDATE NO ACTION`
        );
        await queryRunner.query(
            `ALTER TABLE "article_royalties" ADD CONSTRAINT "FK_article_royalties_royalty_type_id" FOREIGN KEY ("royalty_type_id") REFERENCES "article_royalty_types"("id") ON DELETE SET NULL ON UPDATE NO ACTION`
        );
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(
            `ALTER TABLE "article_royalties" DROP CONSTRAINT "FK_article_royalties_royalty_type_id"`
        );
        await queryRunner.query(`ALTER TABLE "article_royalties" DROP CONSTRAINT "FK_article_royalties_article_id"`);
        await queryRunner.query(`ALTER TABLE "article_royalties" DROP CONSTRAINT "FK_article_royalties_deleted_by"`);
        await queryRunner.query(`ALTER TABLE "article_royalties" DROP CONSTRAINT "FK_article_royalties_updated_by"`);
        await queryRunner.query(`ALTER TABLE "article_royalties" DROP CONSTRAINT "FK_article_royalties_created_by"`);
        await queryRunner.query(`DROP TABLE "article_royalties"`);
    }
}
