import { MigrationInterface, QueryRunner } from 'typeorm';

export class UpdateAdvItemsTable1747362864761 implements MigrationInterface {
    name = 'UpdateAdvItemsTable1747362864761';

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "adv_items" ALTER COLUMN "end_date" DROP NOT NULL`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "adv_items" ALTER COLUMN "end_date" SET NOT NULL`);
    }
}
