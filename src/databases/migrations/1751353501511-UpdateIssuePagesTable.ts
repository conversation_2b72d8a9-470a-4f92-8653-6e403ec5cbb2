import { MigrationInterface, QueryRunner } from 'typeorm';

export class UpdateIssuePagesTable1751353501511 implements MigrationInterface {
    name = 'UpdateIssuePagesTable1751353501511';

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "issue_pages" ADD "file_id" integer`);
        await queryRunner.query(
            `ALTER TABLE "issue_pages" ADD CONSTRAINT "UQ_34e5f3a26ccbcccd13023f65953" UNIQUE ("file_id")`
        );
        await queryRunner.query(
            `ALTER TABLE "issue_pages" ADD CONSTRAINT "FK_34e5f3a26ccbcccd13023f65953" FOREIGN KEY ("file_id") REFERENCES "files"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`
        );
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "issue_pages" DROP CONSTRAINT "FK_34e5f3a26ccbcccd13023f65953"`);
        await queryRunner.query(`ALTER TABLE "issue_pages" DROP CONSTRAINT "UQ_34e5f3a26ccbcccd13023f65953"`);
        await queryRunner.query(`ALTER TABLE "issue_pages" DROP COLUMN "file_id"`);
    }
}
