import { MigrationInterface, QueryRunner } from 'typeorm';

export class UpdateArticlesTable1751877216750 implements MigrationInterface {
    name = 'UpdateArticlesTable1751877216750';

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "articles" ADD "is_unclassified" boolean NOT NULL DEFAULT false`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "articles" DROP COLUMN "is_unclassified"`);
    }
}
