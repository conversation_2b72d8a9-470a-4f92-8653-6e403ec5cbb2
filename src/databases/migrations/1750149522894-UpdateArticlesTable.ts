import { MigrationInterface, QueryRunner } from 'typeorm';

export class UpdateArticlesTable1750149522894 implements MigrationInterface {
    name = 'UpdateArticlesTable1750149522894';

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "articles" ALTER COLUMN "desc" DROP NOT NULL`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`UPDATE "articles" SET "desc" = '' WHERE "desc" IS NULL`);
        await queryRunner.query(`ALTER TABLE "articles" ALTER COLUMN "desc" SET NOT NULL`);
    }
}
