import { MigrationInterface, QueryRunner } from 'typeorm';

export class CreateWorkflowsTable1745224554996 implements MigrationInterface {
    name = 'CreateWorkflowsTable1745224554996';

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(
            `CREATE TABLE "workflows" ("id" SERIAL NOT NULL, "created_by" integer, "updated_by" integer, "deleted_by" integer, "created_at" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(), "updated_at" TIMESTAMP WITH TIME ZONE DEFAULT now(), "deleted_at" TIMESTAMP WITH TIME ZONE, "name" character varying NOT NULL, "status_id" smallint NOT NULL, "desc" character varying, "display_order" smallint NOT NULL, "article_type_id" smallint NOT NULL, "workflow_type_id" smallint NOT NULL, "department_id" integer NOT NULL, CONSTRAINT "PK_5b5757cc1cd86268019fef52e0c" PRIMARY KEY ("id"))`
        );
        await queryRunner.query(
            `ALTER TABLE "workflows" ADD CONSTRAINT "FK_5d7e754199da9d7bf87f810ff17" FOREIGN KEY ("created_by") REFERENCES "users"("id") ON DELETE RESTRICT ON UPDATE NO ACTION`
        );
        await queryRunner.query(
            `ALTER TABLE "workflows" ADD CONSTRAINT "FK_733e74f52a379592f051f994c76" FOREIGN KEY ("updated_by") REFERENCES "users"("id") ON DELETE RESTRICT ON UPDATE NO ACTION`
        );
        await queryRunner.query(
            `ALTER TABLE "workflows" ADD CONSTRAINT "FK_5f188cda10f8d7050b224aaaa9a" FOREIGN KEY ("deleted_by") REFERENCES "users"("id") ON DELETE RESTRICT ON UPDATE NO ACTION`
        );
        await queryRunner.query(
            `ALTER TABLE "workflows" ADD CONSTRAINT "FK_3481cdaa629c4befcf73a9b47c1" FOREIGN KEY ("department_id") REFERENCES "departments"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`
        );
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "workflows" DROP CONSTRAINT "FK_3481cdaa629c4befcf73a9b47c1"`);
        await queryRunner.query(`ALTER TABLE "workflows" DROP CONSTRAINT "FK_5f188cda10f8d7050b224aaaa9a"`);
        await queryRunner.query(`ALTER TABLE "workflows" DROP CONSTRAINT "FK_733e74f52a379592f051f994c76"`);
        await queryRunner.query(`ALTER TABLE "workflows" DROP CONSTRAINT "FK_5d7e754199da9d7bf87f810ff17"`);
        await queryRunner.query(`DROP TABLE "workflows"`);
    }
}
