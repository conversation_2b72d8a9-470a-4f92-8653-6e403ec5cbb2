import { MigrationInterface, QueryRunner } from 'typeorm';

export class UpdatePortletsTable1750149924816 implements MigrationInterface {
    name = 'UpdatePortletsTable1750149924816';

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "portlets" ALTER COLUMN "sql" DROP NOT NULL`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`UPDATE "portlets" SET "sql" = '' WHERE "sql" IS NULL`);
        await queryRunner.query(`ALTER TABLE "portlets" ALTER COLUMN "sql" SET NOT NULL`);
    }
}
