import { MigrationInterface, QueryRunner } from 'typeorm';

export class CreatePressPublicationsAndIssuesTable1750131318198 implements MigrationInterface {
    name = 'CreatePressPublicationsAndIssuesTable1750131318198';

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(
            `CREATE TABLE "press_publications" ("id" SERIAL NOT NULL, "created_by" integer, "updated_by" integer, "deleted_by" integer, "created_at" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(), "updated_at" TIMESTAMP WITH TIME ZONE DEFAULT now(), "deleted_at" TIMESTAMP WITH TIME ZONE, "name" character varying NOT NULL, "status_id" smallint NOT NULL, "display_order" smallint NOT NULL, "avatar_id" integer, "issue_title" character varying, "issue_status_id" smallint NOT NULL, "issue_type_id" smallint NOT NULL, "issue_days" json NOT NULL DEFAULT '[1,2]', "issue_offset" smallint NOT NULL, "department_id" integer NOT NULL, CONSTRAINT "REL_90f658d31a751a83f6b98dcf8b" UNIQUE ("avatar_id"), CONSTRAINT "PK_23cfbf600770ceae0831dd86dfc" PRIMARY KEY ("id"))`
        );
        await queryRunner.query(
            `CREATE TABLE "issues" ("id" SERIAL NOT NULL, "created_by" integer, "updated_by" integer, "deleted_by" integer, "created_at" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(), "updated_at" TIMESTAMP WITH TIME ZONE DEFAULT now(), "deleted_at" TIMESTAMP WITH TIME ZONE, "name" character varying NOT NULL, "status_id" smallint NOT NULL, "avatar_id" integer, "file_id" integer, "press_publication_id" integer NOT NULL, "year_count" smallint NOT NULL, "all_count" smallint NOT NULL, "department_id" integer NOT NULL, CONSTRAINT "REL_cc639091aa2fac0d140990bd9a" UNIQUE ("avatar_id"), CONSTRAINT "REL_245dba7c5684a3593639b338f2" UNIQUE ("file_id"), CONSTRAINT "PK_9d8ecbbeff46229c700f0449257" PRIMARY KEY ("id"))`
        );
        await queryRunner.query(
            `ALTER TABLE "press_publications" ADD CONSTRAINT "FK_22de3670fea6eb424f66e062d8d" FOREIGN KEY ("created_by") REFERENCES "users"("id") ON DELETE RESTRICT ON UPDATE NO ACTION`
        );
        await queryRunner.query(
            `ALTER TABLE "press_publications" ADD CONSTRAINT "FK_4838492d771f20996ffa4610ee3" FOREIGN KEY ("updated_by") REFERENCES "users"("id") ON DELETE RESTRICT ON UPDATE NO ACTION`
        );
        await queryRunner.query(
            `ALTER TABLE "press_publications" ADD CONSTRAINT "FK_f32562a2d43363d9615a7b4d279" FOREIGN KEY ("deleted_by") REFERENCES "users"("id") ON DELETE RESTRICT ON UPDATE NO ACTION`
        );
        await queryRunner.query(
            `ALTER TABLE "press_publications" ADD CONSTRAINT "FK_77ec8431fb01697df1b72d71d12" FOREIGN KEY ("department_id") REFERENCES "departments"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`
        );
        await queryRunner.query(
            `ALTER TABLE "press_publications" ADD CONSTRAINT "FK_90f658d31a751a83f6b98dcf8b6" FOREIGN KEY ("avatar_id") REFERENCES "files"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`
        );
        await queryRunner.query(
            `ALTER TABLE "issues" ADD CONSTRAINT "FK_ca98dc94d6ee237326250c75ac3" FOREIGN KEY ("created_by") REFERENCES "users"("id") ON DELETE RESTRICT ON UPDATE NO ACTION`
        );
        await queryRunner.query(
            `ALTER TABLE "issues" ADD CONSTRAINT "FK_1771afd0fac84983590ac7bd98d" FOREIGN KEY ("updated_by") REFERENCES "users"("id") ON DELETE RESTRICT ON UPDATE NO ACTION`
        );
        await queryRunner.query(
            `ALTER TABLE "issues" ADD CONSTRAINT "FK_7aaf58835f6cadafde84860fe49" FOREIGN KEY ("deleted_by") REFERENCES "users"("id") ON DELETE RESTRICT ON UPDATE NO ACTION`
        );
        await queryRunner.query(
            `ALTER TABLE "issues" ADD CONSTRAINT "FK_286aec091d99143168ae72e6a03" FOREIGN KEY ("press_publication_id") REFERENCES "press_publications"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`
        );
        await queryRunner.query(
            `ALTER TABLE "issues" ADD CONSTRAINT "FK_5162da56cfedf8baab7515f7cc0" FOREIGN KEY ("department_id") REFERENCES "departments"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`
        );
        await queryRunner.query(
            `ALTER TABLE "issues" ADD CONSTRAINT "FK_cc639091aa2fac0d140990bd9af" FOREIGN KEY ("avatar_id") REFERENCES "files"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`
        );
        await queryRunner.query(
            `ALTER TABLE "issues" ADD CONSTRAINT "FK_245dba7c5684a3593639b338f2c" FOREIGN KEY ("file_id") REFERENCES "files"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`
        );
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "issues" DROP CONSTRAINT "FK_245dba7c5684a3593639b338f2c"`);
        await queryRunner.query(`ALTER TABLE "issues" DROP CONSTRAINT "FK_cc639091aa2fac0d140990bd9af"`);
        await queryRunner.query(`ALTER TABLE "issues" DROP CONSTRAINT "FK_5162da56cfedf8baab7515f7cc0"`);
        await queryRunner.query(`ALTER TABLE "issues" DROP CONSTRAINT "FK_286aec091d99143168ae72e6a03"`);
        await queryRunner.query(`ALTER TABLE "issues" DROP CONSTRAINT "FK_7aaf58835f6cadafde84860fe49"`);
        await queryRunner.query(`ALTER TABLE "issues" DROP CONSTRAINT "FK_1771afd0fac84983590ac7bd98d"`);
        await queryRunner.query(`ALTER TABLE "issues" DROP CONSTRAINT "FK_ca98dc94d6ee237326250c75ac3"`);
        await queryRunner.query(`ALTER TABLE "press_publications" DROP CONSTRAINT "FK_90f658d31a751a83f6b98dcf8b6"`);
        await queryRunner.query(`ALTER TABLE "press_publications" DROP CONSTRAINT "FK_77ec8431fb01697df1b72d71d12"`);
        await queryRunner.query(`ALTER TABLE "press_publications" DROP CONSTRAINT "FK_f32562a2d43363d9615a7b4d279"`);
        await queryRunner.query(`ALTER TABLE "press_publications" DROP CONSTRAINT "FK_4838492d771f20996ffa4610ee3"`);
        await queryRunner.query(`ALTER TABLE "press_publications" DROP CONSTRAINT "FK_22de3670fea6eb424f66e062d8d"`);
        await queryRunner.query(`DROP TABLE "issues"`);
        await queryRunner.query(`DROP TABLE "press_publications"`);
    }
}
