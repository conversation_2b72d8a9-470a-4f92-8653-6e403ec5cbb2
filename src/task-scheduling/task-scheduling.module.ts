import { Module } from '@nestjs/common';
import { PressPublicationsModule } from '../modules/press-publications/press-publications.module';
import { AutoCreateIssuesService } from './tasks/auto-create-issues.service';
import { PublishArticleService } from './tasks/publish-article.service';

@Module({
    providers: [AutoCreateIssuesService, PublishArticleService],
    imports: [PressPublicationsModule],
})
export class TaskSchedulingModule {}
