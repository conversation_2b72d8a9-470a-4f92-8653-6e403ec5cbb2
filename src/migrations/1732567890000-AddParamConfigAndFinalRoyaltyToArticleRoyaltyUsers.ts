import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddParamConfigAndFinalRoyaltyToArticleRoyaltyUsers1732567890000 implements MigrationInterface {
    name = 'AddParamConfigAndFinalRoyaltyToArticleRoyaltyUsers1732567890000';

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`
            ALTER TABLE \`article_royalty_users\` 
            ADD COLUMN \`param_config\` JSON NULL COMMENT 'Array of objects with value and label properties'
        `);

        await queryRunner.query(`
            ALTER TABLE \`article_royalty_users\` 
            ADD COLUMN \`final_royalty\` INT NULL DEFAULT 0 COMMENT 'Final royalty amount'
        `);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`
            ALTER TABLE \`article_royalty_users\` 
            DROP COLUMN \`final_royalty\`
        `);

        await queryRunner.query(`
            ALTER TABLE \`article_royalty_users\` 
            DROP COLUMN \`param_config\`
        `);
    }
}
