import { MigrationInterface, QueryRunner } from 'typeorm';

export class CreateRoyaltyParamsTable1732567910000 implements MigrationInterface {
    name = 'CreateRoyaltyParamsTable1732567910000';

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`
            CREATE TABLE \`royalty_params\` (
                \`id\` int NOT NULL AUTO_INCREMENT,
                \`name\` varchar(255) NOT NULL,
                \`desc\` varchar(255) NULL,
                \`values\` json NOT NULL,
                \`status_id\` smallint NOT NULL DEFAULT 1,
                \`display_order\` smallint NOT NULL DEFAULT 0,
                \`department_id\` int NOT NULL,
                \`created_by\` int NOT NULL,
                \`created_at\` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6),
                \`updated_by\` int NULL,
                \`updated_at\` datetime(6) NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6),
                \`deleted_by\` int NULL,
                \`deleted_at\` datetime(6) NULL,
                PRIMARY KEY (\`id\`),
                CONSTRAINT \`FK_royalty_params_department_id\`
                FOREIGN KEY (\`department_id\`) REFERENCES \`departments\`(\`id\`)
                ON DELETE RESTRICT ON UPDATE CASCADE
            ) ENGINE=InnoDB
        `);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`
            ALTER TABLE \`royalty_params\`
            DROP FOREIGN KEY \`FK_royalty_params_department_id\`
        `);

        await queryRunner.query(`DROP TABLE \`royalty_params\``);
    }
}
