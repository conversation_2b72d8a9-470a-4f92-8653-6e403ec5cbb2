import { MigrationInterface, QueryRunner } from 'typeorm';

export class CreateRoyaltyParamOptionsTable1732567960000 implements MigrationInterface {
    name = 'CreateRoyaltyParamOptionsTable1732567960000';

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`
            CREATE TABLE \`royalty_param_options\` (
                \`id\` int NOT NULL AUTO_INCREMENT,
                \`name\` varchar(255) NOT NULL,
                \`value\` varchar(255) NOT NULL,
                \`ratio\` smallint NOT NULL,
                \`display_order\` smallint NOT NULL DEFAULT 0,
                \`user_ids\` json NULL,
                \`royalty_param_id\` int NOT NULL,
                \`created_by\` int NOT NULL,
                \`created_at\` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6),
                \`updated_by\` int NULL,
                \`updated_at\` datetime(6) NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6),
                \`deleted_by\` int NULL,
                \`deleted_at\` datetime(6) NULL,
                PRIMARY KEY (\`id\`),
                CONSTRAINT \`FK_royalty_param_options_royalty_param_id\` 
                FOREIGN KEY (\`royalty_param_id\`) REFERENCES \`royalty_params\`(\`id\`) 
                ON DELETE CASCADE ON UPDATE CASCADE
            ) ENGINE=InnoDB
        `);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`
            ALTER TABLE \`royalty_param_options\` 
            DROP FOREIGN KEY \`FK_royalty_param_options_royalty_param_id\`
        `);

        await queryRunner.query(`DROP TABLE \`royalty_param_options\``);
    }
}
