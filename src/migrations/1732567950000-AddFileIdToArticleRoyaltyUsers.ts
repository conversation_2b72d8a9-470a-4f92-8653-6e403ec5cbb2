import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddFileIdToArticleRoyaltyUsers1732567950000 implements MigrationInterface {
    name = 'AddFileIdToArticleRoyaltyUsers1732567950000';

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`
            ALTER TABLE \`article_royalty_users\` 
            ADD COLUMN \`file_id\` int NULL
        `);

        await queryRunner.query(`
            ALTER TABLE \`article_royalty_users\` 
            ADD CONSTRAINT \`FK_article_royalty_users_file_id\` 
            FOREIGN KEY (\`file_id\`) REFERENCES \`files\`(\`id\`) 
            ON DELETE SET NULL ON UPDATE CASCADE
        `);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`
            ALTER TABLE \`article_royalty_users\` 
            DROP FOREIGN KEY \`FK_article_royalty_users_file_id\`
        `);

        await queryRunner.query(`
            ALTER TABLE \`article_royalty_users\` 
            DROP COLUMN \`file_id\`
        `);
    }
}
