import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddSuggestRoyaltyToArticleRoyaltyUsers1732567940000 implements MigrationInterface {
    name = 'AddSuggestRoyaltyToArticleRoyaltyUsers1732567940000';

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`
            ALTER TABLE \`article_royalty_users\` 
            ADD COLUMN \`suggest_royalty\` int NULL DEFAULT 0
        `);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`
            ALTER TABLE \`article_royalty_users\` 
            DROP COLUMN \`suggest_royalty\`
        `);
    }
}
