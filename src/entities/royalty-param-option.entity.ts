import { Column, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, ManyToOne } from 'typeorm';
import { Field, Int, ObjectType } from '@nestjs/graphql';
import GraphQLJSON from 'graphql-type-json';
import { BaseEntity } from '../commons/bases/base.entity';
import { RoyaltyParamEntity } from './royalty-param.entity';

@ObjectType('royalty_param_options')
@Entity('royalty_param_options')
export class RoyaltyParamOptionEntity extends BaseEntity {
    @Column()
    @Field(() => String)
    name: string;

    @Column()
    @Field(() => String)
    value: string;

    @Column({ type: 'smallint' })
    @Field(() => Int)
    ratio: number;

    @Column({ type: 'smallint', default: 0 })
    @Field(() => Int)
    display_order: number;

    @Column({ type: 'json', nullable: true })
    @Field(() => GraphQLJSON, { nullable: true })
    user_ids?: number[];

    @Column()
    @Field(() => Int)
    royalty_param_id: number;

    /* Relationships */
    @ManyToOne(() => RoyaltyParamEntity)
    @JoinColumn({ name: 'royalty_param_id' })
    @Field(() => RoyaltyParamEntity)
    royaltyParam: RoyaltyParamEntity;
}
