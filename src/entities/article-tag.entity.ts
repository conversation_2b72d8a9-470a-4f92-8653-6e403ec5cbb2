import { <PERSON>um<PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>ToOne } from 'typeorm';
import { Field, Int, ObjectType } from '@nestjs/graphql';
import { BaseEntity } from '../commons/bases/base.entity';
import { ArticleEntity } from './article.entity';
import { TagEntity } from './tag.entity';

@ObjectType('article_tags')
@Entity('article_tags')
export class ArticleTagEntity extends BaseEntity {
    @Column()
    @Field(() => Int)
    article_id: number;

    @Column()
    @Field(() => Int)
    tag_id: number;

    @ManyToOne(() => ArticleEntity, (article) => article.articleTags)
    @JoinColumn({ name: 'article_id' })
    @Field(() => ArticleEntity)
    article: ArticleEntity;

    @ManyToOne(() => TagEntity)
    @JoinColumn({ name: 'tag_id' })
    @Field(() => TagEntity)
    tag: TagEntity;
}
