import { <PERSON>um<PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, JoinT<PERSON>, ManyToMany, ManyToOne, OneToMany } from 'typeorm';
import { Field, ObjectType } from '@nestjs/graphql';
import { BaseEntity } from '../commons/bases/base.entity';
import { GroupEntity } from './group.entity';

@ObjectType('actions')
@Entity('actions')
export class ActionEntity extends BaseEntity {
    @Column()
    @Field()
    name: string;

    @Column({ nullable: true })
    @Field({ nullable: true })
    url?: string;

    @Column({ nullable: true })
    @Field({ nullable: true })
    icon?: string;

    @Column({ nullable: true })
    @Field({ nullable: true })
    parent_id?: number;

    @Column()
    @Field()
    display_order: number;

    @OneToMany(() => ActionEntity, (action) => action.parent)
    @Field(() => [ActionEntity], { nullable: true })
    children?: ActionEntity[];

    @ManyToOne(() => ActionEntity, (action) => action.children)
    @JoinColumn({ name: 'parent_id' })
    @Field(() => ActionEntity, { nullable: true })
    parent?: ActionEntity;

    @ManyToMany(() => GroupEntity, (group) => group.actions)
    @JoinTable({
        name: 'group_actions',
        joinColumn: { name: 'action_id', referencedColumnName: 'id' },
        inverseJoinColumn: { name: 'group_id', referencedColumnName: 'id' },
    })
    @Field(() => [GroupEntity], { nullable: true })
    groups?: GroupEntity[];
}
