import { Column, <PERSON>ti<PERSON>, <PERSON><PERSON><PERSON><PERSON>umn, ManyToOne } from 'typeorm';
import { Field, Int, ObjectType } from '@nestjs/graphql';
import { BaseEntity } from '../commons/bases/base.entity';
import { UserEntity } from './user.entity';
import { DepartmentEntity } from './department.entity';
import { SearchFields } from '../commons/decorators/entity.decorators';

@ObjectType('notifications')
@Entity('notifications')
@SearchFields(['title'])
export class NotificationEntity extends BaseEntity {
    @Column({ nullable: true })
    @Field(() => Int, { nullable: true })
    from_user_id?: number;

    @Column()
    @Field(() => Int)
    to_user_id: number;

    @Column()
    @Field()
    title: string;

    @Column()
    @Field()
    content: string;

    @Column({ default: false })
    @Field()
    is_read: boolean;

    @Column()
    @Field(() => Int)
    department_id: number;

    @ManyToOne(() => UserEntity)
    @JoinColumn({ name: 'from_user_id' })
    @Field(() => UserEntity, { nullable: true })
    fromUser?: UserEntity;

    @ManyToOne(() => UserEntity)
    @JoinColumn({ name: 'to_user_id' })
    @Field(() => UserEntity)
    toUser: UserEntity;

    @ManyToOne(() => DepartmentEntity)
    @JoinColumn({ name: 'department_id' })
    @Field(() => DepartmentEntity)
    department: DepartmentEntity;
}
