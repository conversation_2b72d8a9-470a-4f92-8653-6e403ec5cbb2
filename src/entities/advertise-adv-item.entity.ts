import { Column, <PERSON>ti<PERSON>, <PERSON><PERSON><PERSON><PERSON>umn, ManyToOne } from 'typeorm';
import { Field, Int, ObjectType } from '@nestjs/graphql';
import { BaseEntity } from '../commons/bases/base.entity';
import { AdvertiseEntity } from './advertise.entity';
import { AdvItemEntity } from './adv-item.entity';

@ObjectType('advertise_adv_items')
@Entity('advertise_adv_items')
export class AdvertiseAdvItemEntity extends BaseEntity {
    @Column()
    @Field(() => Int)
    advertise_id: number;

    @Column()
    @Field(() => Int)
    adv_item_id: number;

    @Column({ type: 'smallint' })
    @Field(() => Int)
    display_order: number;

    @ManyToOne(() => AdvertiseEntity, (advertise) => advertise.advertiseAdvItems)
    @JoinColumn({ name: 'advertise_id' })
    @Field(() => AdvertiseEntity)
    advertise: AdvertiseEntity;

    @ManyToOne(() => AdvItemEntity, (advItem) => advItem.advertiseAdvItems)
    @JoinColumn({ name: 'adv_item_id' })
    @Field(() => AdvItemEntity)
    advItem: AdvItemEntity;
}
