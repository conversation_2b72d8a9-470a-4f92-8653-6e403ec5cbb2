import { Column, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>n, ManyToMany, ManyToOne, OneToMany } from 'typeorm';
import { Field, Int, ObjectType, registerEnumType } from '@nestjs/graphql';
import { BaseEntity } from '../commons/bases/base.entity';
import { ActionEntity } from './action.entity';
import { UserEntity } from './user.entity';
import { SearchFields } from '../commons/decorators/entity.decorators';
import { UserDepartmentEntity } from './user-department.entity';
import { DepartmentEntity } from './department.entity';
import { ItemStatus } from '../commons/enums.common';

export enum GroupType {
    ROLE = 1,
    DEPARTMENT = 2,
    FUNCTIONAL = 3,
}
@ObjectType('groups')
@Entity('groups')
@SearchFields(['name'])
export class GroupEntity extends BaseEntity {
    @Column()
    @Field()
    name: string;

    @Column({})
    @Field(() => Int)
    department_id: number;

    @Column({ nullable: true })
    @Field(() => Int, { nullable: true })
    parent_id: number;

    @Column({ nullable: true })
    @Field(() => String, { nullable: true })
    desc: string;

    @Column({ nullable: true, type: 'smallint', default: GroupType.ROLE })
    @Field(() => Int, { nullable: true })
    type_id: GroupType;

    @Column({ nullable: true, type: 'smallint', default: ItemStatus.PENDING })
    @Field(() => Int, { nullable: true })
    status_id: ItemStatus;

    @ManyToOne(() => GroupEntity, (folder) => folder.children, { onDelete: 'CASCADE' })
    @JoinColumn({ name: 'parent_id' })
    @Field(() => GroupEntity, { nullable: true })
    parent?: GroupEntity;

    @OneToMany(() => GroupEntity, (group) => group.parent)
    @Field(() => [GroupEntity], { nullable: true })
    children?: GroupEntity[];

    @ManyToMany(() => ActionEntity, (action) => action.groups)
    @Field(() => [ActionEntity], { nullable: true })
    actions?: ActionEntity[];

    @ManyToMany(() => UserEntity, (user) => user.groups)
    @Field(() => [UserEntity], { nullable: true })
    users?: UserEntity[];

    @ManyToOne(() => DepartmentEntity, (d) => d.groups)
    @JoinColumn({ name: 'department_id' })
    @Field(() => DepartmentEntity)
    department: DepartmentEntity;

    @ManyToMany(() => UserDepartmentEntity, (ud) => ud.groups)
    @Field(() => [UserDepartmentEntity], { nullable: true })
    userDepartments?: UserDepartmentEntity[];
}

registerEnumType(GroupType, {
    name: 'GroupType',
});
