import { <PERSON>umn, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>n, ManyToOne } from 'typeorm';
import { Field, Int, ObjectType, registerEnumType } from '@nestjs/graphql';
import { BaseIdEntity } from '../commons/bases/base-id.entity';
import { WorkflowPermissionEntity } from './workflow-permission.entity';
import { UserDepartmentEntity } from './user-department.entity';

@Entity('workflow_permission_article_types')
@ObjectType('workflow_permission_article_types')
export class WorkflowPermissionArticleTypeEntity extends BaseIdEntity {
    @Column()
    @Field(() => Int)
    workflow_permission_id: number;

    @Column({ type: 'smallint' })
    @Field(() => Int)
    article_type_id: ArticleTypes;

    @Column()
    @Field(() => Int)
    user_department_id: number;

    @ManyToOne(() => WorkflowPermissionEntity, (d) => d.articleTypes)
    @JoinColumn({ name: 'workflow_permission_id' })
    @Field(() => WorkflowPermissionEntity)
    workflowPermission: WorkflowPermissionEntity;

    @ManyToOne(() => UserDepartmentEntity, (d) => d.articleTypes)
    @JoinColumn({ name: 'user_department_id' })
    @Field(() => UserDepartmentEntity)
    userDepartment: UserDepartmentEntity;
}

export enum ArticleTypes {
    ELECTRONIC = 1,
    PAPER = 2,
    TELEVISION = 3,
    RADIO = 5,
    VIDEO = 4,
}

registerEnumType(ArticleTypes, {
    name: 'ArticleTypes',
});
