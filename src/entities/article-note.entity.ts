import { <PERSON>umn, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, ManyTo<PERSON>ne } from 'typeorm';
import { Field, Int, ObjectType } from '@nestjs/graphql';
import { BaseEntity } from '../commons/bases/base.entity';
import { ArticleEntity } from './article.entity';

@ObjectType('article_notes')
@Entity('article_notes')
export class ArticleNoteEntity extends BaseEntity {
    @Column()
    @Field(() => Int)
    article_id: number;

    @Column()
    @Field()
    content: string;

    @ManyToOne(() => ArticleEntity, (article) => article.articleNotes)
    @JoinColumn({ name: 'article_id' })
    @Field(() => ArticleEntity)
    article: ArticleEntity;
}
