import { Column, <PERSON><PERSON><PERSON>, <PERSON>in<PERSON><PERSON><PERSON>n, ManyToOne, OneToMany, OneToOne } from 'typeorm';
import { Field, GraphQLISODateTime, Int, ObjectType } from '@nestjs/graphql';
import { BaseEntity } from '../commons/bases/base.entity';
import { ItemStatus } from '../commons/enums.common';
import { PressPublicationEntity } from './press-publication.entity';
import { DepartmentEntity } from './department.entity';
import { FileEntity } from './file.entity';
import { SearchFields } from '../commons/decorators/entity.decorators';
import { IssuePageEntity } from './issue-page.entity';
import { ArticleEntity } from './article.entity';
import { IssuePageFileEntity } from './issue-page-file.entity';

@ObjectType('issues')
@Entity('issues')
@SearchFields(['name'])
export class IssueEntity extends BaseEntity {
    @Column()
    @Field()
    name: string;

    @Column({ type: 'smallint' })
    @Field(() => Int)
    status_id: ItemStatus;

    @Column({ nullable: true })
    @Field(() => Int, { nullable: true })
    avatar_id?: number;

    @Column({ nullable: true })
    @Field(() => Int, { nullable: true })
    file_id?: number;

    @Column()
    @Field(() => Int)
    press_publication_id: number;

    @Column({ type: 'smallint' })
    @Field(() => Int)
    year_count: number;

    @Column({ type: 'smallint' })
    @Field(() => Int)
    all_count: number;

    @Column()
    @Field(() => Int)
    department_id: number;

    @Column({ nullable: true, type: 'timestamptz' })
    @Field(() => GraphQLISODateTime, { nullable: true })
    publish_date?: Date;

    @ManyToOne(() => PressPublicationEntity, (pp) => pp.issues)
    @JoinColumn({ name: 'press_publication_id' })
    @Field(() => PressPublicationEntity)
    pressPublication: PressPublicationEntity;

    @ManyToOne(() => DepartmentEntity, (d) => d.issues)
    @JoinColumn({ name: 'department_id' })
    @Field(() => DepartmentEntity)
    department: DepartmentEntity;

    @OneToOne(() => FileEntity)
    @JoinColumn({ name: 'avatar_id' })
    @Field(() => FileEntity, { nullable: true })
    avatar?: FileEntity;

    @OneToOne(() => FileEntity)
    @JoinColumn({ name: 'file_id' })
    @Field(() => FileEntity, { nullable: true })
    file?: FileEntity;

    @OneToMany(() => IssuePageEntity, (issuePage) => issuePage.issue)
    @Field(() => [IssuePageEntity], { nullable: true })
    issuePages?: IssuePageEntity[];

    @OneToMany(() => ArticleEntity, (article) => article.issue)
    @Field(() => [ArticleEntity], { nullable: true })
    articles?: ArticleEntity[];

    @OneToMany(() => IssuePageFileEntity, (issuePageFile) => issuePageFile.issue)
    @Field(() => [IssuePageFileEntity], { nullable: true })
    issuePageFiles?: IssuePageFileEntity[];
}
