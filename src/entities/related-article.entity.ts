import { <PERSON>um<PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>ToOne } from 'typeorm';
import { Field, Int, ObjectType } from '@nestjs/graphql';
import { BaseEntity } from '../commons/bases/base.entity';
import { ArticleEntity } from './article.entity';

@ObjectType('related_articles')
@Entity('related_articles')
export class RelatedArticleEntity extends BaseEntity {
    @Column()
    @Field(() => Int)
    article_id: number;

    @Column()
    @Field(() => Int)
    related_article_id: number;

    @ManyToOne(() => ArticleEntity, (article) => article.relatedArticles)
    @JoinColumn({ name: 'article_id' })
    @Field(() => ArticleEntity)
    article: ArticleEntity;

    @ManyToOne(() => ArticleEntity, (article) => article.relatedToArticles)
    @JoinColumn({ name: 'related_article_id' })
    @Field(() => ArticleEntity)
    relatedArticle: ArticleEntity;
}
