import { <PERSON><PERSON>ty, Column, ManyToOne, Unique, Jo<PERSON><PERSON><PERSON>um<PERSON> } from 'typeorm';
import { UserEntity } from './user.entity';
import { Field, Int, ObjectType } from '@nestjs/graphql';
import GraphQLJSON from 'graphql-type-json';
import { BaseEntity } from '../commons/bases/base.entity';

@Entity('login_devices')
@ObjectType('login_devices')
@Unique(['user_id', 'user_agent'])
export class LoginDeviceEntity extends BaseEntity {
    @Column()
    @Field(() => Int)
    user_id: number;

    @Column({ type: 'varchar' })
    @Field(() => String)
    ip_address: string;

    @ManyToOne(() => UserEntity, (user) => user.devices)
    @JoinColumn({ name: 'user_id' })
    user: UserEntity;

    @Column({ type: 'varchar' })
    @Field(() => String)
    platform: string;

    @Column({ type: 'varchar' })
    @Field(() => String)
    os: string;

    @Column({ type: 'varchar' })
    @Field(() => String)
    browser: string;

    @Column({ type: 'varchar' })
    @Field(() => String)
    user_agent: string;

    @Column({ type: 'json' })
    @Field(() => GraphQLJSON)
    raw: object;
}
