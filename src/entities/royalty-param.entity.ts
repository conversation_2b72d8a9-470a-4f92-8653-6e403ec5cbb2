import { <PERSON>umn, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, ManyToOne } from 'typeorm';
import { Field, Int, ObjectType } from '@nestjs/graphql';
import { BaseEntity } from '../commons/bases/base.entity';
import { ItemStatus } from '../commons/enums.common';
import { DepartmentEntity } from './department.entity';
import GraphQLJSON from 'graphql-type-json';

@ObjectType('royalty_params')
@Entity('royalty_params')
export class RoyaltyParamEntity extends BaseEntity {
    @Column()
    @Field()
    name: string;

    @Column({ nullable: true })
    @Field({ nullable: true })
    desc?: string;

    @Column({ type: 'smallint' })
    @Field(() => Int)
    status_id: ItemStatus;

    @Column({ type: 'smallint', default: 0 })
    @Field(() => Int)
    display_order: number;

    @Column()
    @Field(() => Int)
    department_id: number;

    @Column({ type: 'json', nullable: true })
    @Field(() => GraphQLJSON, { nullable: true })
    article_type_ids?: number[];

    /* Relationships */
    @ManyToOne(() => DepartmentEntity)
    @JoinColumn({ name: 'department_id' })
    @Field(() => DepartmentEntity, { nullable: true })
    department?: DepartmentEntity;
}
