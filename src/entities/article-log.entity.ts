import { <PERSON>umn, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, ManyTo<PERSON>ne } from 'typeorm';
import { Field, Int, ObjectType } from '@nestjs/graphql';
import { BaseEntity } from '../commons/bases/base.entity';
import { ArticleEntity } from './article.entity';

@ObjectType('article_logs')
@Entity('article_logs')
export class ArticleLogEntity extends BaseEntity {
    @Column()
    @Field(() => Int)
    article_id: number;

    @Column({ type: 'json' })
    @Field()
    content: string;

    @ManyToOne(() => ArticleEntity, (article) => article.articleLogs)
    @JoinColumn({ name: 'article_id' })
    @Field(() => ArticleEntity)
    article: ArticleEntity;
}
