import { <PERSON>umn, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, ManyToOne } from 'typeorm';
import { Field, Int, ObjectType } from '@nestjs/graphql';
import Graph<PERSON>JSON from 'graphql-type-json';
import { BaseEntity } from '../commons/bases/base.entity';
import { ArticleRoyaltyEntity } from './article-royalty.entity';
import { UserEntity } from './user.entity';
import { FileEntity } from './file.entity';

@ObjectType('article_royalty_users')
@Entity('article_royalty_users')
export class ArticleRoyaltyUserEntity extends BaseEntity {
    @Column()
    @Field(() => Int)
    article_royalty_id: number;

    @Column()
    @Field(() => Int)
    user_id: number;

    @Column({ type: 'smallint' })
    @Field(() => Int)
    percent: number;

    @Column({ nullable: true })
    @Field({ nullable: true })
    comment?: string;

    @Column({ type: 'json', nullable: true })
    @Field(() => GraphQLJSON, { nullable: true })
    param_config?: Array<{ value: string; label: string }>;

    @Column({ type: 'int', nullable: true, default: 0 })
    @Field(() => Int, { nullable: true })
    final_royalty?: number;

    @Column({ type: 'int', nullable: true, default: 0 })
    @Field(() => Int, { nullable: true })
    suggest_royalty?: number;

    @Column({ nullable: true })
    @Field(() => Int, { nullable: true })
    file_id?: number;

    /* Relationships */
    @ManyToOne(() => ArticleRoyaltyEntity, (ar) => ar.articleRoyaltyUsers)
    @JoinColumn({ name: 'article_royalty_id' })
    @Field(() => ArticleRoyaltyEntity)
    articleRoyalty: ArticleRoyaltyEntity;

    @ManyToOne(() => UserEntity)
    @JoinColumn({ name: 'user_id' })
    @Field(() => UserEntity)
    user: UserEntity;

    @ManyToOne(() => FileEntity)
    @JoinColumn({ name: 'file_id' })
    @Field(() => FileEntity, { nullable: true })
    file?: FileEntity;
}
