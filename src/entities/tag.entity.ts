import { Column, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>n, ManyToMany, ManyToOne } from 'typeorm';
import { Field, Int, ObjectType } from '@nestjs/graphql';
import { BaseEntity } from '../commons/bases/base.entity';
import { ItemStatus } from '../commons/enums.common';
import { DepartmentEntity } from './department.entity';
import { SearchFields } from '../commons/decorators/entity.decorators';
import { FileEntity } from './file.entity';

@ObjectType('tags')
@Entity('tags')
@SearchFields(['name'])
export class TagEntity extends BaseEntity {
    @Column()
    @Field()
    name: string;

    @Column()
    @Field()
    slug: string;

    @Column({ type: 'smallint' })
    @Field(() => Int)
    status_id: ItemStatus;

    @Column()
    @Field(() => Int)
    department_id: number;

    @ManyToOne(() => DepartmentEntity, (d) => d.tags)
    @JoinColumn({ name: 'department_id' })
    @Field(() => DepartmentEntity)
    department: DepartmentEntity;

    @ManyToMany(() => FileEntity, (file) => file.tags)
    @Field(() => [FileEntity], { nullable: true })
    files?: FileEntity[];
}
