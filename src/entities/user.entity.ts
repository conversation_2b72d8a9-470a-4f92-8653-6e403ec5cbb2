import { <PERSON>um<PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, ManyToMany, OneToMany, OneToOne } from 'typeorm';
import { Field, GraphQLISODateTime, Int, ObjectType, registerEnumType } from '@nestjs/graphql';
import { BaseEntity } from '../commons/bases/base.entity';
import { FileEntity } from './file.entity';
import { GroupEntity } from './group.entity';
import { PasswordResetEntity } from './password-reset.entity';
import { PersonalTokenEntity } from './personal-token.entity';
import { SearchFields } from '../commons/decorators/entity.decorators';
import { ItemStatus } from '../commons/enums.common';
import { UserDepartmentEntity } from './user-department.entity';
import { PseudonymsEntity } from './pseudonyms.entity';
import { LoginDeviceEntity } from './device.entity';
import { FolderEntity } from './folder.entity';

@Entity('users')
@ObjectType('users')
@SearchFields(['user_name', 'full_name', 'email', 'phone'])
export class UserEntity extends BaseEntity {
    @Column({ unique: true })
    @Field()
    user_name: string;

    @Column()
    password: string;

    @Column()
    @Field()
    full_name: string;

    @Column({ unique: true, nullable: true })
    @Field({ nullable: true })
    phone?: string;

    @Column({ unique: true, nullable: true })
    @Field({ nullable: true })
    email?: string;

    @Column({ nullable: true })
    @Field({ nullable: true })
    address?: string;

    @Column({ nullable: true })
    @Field({ nullable: true })
    avatar_id?: number;

    @Column({ type: 'smallint', nullable: true })
    @Field(() => Int, { nullable: true })
    gender_id?: Genders;

    @Column({ type: 'smallint' })
    @Field(() => Int)
    role_id: UserRoles;

    @Column({ type: 'smallint' })
    @Field(() => Int)
    status_id: ItemStatus;

    @Column({ nullable: true })
    @Field({ nullable: true })
    telegram_id?: string;

    @Column({ type: 'timestamptz', nullable: true })
    @Field(() => String, { nullable: true })
    birthday?: Date;

    @Column({ type: 'boolean', default: false })
    @Field(() => Boolean)
    email_notify: boolean;

    @Column({ type: 'boolean', default: false })
    @Field(() => Boolean)
    telegram_notify: boolean;

    @Column({ type: 'boolean', default: false })
    @Field(() => Boolean)
    zalo_notify: boolean;

    @Column({ type: 'boolean', default: false })
    @Field(() => Boolean)
    email_verified: boolean;

    @Column({ type: 'boolean', default: false })
    @Field(() => Boolean)
    phone_verified: boolean;

    @Column({ type: 'timestamptz', nullable: true })
    @Field(() => GraphQLISODateTime, { nullable: true })
    login_at?: Date;

    @Column({ type: 'timestamptz', nullable: true })
    @Field(() => GraphQLISODateTime, { nullable: true })
    change_password_at?: Date;

    @Column({ type: 'boolean', default: false })
    @Field(() => Boolean)
    require_change_password: boolean;

    /* Relationships */
    @OneToOne(() => FileEntity, (f) => f.user, { nullable: true, onDelete: 'SET NULL' })
    @JoinColumn({ name: 'avatar_id' })
    @Field(() => FileEntity, { nullable: true })
    avatar?: FileEntity;

    @ManyToMany(() => GroupEntity, (group) => group.users)
    @JoinTable({
        name: 'user_groups',
        joinColumn: { name: 'user_id', referencedColumnName: 'id' },
        inverseJoinColumn: { name: 'group_id', referencedColumnName: 'id' },
    })
    @Field(() => [GroupEntity], { nullable: true })
    groups?: GroupEntity[];

    @OneToMany(() => PasswordResetEntity, (passwordReset) => passwordReset.user)
    pws?: PasswordResetEntity[];

    @OneToMany(() => PersonalTokenEntity, (personalToken) => personalToken.user)
    personal_tokens?: PersonalTokenEntity[];

    @OneToMany(() => UserDepartmentEntity, (ud) => ud.user)
    @Field(() => [UserDepartmentEntity], { nullable: true })
    userDepartments?: UserDepartmentEntity[];

    @OneToMany(() => PseudonymsEntity, (p) => p.user)
    @Field(() => [PseudonymsEntity], { nullable: true })
    pseudonyms?: PseudonymsEntity[];

    @OneToMany(() => LoginDeviceEntity, (device) => device.user)
    devices?: LoginDeviceEntity[];

    @ManyToMany(() => FolderEntity, (folder) => folder.users)
    @Field(() => [FolderEntity], { nullable: true })
    folders?: FolderEntity[];
}

export enum UserRoles {
    ADMIN = 1,
    CONTRIBUTOR = 2,
}

export enum Genders {
    MALE = 1,
    FEMALE = 2,
    OTHER = 3,
}

registerEnumType(UserRoles, {
    name: 'UserRoles',
});

registerEnumType(Genders, {
    name: 'Genders',
});
