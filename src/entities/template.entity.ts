import { Column, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, ManyToOne, OneToOne } from 'typeorm';
import { Field, Int, ObjectType, registerEnumType } from '@nestjs/graphql';
import { BaseEntity } from '../commons/bases/base.entity';
import { ItemStatus } from '../commons/enums.common';
import { DepartmentEntity } from './department.entity';
import { FileEntity } from './file.entity';
import { SearchFields } from '../commons/decorators/entity.decorators';

export enum TemplateTypes {
    CONTENT = 1,
    IMAGE = 2,
    RELATED_NEWS = 3,
    QUOTE = 4,
}

registerEnumType(TemplateTypes, {
    name: 'TemplateTypes',
});

@ObjectType('templates')
@Entity('templates')
@SearchFields(['name'])
export class TemplateEntity extends BaseEntity {
    @Column()
    @Field()
    name: string;

    @Column({ nullable: true })
    @Field({ nullable: true })
    desc?: string;

    @Column({ type: 'text' })
    @Field()
    content: string;

    @Column({ type: 'smallint' })
    @Field(() => Int)
    status_id: ItemStatus;

    @Column({ nullable: true })
    @Field(() => Int, { nullable: true })
    avatar_id?: number;

    @Column({ type: 'smallint', default: TemplateTypes.CONTENT })
    @Field(() => Int)
    type_id: TemplateTypes;

    @OneToOne(() => FileEntity)
    @JoinColumn({ name: 'avatar_id' })
    @Field(() => FileEntity, { nullable: true })
    avatar?: FileEntity;
}
