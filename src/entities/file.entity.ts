import { <PERSON>umn, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, JoinTable, ManyToMany, ManyToOne, OneToMany, OneToOne } from 'typeorm';
import { Field, Int, ObjectType } from '@nestjs/graphql';
import { UserEntity } from './user.entity';
import { pathToUrl } from '../commons/helpers/file.helper';
import { BaseEntity } from '../commons/bases/base.entity';
import { TemplateEntity } from './template.entity';
import { FolderEntity } from './folder.entity';
import { DepartmentEntity } from './department.entity';
import { SearchFields } from '../commons/decorators/entity.decorators';
import { TagEntity } from './tag.entity';
import { IssuePageEntity } from './issue-page.entity';
import { IssuePageFileEntity } from './issue-page-file.entity';

@ObjectType('files')
@Entity('files')
@SearchFields(['file_name'])
export class FileEntity extends BaseEntity {
    @Column()
    @Field()
    file_name: string;

    @Column({
        transformer: {
            to(value) {
                return value.toString();
            },
            from(value) {
                return value ? pathToUrl(value) : value;
            },
        },
    })
    @Field()
    file_url: string;

    @Column({ type: 'integer' })
    @Field()
    file_size: number;

    @Column()
    @Field()
    mime_type: string;

    @Column({ nullable: true })
    @Field(() => Int, { nullable: true })
    folder_id?: number;

    @Column()
    @Field(() => Int)
    department_id: number;

    @Column({ nullable: true })
    @Field(() => Int, { nullable: true })
    parent_id?: number;

    @Column({ type: 'boolean', default: false })
    @Field(() => Boolean)
    is_newsroom: boolean;

    @Column({ nullable: true })
    @Field({ nullable: true })
    file_title?: string;

    @ManyToOne(() => FolderEntity, (folder) => folder.files)
    @JoinColumn({ name: 'folder_id' })
    @Field(() => FolderEntity, { nullable: true })
    folder?: FolderEntity;

    @OneToOne(() => UserEntity, (u) => u.avatar)
    // @Field(() => UserEntity, { nullable: true })
    user?: UserEntity;

    @OneToOne(() => TemplateEntity, (t) => t.avatar)
    // @Field(() => TemplateEntity, { nullable: true })
    template?: TemplateEntity;

    @ManyToOne(() => DepartmentEntity, (d) => d.files)
    @JoinColumn({ name: 'department_id' })
    @Field(() => DepartmentEntity)
    department: DepartmentEntity;

    @ManyToOne(() => FileEntity, (file) => file.children, { onDelete: 'CASCADE' })
    @JoinColumn({ name: 'parent_id' })
    @Field(() => FileEntity, { nullable: true })
    parent?: FileEntity;

    @OneToMany(() => FileEntity, (file) => file.parent)
    @Field(() => [FileEntity], { nullable: true })
    children?: FileEntity[];

    @ManyToMany(() => TagEntity, (tag) => tag.files)
    @JoinTable({
        name: 'file_tags',
        joinColumn: { name: 'file_id', referencedColumnName: 'id' },
        inverseJoinColumn: { name: 'tag_id', referencedColumnName: 'id' },
    })
    @Field(() => [TagEntity], { nullable: true })
    tags?: TagEntity[];

    @OneToOne(() => IssuePageEntity, (issuePage) => issuePage.file)
    @Field(() => IssuePageEntity, { nullable: true })
    issuePage?: IssuePageEntity;

    @OneToMany(() => IssuePageFileEntity, (issuePageFile) => issuePageFile.file)
    @Field(() => [IssuePageFileEntity], { nullable: true })
    issuePageFiles?: IssuePageFileEntity[];
}
