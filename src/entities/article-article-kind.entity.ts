import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, ManyTo<PERSON>ne, PrimaryColumn } from 'typeorm';
import { Field, Int, ObjectType, registerEnumType } from '@nestjs/graphql';
import { ArticleEntity } from './article.entity';

@ObjectType('article_article_kinds')
@Entity('article_article_kinds')
export class ArticleArticleKindEntity {
    @PrimaryColumn()
    @Field(() => Number)
    article_id: number;

    @PrimaryColumn({ type: 'smallint' })
    @Field(() => Int)
    article_kind_id: ArticleKind;

    @ManyToOne(() => ArticleEntity, (article) => article.articleKinds)
    @JoinColumn({ name: 'article_id' })
    @Field(() => ArticleEntity)
    article: ArticleEntity;
}

export enum ArticleKind {
    IMAGE = 1,
    VIDEO = 2,
    AUDIO = 3,
    E_MAGAZINE = 4,
    INFOGRAPHIC = 5,
    LIVE = 6,
    TEXT_TO_SPEED = 7,
}

registerEnumType(ArticleKind, {
    name: 'ArticleK<PERSON>',
});
