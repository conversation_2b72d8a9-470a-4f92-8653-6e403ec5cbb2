import { Column, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, ManyToOne, OneToMany, OneToOne } from 'typeorm';
import { Field, Int, ObjectType } from '@nestjs/graphql';
import { BaseEntity } from '../commons/bases/base.entity';
import { ItemStatus } from '../commons/enums.common';
import { PressPublicationEntity } from './press-publication.entity';
import { IssueEntity } from './issue.entity';
import { DepartmentEntity } from './department.entity';
import { SearchFields } from '../commons/decorators/entity.decorators';
import { ArticleIssuePageEntity } from './article-issue-page.entity';
import { FileEntity } from './file.entity';

@ObjectType('issue_pages')
@Entity('issue_pages')
@SearchFields(['name'])
export class IssuePageEntity extends BaseEntity {
    @Column()
    @Field()
    name: string;

    @Column({ type: 'smallint' })
    @Field(() => Int)
    page_number: number;

    @Column({ type: 'smallint' })
    @Field(() => Int)
    status_id: ItemStatus;

    @Column()
    @Field(() => Int)
    press_publication_id: number;

    @Column()
    @Field(() => Int)
    issue_id: number;

    @Column()
    @Field(() => Int)
    department_id: number;

    @Column({ type: 'smallint', default: ItemStatus.PENDING })
    @Field(() => Int)
    approve_status_id: ItemStatus;

    @Column({ nullable: true })
    @Field(() => Int, { nullable: true })
    file_id?: number;

    @ManyToOne(() => PressPublicationEntity, (pp) => pp.issuePages)
    @JoinColumn({ name: 'press_publication_id' })
    @Field(() => PressPublicationEntity)
    pressPublication: PressPublicationEntity;

    @ManyToOne(() => IssueEntity, (issue) => issue.issuePages)
    @JoinColumn({ name: 'issue_id' })
    @Field(() => IssueEntity)
    issue: IssueEntity;

    @ManyToOne(() => DepartmentEntity, (d) => d.issuePages)
    @JoinColumn({ name: 'department_id' })
    @Field(() => DepartmentEntity)
    department: DepartmentEntity;

    @OneToMany(() => ArticleIssuePageEntity, (articleIssuePage) => articleIssuePage.issuePage)
    @Field(() => [ArticleIssuePageEntity], { nullable: true })
    articleIssuePages?: ArticleIssuePageEntity[];

    @OneToOne(() => FileEntity, (f) => f.issuePage)
    @JoinColumn({ name: 'file_id' })
    @Field(() => FileEntity, { nullable: true })
    file?: FileEntity;
}
