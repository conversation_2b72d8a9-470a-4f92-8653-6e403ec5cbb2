import { <PERSON>um<PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>ToOne } from 'typeorm';
import { Field, Int, ObjectType } from '@nestjs/graphql';
import { BaseEntity } from '../commons/bases/base.entity';
import { ItemStatus } from '../commons/enums.common';
import { UserEntity } from './user.entity';
import { SearchFields } from '../commons/decorators/entity.decorators';

@ObjectType('pseudonyms')
@Entity('pseudonyms')
@SearchFields(['name'])
export class PseudonymsEntity extends BaseEntity {
    @Column()
    @Field()
    name: string;

    @Column({ type: 'smallint' })
    @Field(() => Int)
    status_id: ItemStatus;

    @Column({ type: 'boolean', default: false })
    @Field(() => Boolean, { defaultValue: false })
    is_default: boolean;

    @Column()
    @Field(() => Int)
    user_id: number;

    @ManyToOne(() => UserEntity, (u) => u.pseudonyms)
    @JoinColumn({ name: 'user_id' })
    @Field(() => UserEntity)
    user: UserEntity;
}
