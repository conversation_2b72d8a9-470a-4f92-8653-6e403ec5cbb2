import { Column, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>um<PERSON>, ManyToOne, OneToMany } from 'typeorm';
import { Field, Int, ObjectType, registerEnumType } from '@nestjs/graphql';
import { BaseEntity } from '../commons/bases/base.entity';
import { ItemStatus } from '../commons/enums.common';
import { CategoryEntity } from './category.entity';
import { DepartmentEntity } from './department.entity';
import { SearchFields } from '../commons/decorators/entity.decorators';
import { ArticleEntity } from './article.entity';

@ObjectType('layouts')
@Entity('layouts')
@SearchFields(['name'])
export class LayoutEntity extends BaseEntity {
    @Column()
    @Field()
    name: string;

    @Column({ nullable: true })
    @Field({ nullable: true })
    desc?: string;

    @Column({ type: 'text' })
    @Field()
    content: string;

    @Column({ type: 'smallint' })
    @Field(() => Int)
    status_id: ItemStatus;

    @Column({ type: 'smallint' })
    @Field(() => Int)
    layout_type_id: LayoutType;

    @Column()
    @Field(() => Int)
    department_id: number;

    @Column({ type: 'boolean', default: false })
    @Field(() => Boolean)
    is_default: boolean;

    @Column({ type: 'boolean', default: false })
    @Field(() => Boolean)
    is_mobile: boolean;

    /* Relationships */
    @ManyToOne(() => DepartmentEntity, (d) => d.layouts)
    @JoinColumn({ name: 'department_id' })
    @Field(() => DepartmentEntity)
    department: DepartmentEntity;

    @OneToMany(() => CategoryEntity, (c) => c.categoryWebLayout)
    @Field(() => [CategoryEntity], { nullable: true })
    webCategories?: CategoryEntity[];

    @OneToMany(() => CategoryEntity, (c) => c.categoryMobileLayout)
    @Field(() => [CategoryEntity], { nullable: true })
    mobileCategories?: CategoryEntity[];

    @OneToMany(() => CategoryEntity, (c) => c.categoryArticleWebLayout)
    @Field(() => [CategoryEntity], { nullable: true })
    articleWebCategories?: CategoryEntity[];

    @OneToMany(() => CategoryEntity, (c) => c.categoryArticleMobileLayout)
    @Field(() => [CategoryEntity], { nullable: true })
    articleMobileCategories?: CategoryEntity[];

    @OneToMany(() => ArticleEntity, (a) => a.articleWebLayout)
    @Field(() => [ArticleEntity], { nullable: true })
    webArticles?: ArticleEntity[];

    @OneToMany(() => ArticleEntity, (a) => a.articleMobileLayout)
    @Field(() => [ArticleEntity], { nullable: true })
    mobileArticles?: ArticleEntity[];
}

export enum LayoutType {
    DETAIL = 1,
    CATEGORY = 2,
    HOME = 3,
}

registerEnumType(LayoutType, {
    name: 'LayoutType',
});
