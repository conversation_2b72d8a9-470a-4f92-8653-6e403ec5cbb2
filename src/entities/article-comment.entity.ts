import { Column, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, ManyToOne } from 'typeorm';
import { Field, Int, ObjectType } from '@nestjs/graphql';
import { BaseEntity } from '../commons/bases/base.entity';
import { ArticleEntity } from './article.entity';
import { ItemStatus } from '../commons/enums.common';

@ObjectType('article_comments')
@Entity('article_comments')
export class ArticleCommentEntity extends BaseEntity {
    @Column()
    @Field(() => Int)
    article_id: number;

    @Column()
    @Field()
    sender_name: string;

    @Column()
    @Field()
    sender_email: string;

    @Column()
    @Field()
    content: string;

    @Column({ type: 'smallint' })
    @Field(() => Int)
    status_id: ItemStatus;

    @Column()
    @Field()
    ip_address: string;

    @Column()
    @Field()
    user_agent: string;

    @ManyToOne(() => ArticleEntity, (article) => article.articleComments)
    @JoinColumn({ name: 'article_id' })
    @Field(() => ArticleEntity)
    article: ArticleEntity;
}
