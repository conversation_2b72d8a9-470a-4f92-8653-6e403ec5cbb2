import { Column, Entity } from 'typeorm';
import { Field, Int, ObjectType } from '@nestjs/graphql';
import { BaseEntity } from '../commons/bases/base.entity';
import { ArticleTypes } from './workflow-permission-article-type.entity';
import { SearchFields } from '../commons/decorators/entity.decorators';
import { ItemStatus } from '../commons/enums.common';

@Entity('article_royalty_types')
@ObjectType('article_royalty_types')
@SearchFields(['name', 'desc'])
export class ArticleRoyaltyTypeEntity extends BaseEntity {
    @Column({ length: 255 })
    @Field()
    name: string;

    @Column({ length: 255, nullable: true })
    @Field({ nullable: true })
    desc?: string;

    @Column({ type: 'smallint' })
    @Field(() => Int)
    article_type_id: ArticleTypes;

    @Column({ type: 'int' })
    @Field(() => Int)
    from_royalty: number;

    @Column({ type: 'int' })
    @Field(() => Int)
    to_royalty: number;

    @Column({ type: 'boolean', default: false })
    @Field()
    is_default: boolean;

    @Column({ type: 'smallint' })
    @Field(() => Int)
    status_id: ItemStatus;
}
