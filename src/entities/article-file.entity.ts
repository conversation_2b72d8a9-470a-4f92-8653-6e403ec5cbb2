import { <PERSON>umn, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, ManyToOne } from 'typeorm';
import { Field, Int, ObjectType } from '@nestjs/graphql';
import { BaseEntity } from '../commons/bases/base.entity';
import { ArticleEntity } from './article.entity';
import { FileEntity } from './file.entity';
import { UserEntity } from './user.entity';

@ObjectType('article_files')
@Entity('article_files')
export class ArticleFileEntity extends BaseEntity {
    @Column()
    @Field(() => Int)
    article_id: number;

    @Column()
    @Field(() => Int)
    file_id: number;

    @ManyToOne(() => ArticleEntity, (article) => article.articleFiles)
    @JoinColumn({ name: 'article_id' })
    @Field(() => ArticleEntity)
    article: ArticleEntity;

    @ManyToOne(() => FileEntity)
    @JoinColumn({ name: 'file_id' })
    @Field(() => FileEntity)
    file: FileEntity;

    @Column({ type: 'boolean', default: false })
    @Field()
    is_royalty: boolean;

    @Column({ nullable: true })
    @Field(() => Int, { nullable: true })
    author_id?: number;

    @Column({ type: 'int', nullable: true })
    @Field(() => Int, { nullable: true })
    royalty_cost?: number;

    @ManyToOne(() => UserEntity, { nullable: true, onDelete: 'SET NULL' })
    @JoinColumn({ name: 'author_id' })
    @Field(() => UserEntity, { nullable: true })
    author?: UserEntity;
}
