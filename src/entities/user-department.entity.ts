import { <PERSON>umn, <PERSON><PERSON><PERSON>, Jo<PERSON><PERSON><PERSON>umn, JoinTable, ManyToMany, ManyToOne, OneToMany } from 'typeorm';
import { Field, ObjectType } from '@nestjs/graphql';
import { UserEntity } from './user.entity';
import { DepartmentEntity } from './department.entity';
import { GroupEntity } from './group.entity';
import { BaseIdEntity } from '../commons/bases/base-id.entity';
import { WorkflowPermissionArticleTypeEntity } from './workflow-permission-article-type.entity';

@Entity('user_departments')
@ObjectType('user_departments')
export class UserDepartmentEntity extends BaseIdEntity {
    @Column()
    user_id: number;

    @Column()
    department_id: number;

    @ManyToOne(() => UserEntity, (u) => u.userDepartments)
    @JoinColumn({ name: 'user_id' })
    @Field(() => UserEntity)
    user: UserEntity;

    @ManyToOne(() => DepartmentEntity, (d) => d.userDepartments)
    @JoinColumn({ name: 'department_id' })
    @Field(() => DepartmentEntity)
    department: DepartmentEntity;

    @ManyToMany(() => GroupEntity, (g) => g.userDepartments)
    @JoinTable({
        name: 'user_department_groups',
        joinColumn: { name: 'user_department_id', referencedColumnName: 'id' },
        inverseJoinColumn: { name: 'group_id', referencedColumnName: 'id' },
    })
    @Field(() => [GroupEntity], { nullable: true })
    groups?: GroupEntity[];

    @OneToMany(() => WorkflowPermissionArticleTypeEntity, (w) => w.user_department_id)
    @Field(() => [WorkflowPermissionArticleTypeEntity], { nullable: true })
    articleTypes?: WorkflowPermissionArticleTypeEntity[];
}
