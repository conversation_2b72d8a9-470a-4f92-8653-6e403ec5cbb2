import { <PERSON>umn, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, ManyToOne } from 'typeorm';
import { Field, Int, ObjectType } from '@nestjs/graphql';
import { BaseEntity } from '../commons/bases/base.entity';
import { ArticleEntity } from './article.entity';
import { IssuePageEntity } from './issue-page.entity';
import GraphQLJSON from 'graphql-type-json';

@ObjectType('article_issue_pages')
@Entity('article_issue_pages')
export class ArticleIssuePageEntity extends BaseEntity {
    @Column()
    @Field(() => Int)
    article_id: number;

    @Column({ nullable: true })
    @Field(() => Int, { nullable: true })
    issue_page_id?: number;

    @Column({ type: 'smallint' })
    @Field(() => Int)
    display_order: number;

    @Column({ nullable: true })
    @Field({ nullable: true })
    comment?: string;

    @Column('jsonb', { nullable: true })
    @Field(() => GraphQLJSON, { nullable: true })
    position?: any;

    @ManyToOne(() => ArticleEntity, (article) => article.articleIssuePages)
    @JoinColumn({ name: 'article_id' })
    @Field(() => ArticleEntity)
    article: ArticleEntity;

    @ManyToOne(() => IssuePageEntity, (issuePage) => issuePage.articleIssuePages, { nullable: true })
    @JoinColumn({ name: 'issue_page_id' })
    @Field(() => IssuePageEntity, { nullable: true })
    issuePage?: IssuePageEntity;
}
