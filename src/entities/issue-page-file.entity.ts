import { <PERSON>um<PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>ToOne } from 'typeorm';
import { Field, Int, ObjectType } from '@nestjs/graphql';
import { BaseEntity } from '../commons/bases/base.entity';
import { IssueEntity } from './issue.entity';
import { FileEntity } from './file.entity';
import GraphQLJSON from 'graphql-type-json';
import { ItemStatus } from '../commons/enums.common';

@ObjectType('issue_page_files')
@Entity('issue_page_files')
export class IssuePageFileEntity extends BaseEntity {
    @Column()
    @Field(() => Int)
    issue_id: number;

    @Column('jsonb', { nullable: true })
    @Field(() => [Int], { nullable: true })
    issue_page_id?: number[];

    @Column({ nullable: true })
    @Field(() => Int, { nullable: true })
    file_id?: number;

    @Column('jsonb', { nullable: true })
    @Field(() => [Int], { nullable: true })
    file_ids?: number[];

    @Column({ nullable: true })
    @Field({ nullable: true })
    comment?: string;

    @Column('jsonb', { nullable: true })
    @Field(() => GraphQLJSON, { nullable: true })
    file_comments?: any;

    @Column({ type: 'smallint', default: ItemStatus.PENDING })
    @Field(() => Int)
    status_id: ItemStatus;

    @Column({ nullable: true })
    @Field({ nullable: true })
    name?: string;

    @ManyToOne(() => IssueEntity, (issue) => issue.issuePageFiles)
    @JoinColumn({ name: 'issue_id' })
    @Field(() => IssueEntity)
    issue: IssueEntity;

    @ManyToOne(() => FileEntity, (file) => file.issuePageFiles)
    @JoinColumn({ name: 'file_id' })
    @Field(() => FileEntity, { nullable: true })
    file?: FileEntity;
}
