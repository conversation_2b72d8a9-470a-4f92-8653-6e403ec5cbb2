import { <PERSON>umn, CreateDateColumn, DeleteDateColumn, Entity, JoinColumn, ManyToOne } from 'typeorm';
import { BaseIdEntity } from '../commons/bases/base-id.entity';
import { UserEntity } from './user.entity';

@Entity('password_resets')
export class PasswordResetEntity extends BaseIdEntity {
    @Column()
    number_sent: number;

    @Column()
    user_id: number;

    @Column()
    otp: string;

    @Column()
    token: string;

    @Column()
    expires_at: Date;

    @CreateDateColumn()
    created_at: Date;

    @DeleteDateColumn({ nullable: true, default: null })
    deleted_at?: Date;

    @ManyToOne(() => UserEntity, (user) => user.pws)
    @JoinColumn({ name: 'user_id' })
    user?: UserEntity;
}
