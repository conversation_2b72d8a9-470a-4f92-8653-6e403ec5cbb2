import { <PERSON>umn, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, ManyToOne, OneToMany } from 'typeorm';
import { Field, Int, ObjectType, registerEnumType } from '@nestjs/graphql';
import { BaseEntity } from '../commons/bases/base.entity';
import { ArticleEntity } from './article.entity';
import { ArticleRoyaltyTypeEntity } from './article-royalty-type.entity';
import { ArticleRoyaltyUserEntity } from './article-royalty-user.entity';

export enum RoyaltyType {
    CONTENT = 1,
    MEDIA = 2,
    SUPPORT = 3,
}

registerEnumType(RoyaltyType, {
    name: 'RoyaltyType',
});

@ObjectType('article_royalties')
@Entity('article_royalties')
export class ArticleRoyaltyEntity extends BaseEntity {
    @Column({ nullable: true })
    @Field(() => Int, { nullable: true })
    article_id?: number;

    @Column({ type: 'smallint' })
    @Field(() => Int)
    type_id: RoyaltyType;

    @Column({ nullable: true })
    @Field(() => Int, { nullable: true })
    royalty_type_id?: number;

    @Column({ type: 'int' })
    @Field(() => Int)
    suggest_royalty: number;

    @Column({ nullable: true })
    @Field({ nullable: true })
    title?: string;

    @Column({ type: 'int', nullable: true, default: 0 })
    @Field(() => Int, { nullable: true })
    article_statistic?: number;

    /* Relationships */
    @ManyToOne(() => ArticleEntity, (article) => article.articleRoyalties, { nullable: true })
    @JoinColumn({ name: 'article_id' })
    @Field(() => ArticleEntity, { nullable: true })
    article?: ArticleEntity;

    @ManyToOne(() => ArticleRoyaltyTypeEntity, { nullable: true })
    @JoinColumn({ name: 'royalty_type_id' })
    @Field(() => ArticleRoyaltyTypeEntity, { nullable: true })
    royaltyType?: ArticleRoyaltyTypeEntity;

    @OneToMany(() => ArticleRoyaltyUserEntity, (aru) => aru.articleRoyalty)
    @Field(() => [ArticleRoyaltyUserEntity], { nullable: true })
    articleRoyaltyUsers?: ArticleRoyaltyUserEntity[];
}
