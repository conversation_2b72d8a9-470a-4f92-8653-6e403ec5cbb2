import { Column, <PERSON><PERSON>ty, <PERSON>in<PERSON><PERSON>umn, ManyToOne, OneToMany, OneToOne } from 'typeorm';
import { Field, Int, ObjectType, registerEnumType } from '@nestjs/graphql';
import { BaseEntity } from '../commons/bases/base.entity';
import { ItemStatus } from '../commons/enums.common';
import { DepartmentEntity } from './department.entity';
import { FileEntity } from './file.entity';
import { AdvertiseAdvItemEntity } from './advertise-adv-item.entity';
import { SearchFields } from '../commons/decorators/entity.decorators';

@ObjectType('adv_items')
@Entity('adv_items')
@SearchFields(['name'])
export class AdvItemEntity extends BaseEntity {
    @Column()
    @Field()
    name: string;

    @Column({ nullable: true })
    @Field({ nullable: true })
    desc?: string;

    @Column()
    @Field()
    content: string;

    @Column({ type: 'smallint' })
    @Field(() => Int)
    type_id: AdvItemType;

    @Column({ nullable: true })
    @Field(() => Int, { nullable: true })
    file_id?: number;

    @Column({ type: 'smallint' })
    @Field(() => Int)
    status_id: ItemStatus;

    @Column({ type: 'smallint' })
    @Field(() => Int)
    width: number;

    @Column({ type: 'smallint' })
    @Field(() => Int)
    height: number;

    @Column({ type: 'date' })
    @Field(() => String)
    start_date: Date;

    @Column({ type: 'date', nullable: true })
    @Field(() => String, { nullable: true })
    end_date?: Date;

    @Column()
    @Field(() => Int)
    department_id: number;

    @ManyToOne(() => DepartmentEntity, (d) => d.advItems)
    @JoinColumn({ name: 'department_id' })
    @Field(() => DepartmentEntity)
    department: DepartmentEntity;

    @OneToOne(() => FileEntity)
    @JoinColumn({ name: 'file_id' })
    @Field(() => FileEntity, { nullable: true })
    file?: FileEntity;

    @OneToMany(() => AdvertiseAdvItemEntity, (aai) => aai.advItem)
    advertiseAdvItems?: AdvertiseAdvItemEntity[];
}

export enum AdvItemType {
    IMAGE = 1,
    VIDEO = 2,
    HTML = 3,
    TEXT = 4,
}

registerEnumType(AdvItemType, {
    name: 'AdvItemType',
});
