import { Column, <PERSON><PERSON><PERSON>, <PERSON>in<PERSON><PERSON>umn, JoinTable, ManyToMany, ManyToOne, OneToMany } from 'typeorm';
import { Field, Int, ObjectType, registerEnumType } from '@nestjs/graphql';
import { BaseEntity } from '../commons/bases/base.entity';
import { ItemStatus } from '../commons/enums.common';
import { DepartmentEntity } from './department.entity';
import { AdvertiseAdvItemEntity } from './advertise-adv-item.entity';
import { CategoryEntity } from './category.entity';
import { SearchFields } from '../commons/decorators/entity.decorators';

@ObjectType('advertises')
@Entity('advertises')
@SearchFields(['code', 'name'])
export class AdvertiseEntity extends BaseEntity {
    @Column()
    @Field()
    code: string;

    @Column()
    @Field()
    name: string;

    @Column({ nullable: true })
    @Field({ nullable: true })
    desc?: string;

    @Column({ type: 'smallint' })
    @Field(() => Int)
    type_id: AdvType;

    @Column({ type: 'smallint' })
    @Field(() => Int)
    speed: number;

    @Column({ type: 'smallint' })
    @Field(() => Int)
    display_type_id: DisplayType;

    @Column({ type: 'smallint' })
    @Field(() => Int)
    status_id: ItemStatus;

    @Column()
    @Field(() => Int)
    department_id: number;

    @ManyToOne(() => DepartmentEntity, (d) => d.advertises)
    @JoinColumn({ name: 'department_id' })
    @Field(() => DepartmentEntity)
    department: DepartmentEntity;

    @OneToMany(() => AdvertiseAdvItemEntity, (aai) => aai.advertise)
    @Field(() => [AdvertiseAdvItemEntity], { nullable: true })
    advertiseAdvItems?: AdvertiseAdvItemEntity[];

    @ManyToMany(() => CategoryEntity, (category) => category.advertises)
    @JoinTable({
        name: 'advertise_categories',
        joinColumn: { name: 'advertise_id', referencedColumnName: 'id' },
        inverseJoinColumn: { name: 'category_id', referencedColumnName: 'id' },
    })
    @Field(() => [CategoryEntity], { nullable: true })
    categories?: CategoryEntity[];
}

export enum AdvType {
    BANNER = 1,
    POPUP = 2,
    SIDEBAR = 3,
    FLOATING = 4,
}

export enum DisplayType {
    IMAGE = 1,
    VIDEO = 2,
    HTML = 3,
    TEXT = 4,
}

registerEnumType(AdvType, {
    name: 'AdvType',
});

registerEnumType(DisplayType, {
    name: 'DisplayType',
});
