import { <PERSON>umn, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>um<PERSON>, ManyToOne, OneToMany } from 'typeorm';
import { Field, Int, ObjectType } from '@nestjs/graphql';
import { BaseEntity } from '../commons/bases/base.entity';
import { ItemStatus } from '../commons/enums.common';
import { DepartmentEntity } from './department.entity';
import { WorkflowPermissionArticleTypeEntity } from './workflow-permission-article-type.entity';
import { SearchFields } from '../commons/decorators/entity.decorators';

@ObjectType('workflow_transitions')
export class WorkflowTransition {
    @Field(() => Int, { nullable: true })
    from: number | null;

    @Field(() => Int)
    to: number;
}

@ObjectType('workflow_permissions')
@Entity('workflow_permissions')
@SearchFields(['name'])
export class WorkflowPermissionEntity extends BaseEntity {
    @Column()
    @Field()
    name: string;

    @Column({ type: 'smallint' })
    @Field(() => Int)
    status_id: ItemStatus;

    @Column({ nullable: true })
    @Field({ nullable: true })
    desc?: string;

    @Column()
    @Field(() => Int)
    department_id: number;

    @Column({ type: 'jsonb' })
    @Field(() => [Int])
    workflow_ids: number[];

    @Column({ type: 'jsonb' })
    @Field(() => [WorkflowTransition])
    workflow_transitions: WorkflowTransition[];

    @ManyToOne(() => DepartmentEntity, (d) => d.workflowPermissions)
    @JoinColumn({ name: 'department_id' })
    @Field(() => DepartmentEntity)
    department: DepartmentEntity;

    @OneToMany(() => WorkflowPermissionArticleTypeEntity, (w) => w.workflowPermission)
    @Field(() => [WorkflowPermissionArticleTypeEntity], { nullable: true })
    articleTypes?: WorkflowPermissionArticleTypeEntity[];
}
