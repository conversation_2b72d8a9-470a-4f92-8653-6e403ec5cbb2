import { <PERSON><PERSON><PERSON>, <PERSON>reateDate<PERSON><PERSON><PERSON>n, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, ManyToOne } from 'typeorm';
import { BaseIdEntity } from '../commons/bases/base-id.entity';
import { UserEntity } from './user.entity';

@Entity('personal_tokens')
export class PersonalTokenEntity extends BaseIdEntity {
    @Column()
    user_id: number;

    @Column()
    access_token: string;

    @Column()
    refresh_token: string;

    @Column()
    is_enable: boolean;

    @Column()
    expires_at: Date;

    @CreateDateColumn()
    created_at: Date;

    @ManyToOne(() => UserEntity, (user) => user.personal_tokens)
    @JoinColumn({ name: 'user_id' })
    user: UserEntity;
}
