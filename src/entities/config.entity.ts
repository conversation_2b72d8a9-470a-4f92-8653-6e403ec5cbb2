import { Column, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>umn, ManyToOne } from 'typeorm';
import { Field, Int, ObjectType, registerEnumType } from '@nestjs/graphql';
import { BaseEntity } from '../commons/bases/base.entity';
import { DepartmentEntity } from './department.entity';
import { SearchFields } from '../commons/decorators/entity.decorators';

export enum ConfigType {
    TEXT = 1,
    NUMBER = 2,
    IMAGE = 3,
    EDITOR = 4,
    JSON = 5,
}

registerEnumType(ConfigType, {
    name: 'ConfigType',
});

@ObjectType('configs')
@Entity('configs')
@SearchFields(['code', 'name'])
export class ConfigEntity extends BaseEntity {
    @Column({ unique: false })
    @Field()
    code: string;

    @Column()
    @Field()
    name: string;

    @Column()
    @Field()
    content: string;

    @Column()
    @Field(() => Int)
    department_id: number;

    @Column({ type: 'smallint', default: ConfigType.TEXT })
    @Field(() => Int)
    config_type_id: ConfigType;

    @ManyToOne(() => DepartmentEntity, (d) => d.configs)
    @JoinColumn({ name: 'department_id' })
    @Field(() => DepartmentEntity)
    department: DepartmentEntity;
}
