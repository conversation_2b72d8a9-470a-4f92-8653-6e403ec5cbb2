/**
 * Application constants configuration
 * These values are hardcoded and not loaded from environment variables
 */

export const constantConf = {
    /**
     * GraphQL complexity settings
     */
    graphql: {
        /**
         * Maximum allowed complexity for GraphQL queries
         */
        maxComplexity: 200,

        /**
         * Default complexity for fields without explicit complexity
         */
        defaultComplexity: 1,

        /**
         * Maximum allowed depth for GraphQL queries
         */
        maxDepth: 10,

        /**
         * Operations that should be excluded from complexity calculation
         */
        excludedOperations: ['IntrospectionQuery'],
    },
};

export default constantConf;
