import { NestFactory } from '@nestjs/core';
import { AppModule } from './app.module';
import { ValidationSanitizationPipe } from './commons/pipes/validation-sanitization.pipe';
import helmet from 'helmet';
import * as compression from 'compression';
import appConf from './configs/app.conf';
import { useContainer } from 'class-validator';
import { HttpExceptionFilter } from './commons/filters/http-exception.filter';
import rateLimit from 'express-rate-limit';
import * as useragent from 'express-useragent';
import * as bodyParser from 'body-parser';
import * as timeout from 'connect-timeout';

async function bootstrap() {
    const app = await NestFactory.create(AppModule);

    // ✅ Thêm middleware timeout (nếu file lớn upload chậm)
    app.use(timeout(`${appConf.REQUEST_TIMEOUT}m`)); //phút

    // ✅ Tăng giới hạn body (chỉ cần nếu upload kèm metadata JSON to)
    app.use(bodyParser.json({ limit: `${appConf.LIMIT_UPLOAD_SIZE + 1}gb` }));
    app.use(bodyParser.urlencoded({ limit: `${appConf.LIMIT_UPLOAD_SIZE + 1}gb`, extended: true }));

    app.useGlobalFilters(new HttpExceptionFilter());
    app.use(useragent.express());

    // 🔒 Bảo vệ headers HTTP
    app.use(
        helmet({
            contentSecurityPolicy: appConf.NODE_ENV === 'production' ? undefined : false, // Tắt CSP khi ở dev
            crossOriginResourcePolicy: { policy: 'cross-origin' }, // Cho phép tải tài nguyên từ bên ngoài
            hsts: {
                maxAge: 31536000, // 1 năm
                includeSubDomains: true,
                preload: true,
            },
            xContentTypeOptions: true, // X-Content-Type-Options: nosniff
            xFrameOptions: { action: 'deny' }, // X-Frame-Options: DENY
            referrerPolicy: { policy: 'strict-origin-when-cross-origin' }, // Referrer-Policy
        })
    );
    // 🌍 Kích hoạt CORS (Chỉ cho phép từ các domain cụ thể)
    app.enableCors({
        origin: appConf.CORS_ORIGIN, // Chỉ cho phép từ domain này
        methods: 'GET,POST', // 🔥 Chỉ cho phép các phương thức GraphQL cần
        //allowedHeaders: 'Content-Type,Authorization',
        allowedHeaders: 'Content-Type,Authorization,x-apollo-operation-name,apollo-require-preflight',
        credentials: true, // Cho phép cookie & header token
    });

    // 🚀 Nén dữ liệu
    app.use(compression());

    // 🔒 Rate limiting toàn cục
    app.use(
        rateLimit({
            windowMs: 60 * 1000, // 1 phút
            limit: 1000, // Giới hạn 1000 request mỗi IP trong 1 phút
            standardHeaders: 'draft-7', // Trả về các header chuẩn RateLimit
            legacyHeaders: false, // Không sử dụng các header cũ
            message: { error: 'Too many requests, please try again later.' },
            // Không áp dụng rate limit cho các route tĩnh (nếu có)
            skip: (request) => request.url.startsWith('/files'),
        })
    );

    // ✅ Validation & Sanitization Pipes (Tự động kiểm tra và làm sạch dữ liệu đầu vào)
    app.useGlobalPipes(new ValidationSanitizationPipe());

    //Đảm bảo rằng NestJS DI (Dependency Injection)
    useContainer(app.select(AppModule), { fallbackOnErrors: true });

    await app.listen(appConf.APP_PORT);

    // Xử lý khi NestJS bị tắt để tránh lỗi chiếm cổng
    process.on('SIGINT', async () => {
        console.log('🛑 Stopping server...');
        await app.close();
        process.exit(0);
    });

    process.on('SIGTERM', async () => {
        console.log('🛑 SIGTERM received. Closing server...');
        await app.close();
        process.exit(0);
    });
}
bootstrap();
