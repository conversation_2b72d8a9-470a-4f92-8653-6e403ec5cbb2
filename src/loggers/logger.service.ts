import { Injectable, Logger } from '@nestjs/common';
import * as fs from 'fs';
import * as path from 'path';
import appConf from '../configs/app.conf';

@Injectable()
export class LoggerService extends Logger {
    private readonly logDir: string;
    private logStream: fs.WriteStream;
    private errorStream: fs.WriteStream;
    private streams: { [key: string]: fs.WriteStream } = {};

    constructor() {
        super();
        // Use relative path from current working directory if ROOT_PATH is empty
        const basePath = appConf.ROOT_PATH || process.cwd();
        this.logDir = path.join(basePath, 'logs');

        if (!fs.existsSync(this.logDir)) {
            fs.mkdirSync(this.logDir, { recursive: true });
        }

        this.logStream = fs.createWriteStream(path.join(this.logDir, 'app.log'), {
            flags: 'a',
        });
        this.errorStream = fs.createWriteStream(path.join(this.logDir, 'error.log'), {
            flags: 'a',
        });
    }

    log(message: string, fileName?: string) {
        if (fileName) {
            if (!this.streams[fileName]) {
                this.streams[fileName] = fs.createWriteStream(path.join(this.logDir, `${fileName}.log`), {
                    flags: 'a',
                });
            }
            this.streams[fileName].write(`${new Date().toISOString()} LOG: ${message}\n`);
        } else this.logStream.write(`${new Date().toISOString()} LOG: ${message}\n`);
        super.log(message);
    }

    async error(message: string, trace: string, context?: string, request?: Request) {
        this.errorStream.write(`${new Date().toISOString()} ERROR: ${message}\nTRACE: ${trace}\n`);
        super.error(message, trace, context, request);
    }

    warn(message: string, context?: string) {
        super.warn(message, context);
    }

    debug(message: string, context?: string) {
        super.debug(message, context);
    }

    verbose(message: string, context?: string) {
        super.verbose(message, context);
    }
}
