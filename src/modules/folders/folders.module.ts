import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { FolderEntity } from '../../entities/folder.entity';
import { FoldersService } from './folders.service';
import { FoldersResolver } from './folders.resolver';

@Module({
    imports: [TypeOrmModule.forFeature([FolderEntity])],
    providers: [FoldersService, FoldersResolver],
})
export class FoldersModule {}
