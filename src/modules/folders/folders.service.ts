import { Injectable, ForbiddenException, NotFoundException } from '@nestjs/common';
import { BaseService } from '../../commons/bases/base.service';
import { FolderEntity } from '../../entities/folder.entity';
import { InjectRepository } from '@nestjs/typeorm';
import { In, Repository } from 'typeorm';
import { FolderSaveInputDto } from './dtos/folder-save-input.dto';
import { UserEntity } from '../../entities/user.entity';

@Injectable()
export class FoldersService extends BaseService<FolderEntity> {
    constructor(@InjectRepository(FolderEntity) public readonly repo: Repository<FolderEntity>) {
        super(repo);
    }
    /**
     * Check if a folder can be deleted by verifying it has no files
     * and no subfolders with files
     */
    async canDelete(folderId: number): Promise<boolean> {
        // Check if the folder has files
        const folderWithFiles = await this.repo.findOne({
            where: { id: folderId },
            relations: ['files'],
        });

        if (folderWithFiles && folderWithFiles.files && folderWithFiles.files.length > 0) {
            return false;
        }

        // Check if any subfolders exist
        const subfolders = await this.repo.find({
            where: { parent_id: folderId },
        });

        if (subfolders.length === 0) {
            // No subfolders, safe to delete
            return true;
        }

        // Check if any subfolder has files or contains nested subfolders with files
        for (const subfolder of subfolders) {
            const canDeleteSubfolder = await this.canDelete(subfolder.id);
            if (!canDeleteSubfolder) {
                return false;
            }
        }

        // No files found in this folder or any subfolders
        return true;
    }

    async softDelete(id: number, deletedBy?: number): Promise<void> {
        const canDelete = await this.canDelete(id);

        if (!canDelete) {
            throw new ForbiddenException('Cannot delete folder with files or subfolders containing files');
        }

        await super.delete(id);
    }

    async saveFolder(body: FolderSaveInputDto, userId: number, id?: number): Promise<FolderEntity> {
        let folder: FolderEntity = new FolderEntity();
        if (id) {
            folder = (await this.findOne(id)) as FolderEntity;
            if (!folder) throw new NotFoundException();
        }
        Object.assign(folder, body);
        if (body.user_ids?.length)
            folder.users = await this.repo.manager.find(UserEntity, { where: { id: In(body.user_ids) } });
        else folder.users = [];
        return this.repo.manager.transaction(async (run) => {
            return run.save(folder);
        });
    }
}
