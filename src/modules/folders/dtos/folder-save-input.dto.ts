import { Field, InputType, Int } from '@nestjs/graphql';
import { IsArray, IsBoolean, IsNotEmpty, IsOptional, IsString } from 'class-validator';
import { BaseUpdateInputDto } from '../../../commons/bases/base.input';
import { IdExists } from '../../../commons/validators/id-exists.validator';
import { IsUnique } from '../../../commons/validators/is-unique.validator';
import { AutoSanitize } from '../../../commons/decorators/sanitize.decorators';
import { FolderEntity } from '../../../entities/folder.entity';
import { DepartmentEntity } from '../../../entities/department.entity';
import { validationMessagesLang } from '../../../languages/validation-messages.lang';
import { UserEntity } from '../../../entities/user.entity';

@InputType()
@AutoSanitize()
export class FolderSaveInputDto extends BaseUpdateInputDto {
    @Field()
    @IsNotEmpty({ message: validationMessagesLang.REQUIRED })
    @IsString()
    @IsUnique(FolderEntity)
    name: string;

    @Field(() => Int, { nullable: true })
    @IsOptional()
    @IdExists(FolderEntity)
    parent_id?: number;

    @Field(() => Int)
    @IsNotEmpty({ message: validationMessagesLang.REQUIRED })
    @IdExists(DepartmentEntity)
    department_id: number;

    @Field(() => Boolean)
    @IsBoolean()
    is_newsroom: boolean;

    @Field(() => [Int])
    @IsArray()
    @IdExists(UserEntity, { each: true })
    user_ids: number[];
}
