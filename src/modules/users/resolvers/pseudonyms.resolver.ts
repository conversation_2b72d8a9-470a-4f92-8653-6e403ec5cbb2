import { Args, Int, Mutation, Parent, Query, ResolveField } from '@nestjs/graphql';
import { AuthResolver } from '../../../commons/decorators/graphql.decorators';
import { UserEntity } from '../../../entities/user.entity';
import { NotFoundException } from '@nestjs/common';
import { BasePaginationInput } from '../../../commons/bases/base.input';
import { AuthUser } from '../../auth/auth.decorator';
import { IPaginatedType } from '../../../commons/bases/base.model';
import { DataLoaderService } from '../../../data-loaders/data-loaders.service';
import { PseudonymsEntity } from '../../../entities/pseudonyms.entity';
import { PseudonymsService } from '../services/pseudonyms.service';
import { PseudonymsModel } from '../models/pseudonyms.model';
import { PseudonymSaveInputDto } from '../dtos/pseudonym-save-input.dto';

@AuthResolver(PseudonymsEntity)
export class PseudonymsResolver {
    constructor(
        private readonly pseudonymsService: PseudonymsService,
        private readonly dataLoader: DataLoaderService
    ) {}

    @ResolveField(() => UserEntity, { nullable: true })
    async user(@Parent() parent: PseudonymsEntity): Promise<UserEntity | null> {
        if (!parent.user_id) return null;
        return this.dataLoader.relationBatchOne(UserEntity).load(parent.user_id);
    }

    @Query(() => PseudonymsModel, { name: 'pseudonyms_list' })
    async index(@Args('body') body: BasePaginationInput): Promise<IPaginatedType<PseudonymsEntity>> {
        return this.pseudonymsService.search(body);
    }

    @Query(() => PseudonymsEntity, { name: 'pseudonyms_detail' })
    async view(@Args('id', { type: () => Int }) id: number): Promise<PseudonymsEntity> {
        return this.pseudonymsService.findOne(id).then((res) => {
            if (!res) {
                throw new NotFoundException();
            }
            return res;
        });
    }

    @Mutation(() => PseudonymsEntity, { name: 'pseudonyms_create' })
    async store(@Args('body') body: PseudonymSaveInputDto, @AuthUser() auth: UserEntity): Promise<PseudonymsEntity> {
        return this.pseudonymsService.create({ ...body, created_by: auth.id });
    }

    @Mutation(() => PseudonymsEntity, { name: 'pseudonyms_update' })
    async update(
        @Args('id', { type: () => Int }) id: number,
        @Args('body') body: PseudonymSaveInputDto,
        @AuthUser() auth: UserEntity
    ): Promise<PseudonymsEntity> {
        return this.pseudonymsService.updateOne(id, { ...body, updated_by: auth.id });
    }

    @Mutation(() => Boolean, { name: 'pseudonyms_delete' })
    async destroy(@Args('id', { type: () => Int }) id: number, @AuthUser() auth: UserEntity): Promise<Boolean> {
        await this.pseudonymsService.softDelete(id, auth.id);
        return true;
    }
}
