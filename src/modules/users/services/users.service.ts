import { Injectable, NotFoundException } from '@nestjs/common';
import { BaseService } from '../../../commons/bases/base.service';
import { UserEntity } from '../../../entities/user.entity';
import { DeleteResult, In, Repository } from 'typeorm';
import { InjectRepository } from '@nestjs/typeorm';
import * as bcrypt from 'bcrypt';
import { UserCreateInputDto } from '../dtos/user-create-input.dto';
import { ResetPasswordInputDto } from '../dtos/reset-password-input.dto';
import { ChangeGroupsInputDto } from '../dtos/change-groups-input.dto';
import { GroupEntity } from '../../../entities/group.entity';
import { ItemStatus } from '../../../commons/enums.common';
import { isEmpty } from 'lodash';
import { ChangeDepartmentsInputDto } from '../dtos/change-departments-input.dto';
import { UserDepartmentEntity } from '../../../entities/user-department.entity';
import { DepartmentEntity } from '../../../entities/department.entity';

@Injectable()
export class UsersService extends BaseService<UserEntity> {
    constructor(
        @InjectRepository(UserEntity)
        public readonly repo: Repository<UserEntity>
    ) {
        super(repo);
    }

    async store(body: UserCreateInputDto, auth: UserEntity): Promise<UserEntity> {
        const salt = await bcrypt.genSalt(10);
        const hashedPassword = await bcrypt.hash(body.password, salt);

        const newUser = this.repo.create({
            ...body,
            password: hashedPassword,
            created_by: auth.id,
            status_id: body.status_id ?? ItemStatus.ACTIVE,
        });
        return this.repo.save(newUser);
    }

    async resetPassword(id: number, body: ResetPasswordInputDto): Promise<boolean> {
        const salt = await bcrypt.genSalt(10);
        const hashedPassword = await bcrypt.hash(body.password, salt);
        await this.updateOne(id, {
            password: hashedPassword,
            change_password_at: new Date(),
        });
        return true;
    }

    async changeGroups(id: number, body: ChangeGroupsInputDto): Promise<UserEntity | DeleteResult> {
        const user = await this.findOne(id);
        if (!user) {
            throw new NotFoundException();
        }
        const groups = isEmpty(body.group_ids)
            ? []
            : await this.repo.manager.find(GroupEntity, { where: { id: In(body.group_ids) } });
        return this.repo.manager.transaction(async (run) => {
            if (isEmpty(groups)) {
                return run.delete('user_groups', { user_id: id });
            } else {
                user.groups = groups;
                return run.save(user);
            }
        });
    }

    async changeDepartments(id: number, body: ChangeDepartmentsInputDto): Promise<UserDepartmentEntity[] | undefined> {
        const user = await this.findOne(id);
        if (!user) {
            throw new NotFoundException();
        }
        const departments = isEmpty(body.department_ids)
            ? []
            : await this.repo.manager.find(DepartmentEntity, { where: { id: In(body.department_ids) } });
        const oldUserDepartments = await this.repo.manager.find(UserDepartmentEntity, { where: { user_id: id } });
        const deleteIds = oldUserDepartments
            .filter((ud) => !departments.find((d) => d.id === ud.department_id))
            .map((ud) => ud.id);

        const userDepartments = departments
            .filter((d) => !oldUserDepartments.find((ud) => ud.department_id === d.id))
            .map((d) => {
                const ud = new UserDepartmentEntity();
                ud.user = user;
                ud.department = d;
                return ud;
            });
        return this.repo.manager.transaction(async (run) => {
            if (!isEmpty(deleteIds)) await run.delete(UserDepartmentEntity, { user_id: id, id: In(deleteIds) });
            if (!isEmpty(userDepartments)) return run.save(userDepartments);
        });
    }
}
