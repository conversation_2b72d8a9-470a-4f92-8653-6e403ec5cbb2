import { Injectable } from '@nestjs/common';
import { BaseService } from '../../../commons/bases/base.service';
import { Repository } from 'typeorm';
import { InjectRepository } from '@nestjs/typeorm';
import { PseudonymsEntity } from '../../../entities/pseudonyms.entity';

@Injectable()
export class PseudonymsService extends BaseService<PseudonymsEntity> {
    constructor(
        @InjectRepository(PseudonymsEntity)
        public readonly repo: Repository<PseudonymsEntity>
    ) {
        super(repo);
    }
}
