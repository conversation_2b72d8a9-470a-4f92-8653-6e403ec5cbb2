import { Modu<PERSON> } from '@nestjs/common';
import { UsersResolver } from './resolvers/users.resolver';
import { UsersService } from './services/users.service';
import { TypeOrmModule } from '@nestjs/typeorm';
import { UserEntity } from '../../entities/user.entity';
import { AuthModule } from '../auth/auth.module';
import { PseudonymsEntity } from '../../entities/pseudonyms.entity';
import { PseudonymsService } from './services/pseudonyms.service';
import { PseudonymsResolver } from './resolvers/pseudonyms.resolver';

@Module({
    imports: [TypeOrmModule.forFeature([UserEntity, PseudonymsEntity]), AuthModule],
    providers: [UsersResolver, UsersService, PseudonymsService, PseudonymsResolver],
})
export class UsersModule {}
