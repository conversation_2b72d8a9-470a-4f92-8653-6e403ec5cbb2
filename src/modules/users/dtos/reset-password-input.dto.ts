import { Field, InputType } from '@nestjs/graphql';
import { IsNotEmpty, IsString } from 'class-validator';
import { userValidationMessages } from '../../../languages/validation-messages.lang';
import { IsPassword } from '../../../commons/validators/is-password.validator';
import { EqualField } from '../../../commons/validators/equal-field.validator';
import { AutoSanitize } from '../../../commons/decorators/sanitize.decorators';

@InputType()
@AutoSanitize()
export class ResetPasswordInputDto {
    @Field()
    @IsNotEmpty({ message: userValidationMessages.REQUIRED_PASSWORD })
    @IsString()
    @IsPassword()
    password: string;

    @Field()
    @IsNotEmpty({ message: userValidationMessages.REQUIRED_CONFIRM_PASSWORD })
    @EqualField('password', { message: userValidationMessages.CONFIRM_PASSWORD })
    confirm_password: string;
}
