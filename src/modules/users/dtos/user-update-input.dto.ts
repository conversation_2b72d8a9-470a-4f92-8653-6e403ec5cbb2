import { Field, InputType, Int } from '@nestjs/graphql';
import { IsEnum, IsNotEmpty, IsOptional } from 'class-validator';
import { ProfileInputDto } from '../../auth/dtos/profile-input.dto';
import { validationMessagesLang } from '../../../languages/validation-messages.lang';
import { ItemStatus } from '../../../commons/enums.common';
import { UserRoles } from '../../../entities/user.entity';
import { AutoSanitize } from '../../../commons/decorators/sanitize.decorators';

@InputType()
@AutoSanitize()
export class UserUpdateInputDto extends ProfileInputDto {
    @Field(() => Int, { nullable: true })
    @IsOptional()
    @IsNotEmpty({ message: validationMessagesLang.REQUIRED })
    @IsEnum(ItemStatus)
    status_id?: ItemStatus;

    @Field(() => Int, { nullable: true })
    @IsOptional()
    @IsEnum(UserRoles)
    role_id?: UserRoles;
}
