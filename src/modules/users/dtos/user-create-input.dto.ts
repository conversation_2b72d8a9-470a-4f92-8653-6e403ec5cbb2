import { Field, InputType, Int } from '@nestjs/graphql';
import { RegisterInputDto } from '../../auth/dtos/register-input.dto';
import { IsEnum, IsNotEmpty, IsOptional } from 'class-validator';
import { validationMessagesLang } from '../../../languages/validation-messages.lang';
import { ItemStatus } from '../../../commons/enums.common';
import { AutoSanitize } from '../../../commons/decorators/sanitize.decorators';

@InputType()
@AutoSanitize()
export class UserCreateInputDto extends RegisterInputDto {
    @Field(() => Int, { nullable: true })
    @IsOptional()
    @IsNotEmpty({ message: validationMessagesLang.REQUIRED })
    @IsEnum(ItemStatus)
    status_id?: ItemStatus;
}
