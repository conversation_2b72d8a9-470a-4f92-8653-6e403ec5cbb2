import { Field, InputType, Int } from '@nestjs/graphql';
import { IsArray, IsNotEmpty } from 'class-validator';
import { IdExists } from '../../../commons/validators/id-exists.validator';
import { AutoSanitize } from '../../../commons/decorators/sanitize.decorators';
import { DepartmentEntity } from '../../../entities/department.entity';

@InputType()
@AutoSanitize()
export class ChangeDepartmentsInputDto {
    @Field(() => [Int])
    @IsArray()
    //@ArrayNotEmpty({ message: validationMessagesLang.REQUIRED })
    @IsNotEmpty({ each: true })
    @IdExists(DepartmentEntity, { each: true })
    department_ids: number[];
}
