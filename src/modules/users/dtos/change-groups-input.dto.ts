import { Field, InputType, Int } from '@nestjs/graphql';
import { ArrayNotEmpty, IsArray, IsNotEmpty } from 'class-validator';
import { IdExists } from '../../../commons/validators/id-exists.validator';
import { validationMessagesLang } from '../../../languages/validation-messages.lang';
import { GroupEntity } from '../../../entities/group.entity';
import { AutoSanitize } from '../../../commons/decorators/sanitize.decorators';

@InputType()
@AutoSanitize()
export class ChangeGroupsInputDto {
    @Field(() => [Int])
    @IsArray()
    //@ArrayNotEmpty({ message: validationMessagesLang.REQUIRED })
    @IsNotEmpty({ each: true })
    @IdExists(GroupEntity, { each: true })
    group_ids: number[];
}
