import { Field, InputType, Int } from '@nestjs/graphql';
import { IsBoolean, IsEnum, IsNotEmpty, IsString } from 'class-validator';
import { groupValidationMessages, validationMessagesLang } from '../../../languages/validation-messages.lang';
import { IdExists } from '../../../commons/validators/id-exists.validator';
import { IsUnique } from '../../../commons/validators/is-unique.validator';
import { GroupEntity } from '../../../entities/group.entity';
import { BaseUpdateInputDto } from '../../../commons/bases/base.input';
import { ItemStatus } from '../../../commons/enums.common';
import { UserEntity } from '../../../entities/user.entity';
import { AutoSanitize } from '../../../commons/decorators/sanitize.decorators';

@InputType()
@AutoSanitize()
export class PseudonymSaveInputDto extends BaseUpdateInputDto {
    @Field()
    @IsNotEmpty({ message: validationMessagesLang.REQUIRED })
    @IsString()
    @IsUnique(GroupEntity, { message: groupValidationMessages.IS_EXISTS })
    name: string;

    @Field(() => Int)
    @IsEnum(ItemStatus)
    status_id: ItemStatus;

    @Field(() => Boolean)
    @IsBoolean()
    is_default: boolean;

    @Field(() => Int)
    @IsNotEmpty()
    @IdExists(UserEntity)
    user_id: number;
}
