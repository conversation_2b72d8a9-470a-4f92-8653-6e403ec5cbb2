import { Args, Int, Mutation, Parent, Query, ResolveField } from '@nestjs/graphql';
import { NotFoundException } from '@nestjs/common';
import { AuthResolver } from '../../../commons/decorators/graphql.decorators';
import { DataLoaderService } from '../../../data-loaders/data-loaders.service';
import { TemplateEntity } from '../../../entities/template.entity';
import { TemplatesService } from '../services/templates.service';
import { TemplateSaveInputDto } from '../dtos/template-save-input.dto';
import { TemplatesModel } from '../models/templates.model';
import { BasePaginationInput } from '../../../commons/bases/base.input';
import { IPaginatedType } from '../../../commons/bases/base.model';
import { AuthUser } from '../../auth/auth.decorator';
import { UserEntity } from '../../../entities/user.entity';
import { DepartmentEntity } from '../../../entities/department.entity';
import { FileEntity } from '../../../entities/file.entity';

@AuthResolver(TemplateEntity)
export class TemplatesResolver {
    constructor(
        private readonly templatesService: TemplatesService,
        private readonly dataLoader: DataLoaderService
    ) {}

    @ResolveField(() => FileEntity, { nullable: true })
    async avatar(@Parent() parent: TemplateEntity): Promise<FileEntity | null> {
        if (!parent.avatar_id) return null;
        return this.dataLoader.relationBatchOne(FileEntity).load(parent.avatar_id);
    }

    @Query(() => TemplatesModel, { name: 'templates_list' })
    async index(@Args('body') body: BasePaginationInput): Promise<IPaginatedType<TemplateEntity>> {
        return this.templatesService.search(body);
    }

    @Query(() => TemplateEntity, { name: 'templates_detail' })
    async view(@Args('id', { type: () => Number }) id: number): Promise<TemplateEntity> {
        return this.templatesService.findOne(id).then((res) => {
            if (!res) throw new NotFoundException();
            return res;
        });
    }

    @Mutation(() => TemplateEntity, { name: 'templates_create' })
    async store(@Args('body') body: TemplateSaveInputDto, @AuthUser() auth: UserEntity): Promise<TemplateEntity> {
        return this.templatesService.create({ ...body, created_by: auth.id });
    }

    @Mutation(() => TemplateEntity, { name: 'templates_update' })
    async update(
        @Args('id', { type: () => Int }) id: number,
        @Args('body') body: TemplateSaveInputDto,
        @AuthUser() auth: UserEntity
    ): Promise<TemplateEntity> {
        return this.templatesService.updateOne(id, { ...body, updated_by: auth.id });
    }

    @Mutation(() => Boolean, { name: 'templates_delete' })
    async destroy(@Args('id', { type: () => Int }) id: number, @AuthUser() auth: UserEntity): Promise<boolean> {
        await this.templatesService.softDelete(id, auth.id);
        return true;
    }
}
