import { Injectable } from '@nestjs/common';
import { BaseService } from '../../../commons/bases/base.service';
import { TemplateEntity } from '../../../entities/template.entity';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';

@Injectable()
export class TemplatesService extends BaseService<TemplateEntity> {
    constructor(@InjectRepository(TemplateEntity) public readonly repo: Repository<TemplateEntity>) {
        super(repo);
    }
}
