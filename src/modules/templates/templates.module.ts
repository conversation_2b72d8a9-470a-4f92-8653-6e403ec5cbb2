import { Module } from '@nestjs/common';
import { TemplatesService } from './services/templates.service';
import { TemplatesResolver } from './resolvers/templates.resolver';
import { TemplateEntity } from '../../entities/template.entity';
import { TypeOrmModule } from '@nestjs/typeorm';

@Module({
    providers: [TemplatesService, TemplatesResolver],
    imports: [TypeOrmModule.forFeature([TemplateEntity])],
})
export class TemplatesModule {}
