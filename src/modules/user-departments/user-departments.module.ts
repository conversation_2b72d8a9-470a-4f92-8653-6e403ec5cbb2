import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { UserDepartmentEntity } from '../../entities/user-department.entity';
import { UserDepartmentsResolver } from './user-departments.resolver';
import { DataLoaderModule } from '../../data-loaders/data-loaders.module';

@Module({
    imports: [TypeOrmModule.forFeature([UserDepartmentEntity]), DataLoaderModule],
    providers: [UserDepartmentsResolver],
    exports: [],
})
export class UserDepartmentsModule {}
