import { Parent, ResolveField } from '@nestjs/graphql';
import { AuthResolver } from '../../commons/decorators/graphql.decorators';
import { UserDepartmentEntity } from '../../entities/user-department.entity';
import { DepartmentEntity } from '../../entities/department.entity';
import { UserEntity } from '../../entities/user.entity';
import { DataLoaderService } from '../../data-loaders/data-loaders.service';

@AuthResolver(UserDepartmentEntity)
export class UserDepartmentsResolver {
    constructor(private readonly dataLoader: DataLoaderService) {}

    @ResolveField(() => DepartmentEntity)
    async department(@Parent() userDept: UserDepartmentEntity): Promise<DepartmentEntity | null> {
        return this.dataLoader.relationBatchOne(DepartmentEntity).load(userDept.department_id);
    }

    @ResolveField(() => UserEntity)
    async user(@Parent() userDept: UserDepartmentEntity): Promise<UserEntity | null> {
        return this.dataLoader.relationBatchOne(UserEntity).load(userDept.user_id);
    }
}
