import { Query, Resolver } from '@nestjs/graphql';
import { LoginDeviceEntity } from '../../entities/device.entity';
import { AuthResolver } from '../../commons/decorators/graphql.decorators';
import { DevicesService } from './devices.service';
import { AuthUser } from '../auth/auth.decorator';
import { UserEntity } from '../../entities/user.entity';

@AuthResolver(LoginDeviceEntity)
@Resolver(() => LoginDeviceEntity)
export class DevicesResolver {
    constructor(private readonly devicesService: DevicesService) {}

    @Query(() => [LoginDeviceEntity], { name: 'login_devices_listing' })
    async devices(@AuthUser() user: UserEntity): Promise<LoginDeviceEntity[]> {
        return this.devicesService.findByUserId(user.id);
    }
}
