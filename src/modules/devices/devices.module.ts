import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { LoginDeviceEntity } from '../../entities/device.entity';
import { DevicesResolver } from './devices.resolver';
import { DevicesService } from './devices.service';

@Module({
    imports: [TypeOrmModule.forFeature([LoginDeviceEntity])],
    providers: [DevicesResolver, DevicesService],
    exports: [DevicesService],
})
export class DevicesModule {}
