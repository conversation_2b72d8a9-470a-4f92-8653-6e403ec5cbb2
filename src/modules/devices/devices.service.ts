import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { LoginDeviceEntity } from '../../entities/device.entity';
import { Request } from 'express';
import { BaseService } from '../../commons/bases/base.service';

@Injectable()
export class DevicesService extends BaseService<LoginDeviceEntity> {
    constructor(@InjectRepository(LoginDeviceEntity) public readonly repo: Repository<LoginDeviceEntity>) {
        super(repo);
    }

    async findByUserId(userId: number): Promise<LoginDeviceEntity[]> {
        return this.repo.find({
            where: {
                user_id: userId,
            },
            order: { created_at: 'DESC' },
        });
    }

    async saveDevice(userId: number, req: Request): Promise<LoginDeviceEntity> {
        const ipAddress = req.ip || req.connection.remoteAddress || (req.headers['x-forwarded-for'] as string);
        const userAgent = req.headers['user-agent'];
        const { platform, os, browser } = req.useragent || {};

        const deviceByUserId = await this.repo.findOneBy({
            user_id: userId,
            user_agent: userAgent,
        });

        if (deviceByUserId) {
            return await this.repo.save({
                ...deviceByUserId,
                raw: req.useragent || {},
                updated_at: new Date(),
                updated_by: userId,
            });
        } else {
            return await this.repo.save({
                user_id: userId,
                ip_address: ipAddress,
                platform: platform,
                raw: req.useragent || {},
                os: os,
                user_agent: userAgent,
                browser: browser,
            });
        }
    }
}
