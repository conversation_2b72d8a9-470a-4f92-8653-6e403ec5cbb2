import { Modu<PERSON> } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { IssueEntity } from '../../entities/issue.entity';
import { IssuesService } from './services/issues.service';
import { IssuesResolver } from './resolvers/issues.resolver';
import { IssuePageEntity } from '../../entities/issue-page.entity';
import { IssuePagesService } from './services/issue-pages.service';
import { IssuePagesResolver } from './resolvers/issue-pages.resolver';
import { IssuePageFileEntity } from '../../entities/issue-page-file.entity';
import { IssuePageFilesService } from './services/issue-page-files.service';
import { IssuePageFilesResolver } from './resolvers/issue-page-files.resolver';

@Module({
    imports: [TypeOrmModule.forFeature([IssueEntity, IssuePageEntity, IssuePageFileEntity])],
    providers: [
        IssuesService,
        IssuesResolver,
        IssuePagesService,
        IssuePagesResolver,
        IssuePageFilesService,
        IssuePageFilesResolver,
    ],
})
export class IssuesModule {}
