import { Injectable, NotFoundException } from '@nestjs/common';
import { BaseService } from '../../../commons/bases/base.service';
import { IssueEntity } from '../../../entities/issue.entity';
import { InjectRepository } from '@nestjs/typeorm';
import { EntityNotFoundError, In, Repository } from 'typeorm';
import { IssueSaveInputDto } from '../dtos/issue-save-input.dto';
import { ItemStatus } from '../../../commons/enums.common';
import { PressPublicationEntity } from '../../../entities/press-publication.entity';
import { IssuePageEntity } from '../../../entities/issue-page.entity';

@Injectable()
export class IssuesService extends BaseService<IssueEntity> {
    constructor(@InjectRepository(IssueEntity) public readonly repo: Repository<IssueEntity>) {
        super(repo);
    }

    async saveIssue(data: IssueSaveInputDto, userId: number, id?: number): Promise<IssueEntity> {
        // Khi tạo/ sửa issues vs status_id = active => check page_count > 0 thì tự động tạo bản ghi trong bảng issue_pages (check theo issue_id)
        // Logic: for page = 1 to issues.press_publications.page_count => cho vào page_number, name = Trang + page (vd Trang 1, Trang 2), status_id = active
        // Trong TH issue_pages có r thì check tồn tại, nếu thiếu thì thêm, thừa thì set status_id cái thừa về inactive
        let issue = id
            ? await this.repo.findOneOrFail({ where: { id } }).catch((error) => {
                  if (error instanceof EntityNotFoundError) {
                      throw new NotFoundException();
                  }
                  throw error;
              })
            : this.repo.create({ ...data, created_by: userId });
        if (!issue) throw new NotFoundException();
        const pressPublication = await this.repo.manager.findOneOrFail(PressPublicationEntity, {
            where: { id: data.press_publication_id },
        });
        if (data.status_id === ItemStatus.ACTIVE && pressPublication.page_count > 0) {
            return this.repo.manager.transaction(async (manager) => {
                if (id) {
                    issue = await manager.save(IssueEntity, { ...data, updated_by: userId, id });
                } else {
                    issue = await manager.save(issue);
                }
                const issuePages = await manager.find(IssuePageEntity, { where: { issue_id: issue.id } });
                const pageNumbers = issuePages.map((page) => page.page_number);
                const pagesNeedToCreate = Array.from({ length: pressPublication.page_count }, (_, i) => i + 1).filter(
                    (page) => !pageNumbers.includes(page)
                );
                const pagesNeedToDelete = pageNumbers.filter((page) => page > pressPublication.page_count);
                if (pagesNeedToCreate.length > 0) {
                    const newPages = pagesNeedToCreate.map((page) =>
                        manager.create(IssuePageEntity, {
                            name: `Trang ${page}`,
                            page_number: page,
                            status_id: ItemStatus.ACTIVE,
                            press_publication_id: pressPublication.id,
                            issue_id: issue.id,
                            department_id: issue.department_id,
                            created_by: userId,
                        })
                    );
                    await manager.save(IssuePageEntity, newPages);
                }
                if (pagesNeedToDelete.length > 0) {
                    await manager.update(
                        IssuePageEntity,
                        {
                            issue_id: issue.id,
                            page_number: In(pagesNeedToDelete),
                        },
                        {
                            status_id: ItemStatus.INACTIVE,
                            updated_by: userId,
                        }
                    );
                }
                return issue;
            });
        } else {
            if (id) {
                return this.updateOne(id, { ...data, updated_by: userId });
            }
            return this.create({ ...data, created_by: userId });
        }
    }
}
