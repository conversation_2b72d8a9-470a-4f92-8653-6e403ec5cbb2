import { Injectable } from '@nestjs/common';
import { BaseService } from '../../../commons/bases/base.service';
import { IssuePageEntity } from '../../../entities/issue-page.entity';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';

@Injectable()
export class IssuePagesService extends BaseService<IssuePageEntity> {
    constructor(@InjectRepository(IssuePageEntity) public readonly repo: Repository<IssuePageEntity>) {
        super(repo);
    }
}
