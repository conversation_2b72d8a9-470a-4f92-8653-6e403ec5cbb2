import { Injectable } from '@nestjs/common';
import { BaseService } from '../../../commons/bases/base.service';
import { IssuePageFileEntity } from '../../../entities/issue-page-file.entity';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';

@Injectable()
export class IssuePageFilesService extends BaseService<IssuePageFileEntity> {
    constructor(@InjectRepository(IssuePageFileEntity) public readonly repo: Repository<IssuePageFileEntity>) {
        super(repo);
    }
}
