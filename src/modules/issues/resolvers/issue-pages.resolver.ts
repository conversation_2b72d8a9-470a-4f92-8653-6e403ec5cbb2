import { Args, Int, Mutation, Parent, Query, ResolveField } from '@nestjs/graphql';
import { NotFoundException } from '@nestjs/common';
import { AuthResolver } from '../../../commons/decorators/graphql.decorators';
import { DataLoaderService } from '../../../data-loaders/data-loaders.service';
import { IssuePageEntity } from '../../../entities/issue-page.entity';
import { DepartmentEntity } from '../../../entities/department.entity';
import { PressPublicationEntity } from '../../../entities/press-publication.entity';
import { IssueEntity } from '../../../entities/issue.entity';
import { UserEntity } from '../../../entities/user.entity';
import { IssuePagesService } from '../services/issue-pages.service';
import { IssuePagesModel } from '../models/issue-pages.model';
import { BasePaginationInput } from '../../../commons/bases/base.input';
import { IPaginatedType } from '../../../commons/bases/base.model';
import { AuthUser } from '../../auth/auth.decorator';
import { IssuePageSaveInputDto } from '../dtos/issue-page-save-input.dto';
import { ArticleIssuePageEntity } from '../../../entities/article-issue-page.entity';
import { IssuePageChangeApproveStatusInputDto } from '../dtos/issue-page-change-approve-status-input.dto';

@AuthResolver(IssuePageEntity)
export class IssuePagesResolver {
    constructor(
        private readonly issuePagesService: IssuePagesService,
        private readonly dataLoader: DataLoaderService
    ) {}

    @ResolveField(() => DepartmentEntity, { nullable: true })
    async department(@Parent() parent: IssuePageEntity): Promise<DepartmentEntity | null> {
        if (!parent.department_id) return null;
        return this.dataLoader.relationBatchOne(DepartmentEntity).load(parent.department_id);
    }

    @ResolveField(() => PressPublicationEntity, { nullable: true })
    async pressPublication(@Parent() parent: IssuePageEntity): Promise<PressPublicationEntity | null> {
        if (!parent.press_publication_id) return null;
        return this.dataLoader.relationBatchOne(PressPublicationEntity).load(parent.press_publication_id);
    }

    @ResolveField(() => IssueEntity, { nullable: true })
    async issue(@Parent() parent: IssuePageEntity): Promise<IssueEntity | null> {
        if (!parent.issue_id) return null;
        return this.dataLoader.relationBatchOne(IssueEntity).load(parent.issue_id);
    }

    @ResolveField(() => [ArticleIssuePageEntity], { nullable: true })
    async articleIssuePages(@Parent() parent: IssuePageEntity): Promise<ArticleIssuePageEntity[]> {
        return this.dataLoader.relationBatchOneMany(ArticleIssuePageEntity, 'issuePage').load(parent.id);
    }

    @Query(() => IssuePagesModel, { name: 'issue_pages_list' })
    async index(@Args('body') body: BasePaginationInput): Promise<IPaginatedType<IssuePageEntity>> {
        return this.issuePagesService.search(body);
    }

    @Query(() => IssuePageEntity, { name: 'issue_pages_detail' })
    async view(@Args('id', { type: () => Int }) id: number): Promise<IssuePageEntity> {
        return this.issuePagesService.findOne(id).then((res) => {
            if (!res) throw new NotFoundException();
            return res;
        });
    }

    @Mutation(() => IssuePageEntity, { name: 'issue_pages_create' })
    async store(@Args('body') body: IssuePageSaveInputDto, @AuthUser() auth: UserEntity): Promise<IssuePageEntity> {
        return this.issuePagesService.create({ ...body, created_by: auth.id });
    }

    @Mutation(() => IssuePageEntity, { name: 'issue_pages_update' })
    async update(
        @Args('id', { type: () => Int }) id: number,
        @Args('body') body: IssuePageSaveInputDto,
        @AuthUser() auth: UserEntity
    ): Promise<IssuePageEntity> {
        return this.issuePagesService.updateOne(id, { ...body, updated_by: auth.id });
    }

    @Mutation(() => Boolean, { name: 'issue_pages_delete' })
    async destroy(@Args('id', { type: () => Int }) id: number, @AuthUser() auth: UserEntity): Promise<boolean> {
        await this.issuePagesService.softDelete(id, auth.id);
        return true;
    }

    @Mutation(() => Boolean, { name: 'issue_pages_change_approve_status' })
    async changeApproveStatus(
        @Args('id', { type: () => Int }) id: number,
        @Args('body') body: IssuePageChangeApproveStatusInputDto,
        @AuthUser() auth: UserEntity
    ): Promise<boolean> {
        await this.issuePagesService.updateOne(id, { ...body, updated_by: auth.id });
        return true;
    }
}
