import { Args, Int, Mutation, Parent, Query, ResolveField } from '@nestjs/graphql';
import { NotFoundException } from '@nestjs/common';
import { AuthResolver } from '../../../commons/decorators/graphql.decorators';
import { DataLoaderService } from '../../../data-loaders/data-loaders.service';
import { IssueEntity } from '../../../entities/issue.entity';
import { DepartmentEntity } from '../../../entities/department.entity';
import { FileEntity } from '../../../entities/file.entity';
import { PressPublicationEntity } from '../../../entities/press-publication.entity';
import { UserEntity } from '../../../entities/user.entity';
import { IssuesService } from '../services/issues.service';
import { IssuesModel } from '../models/issues.model';
import { IssueSaveInputDto } from '../dtos/issue-save-input.dto';
import { BasePaginationInput } from '../../../commons/bases/base.input';
import { IPaginatedType } from '../../../commons/bases/base.model';
import { AuthUser } from '../../auth/auth.decorator';
import { IssuePageEntity } from '../../../entities/issue-page.entity';
import { IssuePageFileEntity } from '../../../entities/issue-page-file.entity';
import { ArticleEntity } from '../../../entities/article.entity';

@AuthResolver(IssueEntity)
export class IssuesResolver {
    constructor(
        private readonly issuesService: IssuesService,
        private readonly dataLoader: DataLoaderService
    ) {}

    @ResolveField(() => DepartmentEntity, { nullable: true })
    async department(@Parent() parent: IssueEntity): Promise<DepartmentEntity | null> {
        if (!parent.department_id) return null;
        return this.dataLoader.relationBatchOne(DepartmentEntity).load(parent.department_id);
    }

    @ResolveField(() => PressPublicationEntity, { nullable: true })
    async pressPublication(@Parent() parent: IssueEntity): Promise<PressPublicationEntity | null> {
        if (!parent.press_publication_id) return null;
        return this.dataLoader.relationBatchOne(PressPublicationEntity).load(parent.press_publication_id);
    }

    @ResolveField(() => FileEntity, { nullable: true })
    async avatar(@Parent() parent: IssueEntity): Promise<FileEntity | null> {
        if (!parent.avatar_id) return null;
        return this.dataLoader.relationBatchOne(FileEntity).load(parent.avatar_id);
    }

    @ResolveField(() => FileEntity, { nullable: true })
    async file(@Parent() parent: IssueEntity): Promise<FileEntity | null> {
        if (!parent.file_id) return null;
        return this.dataLoader.relationBatchOne(FileEntity).load(parent.file_id);
    }

    @ResolveField(() => [IssuePageEntity], { nullable: true })
    async issuePages(@Parent() parent: IssueEntity): Promise<IssuePageEntity[]> {
        return this.dataLoader.relationBatchOneMany(IssuePageEntity, 'issue').load(parent.id);
    }

    @ResolveField(() => [ArticleEntity], { nullable: true })
    async articles(@Parent() parent: IssueEntity): Promise<ArticleEntity[]> {
        return this.dataLoader.relationBatchOneMany(ArticleEntity, 'issue').load(parent.id);
    }

    @ResolveField(() => [IssuePageFileEntity], { nullable: true })
    async issuePageFiles(@Parent() parent: IssueEntity): Promise<IssuePageFileEntity[]> {
        return this.dataLoader.relationBatchOneMany(IssuePageFileEntity, 'issue').load(parent.id);
    }

    @Query(() => IssuesModel, { name: 'issues_list' })
    async index(@Args('body') body: BasePaginationInput): Promise<IPaginatedType<IssueEntity>> {
        return this.issuesService.search(body);
    }

    @Query(() => IssueEntity, { name: 'issues_detail' })
    async view(@Args('id', { type: () => Int }) id: number): Promise<IssueEntity> {
        return this.issuesService.findOne(id).then((res) => {
            if (!res) throw new NotFoundException();
            return res;
        });
    }

    @Mutation(() => IssueEntity, { name: 'issues_create' })
    async store(@Args('body') body: IssueSaveInputDto, @AuthUser() auth: UserEntity): Promise<IssueEntity> {
        return this.issuesService.saveIssue(body, auth.id);
    }

    @Mutation(() => IssueEntity, { name: 'issues_update' })
    async update(
        @Args('id', { type: () => Int }) id: number,
        @Args('body') body: IssueSaveInputDto,
        @AuthUser() auth: UserEntity
    ): Promise<IssueEntity> {
        return this.issuesService.saveIssue(body, auth.id, id);
    }

    @Mutation(() => Boolean, { name: 'issues_delete' })
    async destroy(@Args('id', { type: () => Int }) id: number, @AuthUser() auth: UserEntity): Promise<boolean> {
        await this.issuesService.softDelete(id, auth.id);
        return true;
    }
}
