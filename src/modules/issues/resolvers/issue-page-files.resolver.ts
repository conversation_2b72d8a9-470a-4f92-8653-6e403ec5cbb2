import { Args, Int, Mutation, Parent, Query, ResolveField } from '@nestjs/graphql';
import { NotFoundException } from '@nestjs/common';
import { IssuePageFileEntity } from '../../../entities/issue-page-file.entity';
import { IssuePageFilesService } from '../services/issue-page-files.service';
import { BasePaginationInput } from '../../../commons/bases/base.input';
import { IssuePageFilesModel } from '../models/issue-page-files.model';
import { IssuePageFileSaveInputDto } from '../dtos/issue-page-file-save-input.dto';
import { IssuePageFileCommentSaveInputDto } from '../dtos/issue-page-file-comment-save-input.dto';
import { UserEntity } from '../../../entities/user.entity';
import { IssueEntity } from '../../../entities/issue.entity';
import { FileEntity } from '../../../entities/file.entity';
import { AuthResolver } from '../../../commons/decorators/graphql.decorators';
import { DataLoaderService } from 'src/data-loaders/data-loaders.service';
import { IPaginatedType } from '../../../commons/bases/base.model';
import { AuthUser } from '../../auth/auth.decorator';
import { uniq } from 'lodash';

@AuthResolver(IssuePageFileEntity)
export class IssuePageFilesResolver {
    constructor(
        private readonly issuePageFilesService: IssuePageFilesService,
        private readonly dataLoader: DataLoaderService
    ) {}

    @ResolveField(() => IssueEntity, { nullable: true })
    async issue(@Parent() parent: IssuePageFileEntity): Promise<IssueEntity | null> {
        if (!parent.issue_id) return null;
        return this.dataLoader.relationBatchOne(IssueEntity).load(parent.issue_id);
    }

    @ResolveField(() => FileEntity, { nullable: true })
    async file(@Parent() parent: IssuePageFileEntity): Promise<FileEntity | null> {
        if (!parent.file_id) return null;
        return this.dataLoader.relationBatchOne(FileEntity).load(parent.file_id);
    }

    @ResolveField(() => [FileEntity], { nullable: true })
    async files(@Parent() parent: IssuePageFileEntity): Promise<FileEntity[]> {
        if (!parent.file_ids || parent.file_ids.length === 0) return [];
        const files = await Promise.all(
            parent.file_ids.map((fileId) => this.dataLoader.relationBatchOne(FileEntity).load(fileId))
        );
        return files.filter((file) => file !== null);
    }

    @Query(() => IssuePageFilesModel, { name: 'issue_page_files_list' })
    async index(@Args('body') body: BasePaginationInput): Promise<IPaginatedType<IssuePageFileEntity>> {
        return this.issuePageFilesService.search(body);
    }

    @Query(() => IssuePageFileEntity, { name: 'issue_page_files_detail' })
    async view(@Args('id', { type: () => Int }) id: number): Promise<IssuePageFileEntity> {
        return this.issuePageFilesService.findOne(id).then((res) => {
            if (!res) throw new NotFoundException();
            return res;
        });
    }

    @Mutation(() => IssuePageFileEntity, { name: 'issue_page_files_create' })
    async store(
        @Args('body') body: IssuePageFileSaveInputDto,
        @AuthUser() auth: UserEntity
    ): Promise<IssuePageFileEntity> {
        return this.issuePageFilesService.create({ ...body, file_ids: [body.file_id], created_by: auth.id });
    }

    @Mutation(() => IssuePageFileEntity, { name: 'issue_page_files_update' })
    async update(
        @Args('id', { type: () => Int }) id: number,
        @Args('body') body: IssuePageFileSaveInputDto,
        @AuthUser() auth: UserEntity
    ): Promise<IssuePageFileEntity> {
        const oldIpf = await this.issuePageFilesService.findOne(id);
        if (!oldIpf) throw new NotFoundException();
        const file_ids = uniq([...(oldIpf.file_ids ?? []), body.file_id]);
        return this.issuePageFilesService.updateOne(id, { ...body, file_ids, updated_by: auth.id });
    }

    @Mutation(() => Boolean, { name: 'issue_page_files_delete' })
    async destroy(@Args('id', { type: () => Int }) id: number, @AuthUser() auth: UserEntity): Promise<boolean> {
        await this.issuePageFilesService.softDelete(id, auth.id);
        return true;
    }

    @Mutation(() => IssuePageFileEntity, { name: 'issue_page_files_update_comment' })
    async updateComment(
        @Args('id', { type: () => Int }) id: number,
        @Args('body') body: IssuePageFileCommentSaveInputDto,
        @AuthUser() auth: UserEntity
    ): Promise<IssuePageFileEntity> {
        const issuePageFile = await this.issuePageFilesService.findOne(id);
        if (!issuePageFile) throw new NotFoundException();
        if (issuePageFile.file_id !== body.file_id) throw new NotFoundException();
        const fileComments = issuePageFile.file_comments ?? [];
        const index = fileComments.findIndex((comment) => comment.file_id === body.file_id);
        if (index === -1) {
            fileComments.push({ file_id: body.file_id, comments: [body.comment] });
        } else {
            fileComments[index].comments.push(body.comment);
        }
        return this.issuePageFilesService.updateOne(id, {
            file_comments: fileComments,
            updated_by: auth.id,
        });
    }
}
