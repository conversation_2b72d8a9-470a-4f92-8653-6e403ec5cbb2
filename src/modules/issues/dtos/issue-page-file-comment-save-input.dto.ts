import { Field, InputType, Int } from '@nestjs/graphql';
import { IsNotEmpty, IsString } from 'class-validator';
import { validationMessagesLang } from '../../../languages/validation-messages.lang';
import { IdExists } from '../../../commons/validators/id-exists.validator';
import { AutoSanitize } from '../../../commons/decorators/sanitize.decorators';
import { FileEntity } from '../../../entities/file.entity';

@InputType()
@AutoSanitize()
export class IssuePageFileCommentSaveInputDto {
    @Field(() => Int)
    @IsNotEmpty({ message: validationMessagesLang.REQUIRED })
    @IdExists(FileEntity)
    file_id: number;

    @Field()
    @IsNotEmpty({ message: validationMessagesLang.REQUIRED })
    @IsString()
    comment: string;
}
