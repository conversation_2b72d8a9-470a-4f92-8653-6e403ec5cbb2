import { Field, InputType, Int } from '@nestjs/graphql';
import { IsEnum } from 'class-validator';
import { ItemStatus } from '../../../commons/enums.common';
import { AutoSanitize } from '../../../commons/decorators/sanitize.decorators';

@InputType()
@AutoSanitize()
export class IssuePageChangeApproveStatusInputDto {
    @Field(() => Int)
    @IsEnum(ItemStatus)
    approve_status_id: ItemStatus;
}
