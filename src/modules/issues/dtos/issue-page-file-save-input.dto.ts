import { Field, InputType, Int } from '@nestjs/graphql';
import { IsArray, IsEnum, IsNotEmpty, IsOptional, IsString } from 'class-validator';
import { validationMessagesLang } from '../../../languages/validation-messages.lang';
import { BaseUpdateInputDto } from '../../../commons/bases/base.input';
import { IdExists } from '../../../commons/validators/id-exists.validator';
import { IssueEntity } from '../../../entities/issue.entity';
import { FileEntity } from '../../../entities/file.entity';
import { AutoSanitize } from '../../../commons/decorators/sanitize.decorators';
import GraphQLJSON from 'graphql-type-json';
import { IssuePageEntity } from '../../../entities/issue-page.entity';
import { ItemStatus } from '../../../commons/enums.common';

@InputType()
@AutoSanitize()
export class IssuePageFileSaveInputDto extends BaseUpdateInputDto {
    @Field(() => Int)
    @IsNotEmpty({ message: validationMessagesLang.REQUIRED })
    @IdExists(IssueEntity)
    issue_id: number;

    @Field(() => [Int], { nullable: true })
    @IsOptional()
    @IsArray()
    @IdExists(IssuePageEntity, { each: true })
    issue_page_id?: number[];

    @Field(() => Int, { nullable: true })
    @IsNotEmpty({ message: validationMessagesLang.REQUIRED })
    @IdExists(FileEntity)
    file_id: number;

    @Field({ nullable: true })
    @IsOptional()
    @IsString()
    comment?: string;

    @Field(() => GraphQLJSON, { nullable: true })
    @IsOptional()
    file_comments?: any;

    @Field(() => Int)
    @IsEnum(ItemStatus)
    status_id: ItemStatus;

    @Field({ nullable: true })
    @IsOptional()
    @IsString()
    name?: string;
}
