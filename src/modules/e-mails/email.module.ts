import { MailerModule } from '@nestjs-modules/mailer';
import { HandlebarsAdapter } from '@nestjs-modules/mailer/dist/adapters/handlebars.adapter';
import { Global, Module } from '@nestjs/common';
import { join } from 'path';
import { EmailService } from './email.service';
import appConf from '../../configs/app.conf';

@Global()
@Module({
    imports: [
        MailerModule.forRootAsync({
            useFactory: async () => ({
                transport: {
                    host: appConf.MAIL_HOST,
                    port: appConf.MAIL_PORT,
                    secure: false,
                    auth: {
                        user: appConf.MAIL_USER,
                        pass: appConf.MAIL_PASSWORD,
                    },
                },
                defaults: {
                    from: `\"No Reply\" <${appConf.MAIL_FROM}>`,
                },
                template: {
                    dir: join(process.cwd(), 'email-templates'),
                    adapter: new HandlebarsAdapter(),
                    options: {
                        strict: true,
                    },
                },
            }),
        }),
    ],
    providers: [EmailService],
    exports: [EmailService],
})
export class EmailModule {}
