import { Injectable } from '@nestjs/common';
import { MailerService } from '@nestjs-modules/mailer';
import { ISendMailOptions } from '@nestjs-modules/mailer/dist/interfaces/send-mail-options.interface';
import appConf from '../../configs/app.conf';

@Injectable()
export class EmailService {
    constructor(private readonly mailerService: MailerService) {}

    async sendEmail(option: ISendMailOptions) {
        try {
            await this.mailerService.sendMail(option);
        } catch (error) {
            console.log(error);
        }
    }

    async sendForgotPasswordEmail(name: string, email: string, otp: string) {
        await this.sendEmail({
            to: email,
            subject: 'Forgot Password Email',
            template: 'forgot_password', // Tên của file mẫu .hbs
            context: {
                name,
                otp,
                exp: appConf.OTP_EXPIRATION_TIME,
            },
        });
    }
}
