import { Injectable } from '@nestjs/common';
import { BaseService } from '../../commons/bases/base.service';
import { TagEntity } from '../../entities/tag.entity';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';

@Injectable()
export class TagsService extends BaseService<TagEntity> {
    constructor(@InjectRepository(TagEntity) private readonly repo: Repository<TagEntity>) {
        super(repo);
    }
}
