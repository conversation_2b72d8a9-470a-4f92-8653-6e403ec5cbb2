import { AuthResolver } from '../../commons/decorators/graphql.decorators';
import { TagEntity } from '../../entities/tag.entity';
import { TagsService } from './tags.service';
import { DataLoaderService } from '../../data-loaders/data-loaders.service';
import { Args, Int, Mutation, Parent, Query, ResolveField } from '@nestjs/graphql';
import { DepartmentEntity } from '../../entities/department.entity';
import { BasePaginationInput } from '../../commons/bases/base.input';
import { TagsModel } from './models/tags.model';
import { IPaginatedType } from '../../commons/bases/base.model';
import { NotFoundException } from '@nestjs/common';
import { AuthUser } from '../auth/auth.decorator';
import { UserEntity } from '../../entities/user.entity';
import { TagSaveInputDto } from './dtos/tag-save-input.dto';

@AuthResolver(TagEntity)
export class TagsResolver {
    constructor(
        private readonly tagsService: TagsService,
        private readonly dataLoader: DataLoaderService
    ) {}

    @ResolveField(() => DepartmentEntity, { nullable: true })
    async department(@Parent() parent: TagEntity): Promise<DepartmentEntity | null> {
        if (!parent.department_id) return null;
        return this.dataLoader.relationBatchOne(DepartmentEntity).load(parent.department_id);
    }

    @Query(() => TagsModel, { name: 'tags_list' })
    async index(@Args('body') body: BasePaginationInput): Promise<IPaginatedType<TagEntity>> {
        return this.tagsService.search(body);
    }

    @Query(() => TagEntity, { name: 'tags_detail' })
    async view(@Args('id', { type: () => Number }) id: number): Promise<TagEntity> {
        return this.tagsService.findOne(id).then((res) => {
            if (!res) throw new NotFoundException();
            return res;
        });
    }

    @Mutation(() => TagEntity, { name: 'tags_create' })
    async store(@Args('body') body: TagSaveInputDto, @AuthUser() auth: UserEntity): Promise<TagEntity> {
        return this.tagsService.create({ ...body, created_by: auth.id });
    }

    @Mutation(() => TagEntity, { name: 'tags_update' })
    async update(
        @Args('id', { type: () => Int }) id: number,
        @Args('body') body: TagSaveInputDto,
        @AuthUser() auth: UserEntity
    ): Promise<TagEntity> {
        return this.tagsService.updateOne(id, { ...body, updated_by: auth.id });
    }

    @Mutation(() => Boolean, { name: 'tags_delete' })
    async destroy(@Args('id', { type: () => Int }) id: number, @AuthUser() auth: UserEntity): Promise<Boolean> {
        await this.tagsService.softDelete(id, auth.id);
        return true;
    }
}
