import { <PERSON><PERSON><PERSON>y, IsBoolean, IsNotEmpty, IsOptional, IsString } from 'class-validator';
import { IdExists } from '../../../commons/validators/id-exists.validator';
import { DepartmentEntity } from '../../../entities/department.entity';
import { FolderEntity } from '../../../entities/folder.entity';
import { FileEntity } from '../../../entities/file.entity';
import { Transform } from 'class-transformer';

export class FileUploadInputDto {
    @IsNotEmpty()
    @IdExists(DepartmentEntity)
    department_id: number;

    @IsOptional()
    @IdExists(FolderEntity)
    folder_id?: number;

    @Transform(({ value }) => value === 'true')
    @IsBoolean()
    is_newsroom: boolean;

    @IsOptional()
    @IdExists(FileEntity)
    parent_id?: number;

    @Transform(({ value }) => {
        if (Array.isArray(value)) {
            return value;
        }
        return value ? JSON.parse(value) : null;
    })
    @IsOptional()
    @IsArray()
    @IsString({ each: true })
    @IsNotEmpty({ each: true })
    file_tags?: string[];

    @IsOptional()
    @IsString()
    file_title?: string;
}
