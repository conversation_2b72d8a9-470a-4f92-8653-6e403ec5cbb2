import { IsNotEmpty, IsString } from 'class-validator';
import { IdExists } from '../../../commons/validators/id-exists.validator';
import { Field, InputType, Int } from '@nestjs/graphql';
import { FileEntity } from '../../../entities/file.entity';

@InputType()
export class FileTitleChangeInputDto {
    @IsNotEmpty()
    @Field(() => Int)
    @IdExists(FileEntity)
    file_id: number;

    @IsNotEmpty()
    @IsString()
    @Field(() => String)
    file_title: string;
}
