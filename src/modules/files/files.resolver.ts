import { Args, Int, Mutation, Parent, Query, ResolveField } from '@nestjs/graphql';
import { NotFoundException } from '@nestjs/common';
import { FilesModel } from './models/files.model';
import { BasePaginationInput } from '../../commons/bases/base.input';
import { IPaginatedType } from '../../commons/bases/base.model';
import { FilesService } from './files.service';
import { FileEntity } from '../../entities/file.entity';
import { FileNameChangeInputDto } from './dtos/file-name-change.input.dto';
import { AuthUser } from '../auth/auth.decorator';
import { UserEntity } from '../../entities/user.entity';
import { AuthResolver } from '../../commons/decorators/graphql.decorators';
import { DataLoaderService } from '../../data-loaders/data-loaders.service';
import { TagEntity } from '../../entities/tag.entity';
import { FileTitleChangeInputDto } from './dtos/file-title-change.input.dto';

@AuthResolver(FileEntity)
export class FilesResolver {
    constructor(
        private readonly filesService: FilesService,
        private readonly dataLoader: DataLoaderService
    ) {}

    @ResolveField(() => [TagEntity], { nullable: true })
    async tags(@Parent() parent: FileEntity): Promise<TagEntity[]> {
        return this.dataLoader.relationBatchManyMany(TagEntity, 'files').load(parent.id);
    }

    @ResolveField(() => FileEntity, { nullable: true })
    async parent(@Parent() parent: FileEntity): Promise<FileEntity | null> {
        if (!parent.parent_id) return null;
        return this.dataLoader.relationBatchOne(FileEntity).load(parent.parent_id);
    }

    @Query(() => FilesModel, { name: 'files_list' })
    async index(@Args('body') body: BasePaginationInput): Promise<IPaginatedType<FileEntity>> {
        return this.filesService.search(body);
    }

    @Query(() => FileEntity, { name: 'files_by_url' })
    async getByUrl(@Args('file_url') fileUrl: string): Promise<FileEntity> {
        const file = await this.filesService.findByFileUrl(fileUrl);
        if (!file) {
            throw new NotFoundException('File not found');
        }
        return file;
    }

    @Mutation(() => FileEntity, { name: 'file_change_name' })
    async changeName(@Args('body') body: FileNameChangeInputDto, @AuthUser() auth: UserEntity): Promise<FileEntity> {
        return this.filesService.updateOne(body.file_id, {
            file_name: body.file_name,
            updated_by: auth.id,
        });
    }

    @Mutation(() => FileEntity, { name: 'file_change_title' })
    async changeTitle(@Args('body') body: FileTitleChangeInputDto, @AuthUser() auth: UserEntity): Promise<FileEntity> {
        return this.filesService.updateOne(body.file_id, {
            file_title: body.file_title,
            updated_by: auth.id,
        });
    }

    @Mutation(() => Boolean, { name: 'file_delete' })
    async delete(@Args('id', { type: () => Int }) id: number): Promise<boolean> {
        await this.filesService.delete(id);
        return true;
    }
}
