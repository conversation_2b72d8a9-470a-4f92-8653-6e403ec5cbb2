
import { BadRequestException, Injectable, NestMiddleware } from '@nestjs/common';
import { Express, NextFunction, Request, Response } from 'express';
import * as fs from 'fs';
import { promisify } from 'util';
import { multerConfig } from '../../configs/multer.conf';

// Mở rộng interface Request để hỗ trợ các thuộc tính file upload
declare global {
    namespace Express {
        interface Request {
            file?: Express.Multer.File;
            files?:
                | {
                      [fieldname: string]: Express.Multer.File[];
                  }
                | Express.Multer.File[];
        }
    }
}

/**
 * Middleware để kiểm tra bảo mật file sau khi upload
 * Trong môi trường production, bạn nên tích hợp với một dịch vụ quét virus như ClamAV
 */
@Injectable()
export class FileSecurityMiddleware implements NestMiddleware {
    /**
     * Danh sách các signature nguy hiểm để phát hiện mã độc trong file
     *
     * Lưu ý:
     * 1. Danh sách này chỉ là lớp bảo vệ cơ bản và không thể phát hiện tất cả các loại mã độc
     * 2. Một số mẫu có thể gây ra false positives trong nội dung hợp pháp
     * 3. Trong môi trường sản xuất, nên kết hợp với các giải pháp quét virus chuyên nghiệp
     * 4. Danh sách này nên được cập nhật thường xuyên khi phát hiện các mẫu tấn công mới
     */
    private readonly dangerousSignatures = [
        // PHP code
        '<?php',
        '<?=',
        '<?',
        '.php',
        '.phtml',
        '.php3',
        '.php4',
        '.php5',
        '.phps',

        // JavaScript injection
        '<script', // Không có dấu > để bắt cả các biến thể
        'javascript:',
        'vbscript:',
        'data:text/html',
        'document.cookie',
        'document.write(',
        'document.location',
        'window.location',

        // Event handlers
        'onabort=',
        'onblur=',
        'onchange=',
        'onclick=',
        'ondblclick=',
        'onerror=',
        'onfocus=',
        'onkeydown=',
        'onkeypress=',
        'onkeyup=',
        'onload=',
        'onmousedown=',
        'onmousemove=',
        'onmouseout=',
        'onmouseover=',
        'onmouseup=',
        'onreset=',
        'onresize=',
        'onselect=',
        'onsubmit=',
        'onunload=',

        // SQL injection
        'UNION SELECT',
        'UNION ALL SELECT',
        'INFORMATION_SCHEMA',
        'SELECT * FROM',
        'DROP TABLE',
        'DELETE FROM',
        'INSERT INTO',
        'EXEC(',
        'EXECUTE(',
        'WAITFOR DELAY',
        'CAST(',
        'CONVERT(',
        'DECLARE @', // Trong SQL Server, DECLARE @ dùng để khai báo biến

        // Command injection
        'eval(',
        'exec(',
        'system(',
        'shell_exec(',
        'passthru(',
        'popen(',
        'proc_open(',
        'pcntl_exec(',
        'assert(',
        'create_function(',
        'call_user_func(',
        'preg_replace', // Có thể dùng với /e modifier để thực thi mã

        // File inclusion
        'include(',
        'require(',
        'include_once(',
        'require_once(',

        // Directory traversal - cần cẩn thận với false positives
        '../../', // Ít nhất 2 cấp để giảm false positives
        '..\\\\..\\\\',
        '..%2f..%2f',
        '%2e%2e%2f%2e%2e%2f',

        // Configuration files
        '.htaccess',
        'web.config',
        'php.ini',
        'httpd.conf',

        // Embedded malware signatures
        'base64_decode(',
        'base64_encode(',
        'gzinflate(',
        'gzdeflate(',
        'str_rot13(',
        'chr(',
        'fromCharCode(',
        'String.fromCharCode(',
        'unescape(',
        'escape(',
        'unserialize(',
        '__wakeup',

        // Remote file inclusion
        'curl(',
        'file_get_contents(',
        'fopen(',
        'fsockopen(',
        'pfsockopen(',
        'stream_socket_client(',
        'socket_create(',
        'socket_connect(',

        // Email injection
        'mail(',
        'ezmlm_hash(',

        // File operations
        'unlink(',
        'rmdir(',
        'chmod(',
        'mkdir(',
        'fwrite(',
        'fputs(',
        'file_put_contents(',
        'copy(',
        'move_uploaded_file(',

        // Executable files
        '.exe',
        '.dll',
        '.bat',
        '.cmd',
        '.sh',
        '.jar',
        '.jsp',
        '.asp',
        '.aspx',
        '.cgi',
        '.pl',
        '.py',

        // XSS patterns
        '<img',
        '<iframe',
        '<object',
        '<embed',

        // Registry manipulation (Windows)
        'win32_create_service(',
        'win32_delete_service(',
        'win32_start_service(',

        // Cryptographic (có thể dùng để giải mã mã độc)
        'mcrypt_encrypt(',
        'mcrypt_decrypt(',
        'crypt(',

        // Webshell signatures
        'phpinfo()',
        'c99shell',
        'r57shell',
        'webshell',
        'b374k',
        'weevely',
        'wso shell',
        'filesman',
        'backdoor',
        'rootkit',
        'hacktools',
        'hacktool',
        'netcat',
        'nc.exe',
        'trojan',
        'malware',
        'exploit',

        // Crypto miners
        'coinhive',
        'cryptonight',
        'miner',
        'monero',
        'stratum+tcp',

        // Obfuscation techniques
        'eval(base64_decode(',
        'eval(gzinflate(',
        'eval(str_rot13(',
        'document.write(unescape(',
        'String.fromCharCode(',
    ];

    // Sử dụng danh sách các loại MIME được phép từ cấu hình
    private readonly allowedMimeTypes = multerConfig.allowedMimeTypes;

    // Đọc file dưới dạng promise
    private readFileAsync = promisify(fs.readFile);

    async use(req: Request, res: Response, next: NextFunction) {
        // Chỉ áp dụng cho route upload file
        if (!req.route.path.includes('upload')) {
            return next();
        }

        // Kiểm tra nếu có file được upload
        if (!req.file && (!req.files || Object.keys(req.files).length === 0)) {
            return next();
        }

        try {
            // Xử lý các file được upload
            let files: Express.Multer.File[] = [];

            if (req.file) {
                // Single file upload
                files = [req.file];
            } else if (req.files) {
                // Multiple files upload
                if (Array.isArray(req.files)) {
                    files = req.files;
                } else {
                    // req.files là object với các field
                    const filesObj = req.files as { [fieldname: string]: Express.Multer.File[] };
                    Object.keys(filesObj).forEach((key) => {
                        const fieldFiles = filesObj[key];
                        if (Array.isArray(fieldFiles)) {
                            files = files.concat(fieldFiles);
                        }
                    });
                }
            }

            for (const file of files) {
                // Kiểm tra file có tồn tại không
                if (!file.path || !fs.existsSync(file.path)) {
                    continue;
                }

                try {
                    // Đọc nội dung file
                    const fileBuffer = await this.readFileAsync(file.path);

                    // Kiểm tra nội dung file có phải là file hợp lệ không
                    const fileTypeFromBuffer = await import('file-type');
                    const fileInfo = await fileTypeFromBuffer.fileTypeFromBuffer(fileBuffer);

                    // Nếu không xác định được loại file hoặc không phải file hợp lệ
                    if (!fileInfo || !this.allowedMimeTypes.includes(fileInfo.mime)) {
                        fs.unlinkSync(file.path);
                        const error = new BadRequestException(`Invalid file content detected and removed`);
                        return next(error);
                    }

                    // Kiểm tra xem mimetype có khớp với nội dung file không
                    if (fileInfo.mime !== file.mimetype) {
                        fs.unlinkSync(file.path);
                        const error = new BadRequestException('File content does not match the declared type!');
                        return next(error);
                    }

                    // Kiểm tra các signature nguy hiểm (chỉ cho các file text, bỏ qua binary files)
                    const isBinaryFile = file.mimetype.includes('zip') || 
                                       file.mimetype.includes('rar') ||
                                       file.mimetype.includes('pdf') ||
                                       file.mimetype.includes('audio/') ||
                                       file.mimetype.includes('msword') ||
                                       file.mimetype.includes('wordprocessingml');
                    
                    if (!isBinaryFile && fileBuffer.length < 10 * 1024 * 1024) {
                        // Chỉ kiểm tra nếu file nhỏ hơn 10MB và không phải binary file
                        try {
                            const fileContent = fileBuffer.toString('utf8');
                            for (const signature of this.dangerousSignatures) {
                                if (fileContent.includes(signature)) {
                                    // Xóa file nếu phát hiện signature nguy hiểm
                                    fs.unlinkSync(file.path);
                                    const error = new BadRequestException(
                                        `Potentially malicious file detected and removed`
                                    );
                                    return next(error);
                                }
                            }
                        } catch (e) {
                            // Lỗi khi chuyển đổi buffer sang string, có thể bỏ qua vì đây là file nhị phân
                        }
                    }
                } catch (error) {
                    // Nếu lỗi không phải BadRequestException, xóa file và ném lỗi
                    if (!(error instanceof BadRequestException)) {
                        if (fs.existsSync(file.path)) {
                            fs.unlinkSync(file.path);
                        }
                        // Tạo lỗi mới thay vì ném lại
                        const newError = new BadRequestException(`Error validating file: ${error.message}`);
                        return next(newError);
                    }
                    return next(error);
                }
            }

            next();
        } catch (error) {
            if (error instanceof BadRequestException) {
                return next(error);
            }
            return next(new BadRequestException(`File security check failed: ${error.message}`));
        }
    }
}
