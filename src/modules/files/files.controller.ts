import {
    Body,
    Controller,
    Get,
    InternalServerErrorException,
    Param,
    Post,
    Res,
    UploadedFile,
    UseGuards,
    UseInterceptors,
} from '@nestjs/common';
import { multerConfig, multerOptions } from '../../configs/multer.conf';
import { FilesService } from './files.service';
import { FileInterceptor } from '@nestjs/platform-express';
import { JwtAuthGuard } from '../auth/jwt-auth.guard';
import { FileUploadInputDto } from './dtos/file-upload-input.dto';
import { AuthUser } from '../auth/auth.decorator';

@Controller('files')
export class FilesController {
    constructor(private readonly filesService: FilesService) {}

    @Get('/:filePath/:datePath/:fileName')
    viewUploadFile(
        @Param('filePath') filePath: string,
        @Param('datePath') datePath: string,
        @Param('fileName') fileName: string,
        @Res() res: any
    ) {
        return res.sendFile(`${fileName}`, {
            root: `${multerConfig.dest}/${filePath}/${datePath}/`,
        });
    }

    @Post('/upload')
    @UseGuards(JwtAuthGuard)
    @UseInterceptors(FileInterceptor('file', multerOptions))
    async privateUpload(
        @UploadedFile() file: Express.Multer.File,
        @Body() body: FileUploadInputDto,
        @AuthUser() auth: any
    ) {
        const fileObj = await this.filesService.insert(file, auth, body);
        if (!fileObj) throw new InternalServerErrorException();
        return { upload: fileObj };
    }
}
