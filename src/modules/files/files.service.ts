import { Injectable } from '@nestjs/common';
import { BaseService } from '../../commons/bases/base.service';
import { FileEntity } from '../../entities/file.entity';
import { InjectRepository } from '@nestjs/typeorm';
import { In, Repository } from 'typeorm';
import slugify from 'slugify';
import appConf from '../../configs/app.conf';
import { convertUploadPath, normalizePath } from '../../commons/helpers/file.helper';
import { detectMimeType } from 'nodemailer/lib/mime-funcs';
import { UserEntity } from '../../entities/user.entity';
import { FileUploadInputDto } from './dtos/file-upload-input.dto';
import { TagEntity } from '../../entities/tag.entity';
import { ItemStatus } from '../../commons/enums.common';

@Injectable()
export class FilesService extends BaseService<FileEntity> {
    constructor(
        @InjectRepository(FileEntity)
        public readonly repo: Repository<FileEntity>
    ) {
        super(repo);
    }

    async findByFileUrl(fileUrl: string): Promise<FileEntity | null> {
        return this.repo.findOne({
            where: { file_url: fileUrl },
        });
    }

    async insert(file: Express.Multer.File, auth: UserEntity, uploadData: FileUploadInputDto) {
        return this.repo.manager.transaction(async (run) => {
            const fTags: TagEntity[] = [];
            if (uploadData.file_tags?.length) {
                //Nếu có file_tags thì thêm vào bảng file_tags (tìm tags theo name và department_id, nếu ko có thì tạo mới)
                const tags = await run.find(TagEntity, {
                    where: {
                        name: In(uploadData.file_tags),
                        department_id: uploadData.department_id,
                    },
                });

                //Lọc ra các tag cần update
                const tagNeedToUpdate = tags.filter((tag) => uploadData.file_tags?.includes(tag.name));
                fTags.push(...tagNeedToUpdate);

                //Lọc ra các tag cần tạo mới
                const tagNeedToCreate = uploadData.file_tags.filter((tag) => !tags.some((t) => t.name === tag));
                if (tagNeedToCreate.length > 0) {
                    const tagEntities = tagNeedToCreate.map((tag) =>
                        run.create(TagEntity, {
                            name: tag,
                            department_id: uploadData.department_id,
                            slug: slugify(tag, { lower: true }),
                            status_id: ItemStatus.ACTIVE,
                            created_by: auth.id,
                        })
                    );
                    const tagInsertResult = await run.save(tagEntities);
                    fTags.push(...tagInsertResult);
                }
            }
            const fileEntity = run.create(FileEntity, {
                file_name: slugify(file.originalname),
                file_url: normalizePath(file.path),
                file_size: file.size,
                mime_type: detectMimeType(file.path),
                folder_id: uploadData.folder_id,
                department_id: uploadData.department_id,
                created_by: auth.id,
                is_newsroom: uploadData.is_newsroom,
                parent_id: uploadData.parent_id,
                tags: fTags,
                file_title: uploadData.file_title,
            });
            const f = await run.save(fileEntity);
            return {
                id: f.id,
                name: f.file_name,
                url: appConf.API_URL + convertUploadPath(file.path),
                folder_id: f.folder_id,
                department_id: f.department_id,
                file_title: f.file_title,
            };
        });
    }
}
