import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { LayoutEntity } from '../../entities/layout.entity';
import { LayoutsService } from './layouts.service';
import { LayoutsResolver } from './layouts.resolver';

@Module({
    imports: [TypeOrmModule.forFeature([LayoutEntity])],
    providers: [LayoutsService, LayoutsResolver],
    exports: [LayoutsService],
})
export class LayoutsModule {}
