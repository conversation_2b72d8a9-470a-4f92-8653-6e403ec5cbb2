import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { LayoutEntity } from '../../entities/layout.entity';
import { BaseService } from '../../commons/bases/base.service';

@Injectable()
export class LayoutsService extends BaseService<LayoutEntity> {
    constructor(
        @InjectRepository(LayoutEntity)
        private readonly repo: Repository<LayoutEntity>
    ) {
        super(repo);
    }
}
