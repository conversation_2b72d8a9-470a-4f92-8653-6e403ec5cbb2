import { <PERSON>rg<PERSON>, <PERSON>t, <PERSON><PERSON>, ResolveField, Resolver } from '@nestjs/graphql';
import { NotFoundException } from '@nestjs/common';
import { AuthMutation, StaticAuthQuery } from '../../commons/decorators/graphql.decorators';
import { DataLoaderService } from '../../data-loaders/data-loaders.service';
import { LayoutEntity } from '../../entities/layout.entity';
import { LayoutsService } from './layouts.service';
import { LayoutSaveInputDto } from './dtos/layout-save-input.dto';
import { LayoutsModel } from './models/layouts.model';
import { BasePaginationInput } from '../../commons/bases/base.input';
import { UserEntity } from '../../entities/user.entity';
import { IPaginatedType } from '../../commons/bases/base.model';
import { AuthUser } from '../auth/auth.decorator';
import { CategoryEntity } from '../../entities/category.entity';
import { DepartmentEntity } from '../../entities/department.entity';
import { ArticleEntity } from '../../entities/article.entity';

@Resolver(() => LayoutEntity)
export class LayoutsResolver {
    constructor(
        private readonly layoutsService: LayoutsService,
        private readonly dataLoader: DataLoaderService
    ) {}

    @ResolveField(() => DepartmentEntity, { nullable: true })
    async department(@Parent() parent: LayoutEntity): Promise<DepartmentEntity | null> {
        if (!parent.department_id) return null;
        return this.dataLoader.relationBatchOne(DepartmentEntity).load(parent.department_id);
    }

    @ResolveField(() => [CategoryEntity], { nullable: true })
    async webCategories(@Parent() parent: LayoutEntity): Promise<CategoryEntity[]> {
        return this.dataLoader.relationBatchOneMany(CategoryEntity, 'categoryWebLayout').load(parent.id);
    }

    @ResolveField(() => [CategoryEntity], { nullable: true })
    async mobileCategories(@Parent() parent: LayoutEntity): Promise<CategoryEntity[]> {
        return this.dataLoader.relationBatchOneMany(CategoryEntity, 'categoryMobileLayout').load(parent.id);
    }

    @ResolveField(() => [CategoryEntity], { nullable: true })
    async articleWebCategories(@Parent() parent: LayoutEntity): Promise<CategoryEntity[]> {
        return this.dataLoader.relationBatchOneMany(CategoryEntity, 'categoryArticleWebLayout').load(parent.id);
    }

    @ResolveField(() => [CategoryEntity], { nullable: true })
    async articleMobileCategories(@Parent() parent: LayoutEntity): Promise<CategoryEntity[]> {
        return this.dataLoader.relationBatchOneMany(CategoryEntity, 'categoryArticleMobileLayout').load(parent.id);
    }

    @ResolveField(() => [ArticleEntity], { nullable: true })
    async webArticles(@Parent() parent: LayoutEntity): Promise<ArticleEntity[]> {
        return this.dataLoader.relationBatchOneMany(ArticleEntity, 'articleWebLayout').load(parent.id);
    }

    @ResolveField(() => [ArticleEntity], { nullable: true })
    async mobileArticles(@Parent() parent: LayoutEntity): Promise<ArticleEntity[]> {
        return this.dataLoader.relationBatchOneMany(ArticleEntity, 'articleMobileLayout').load(parent.id);
    }

    @StaticAuthQuery(() => LayoutsModel, { name: 'layouts_list' })
    async index(@Args('body') body: BasePaginationInput): Promise<IPaginatedType<LayoutEntity>> {
        return this.layoutsService.search(body);
    }

    @StaticAuthQuery(() => LayoutEntity, { name: 'layouts_detail' })
    async view(@Args('id', { type: () => Int }) id: number): Promise<LayoutEntity> {
        return this.layoutsService.findOne(id).then((res) => {
            if (!res) {
                throw new NotFoundException();
            }
            return res;
        });
    }

    @AuthMutation(() => LayoutEntity, { name: 'layouts_create' })
    async store(@Args('body') body: LayoutSaveInputDto, @AuthUser() auth: UserEntity): Promise<LayoutEntity> {
        return this.layoutsService.create({ ...body, created_by: auth.id });
    }

    @AuthMutation(() => LayoutEntity, { name: 'layouts_update' })
    async update(
        @Args('id', { type: () => Int }) id: number,
        @Args('body') body: LayoutSaveInputDto,
        @AuthUser() auth: UserEntity
    ): Promise<LayoutEntity> {
        return this.layoutsService.updateOne(id, { ...body, updated_by: auth.id });
    }

    @AuthMutation(() => Boolean, { name: 'layouts_delete' })
    async destroy(@Args('id', { type: () => Int }) id: number, @AuthUser() auth: UserEntity): Promise<boolean> {
        await this.layoutsService.softDelete(id, auth.id);
        return true;
    }
}
