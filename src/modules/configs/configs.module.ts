import { Module } from '@nestjs/common';
import { ConfigsService } from './services/configs.service';
import { ConfigsResolver } from './resolvers/configs.resolver';
import { ConfigEntity } from '../../entities/config.entity';
import { TypeOrmModule } from '@nestjs/typeorm';

@Module({
    providers: [ConfigsService, ConfigsResolver],
    imports: [TypeOrmModule.forFeature([ConfigEntity])],
})
export class ConfigsModule {}
