import { Injectable } from '@nestjs/common';
import { BaseService } from '../../../commons/bases/base.service';
import { ConfigEntity } from '../../../entities/config.entity';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';

@Injectable()
export class ConfigsService extends BaseService<ConfigEntity> {
    constructor(@InjectRepository(ConfigEntity) public readonly repo: Repository<ConfigEntity>) {
        super(repo);
    }
}
