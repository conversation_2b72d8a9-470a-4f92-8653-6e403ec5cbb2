import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { ArticleEntity } from '../../entities/article.entity';
import { ArticleArticleKindEntity } from '../../entities/article-article-kind.entity';
import { ArticleCategoryEntity } from '../../entities/article-category.entity';
import { ArticleTagEntity } from '../../entities/article-tag.entity';
import { ArticleFileEntity } from '../../entities/article-file.entity';
import { ArticleNoteEntity } from '../../entities/article-note.entity';
import { ArticleCommentEntity } from '../../entities/article-comment.entity';
import { ArticleLogEntity } from '../../entities/article-log.entity';
import { RelatedArticleEntity } from '../../entities/related-article.entity';
import { ArticleIssuePageEntity } from '../../entities/article-issue-page.entity';
import { ArticleRoyaltyTypeEntity } from '../../entities/article-royalty-type.entity';
import { ArticleRoyaltyEntity } from '../../entities/article-royalty.entity';
import { ArticleRoyaltyUserEntity } from '../../entities/article-royalty-user.entity';
import { RoyaltyParamEntity } from '../../entities/royalty-param.entity';
import { RoyaltyParamOptionEntity } from '../../entities/royalty-param-option.entity';
import { ArticlesService } from './services/articles.service';
import { ArticleCategoriesService } from './services/article-categories.service';
import { ArticleTagsService } from './services/article-tags.service';
import { ArticleFilesService } from './services/article-files.service';
import { ArticleNotesService } from './services/article-notes.service';
import { ArticleCommentsService } from './services/article-comments.service';
import { ArticleLogsService } from './services/article-logs.service';
import { RelatedArticlesService } from './services/related-articles.service';
import { ArticleIssuePagesService } from './services/article-issue-pages.service';
import { ArticleRoyaltyTypesService } from './services/article-royalty-types.service';
import { ArticleRoyaltiesService } from './services/article-royalties.service';
import { ArticleRoyaltyUsersService } from './services/article-royalty-users.service';
import { RoyaltyParamsService } from './services/royalty-params.service';
import { RoyaltyParamOptionsService } from './services/royalty-param-options.service';
import { ArticlesResolver } from './resolvers/articles.resolver';
import { ArticleCategoriesResolver } from './resolvers/article-categories.resolver';
import { ArticleTagsResolver } from './resolvers/article-tags.resolver';
import { ArticleFilesResolver } from './resolvers/article-files.resolver';
import { ArticleNotesResolver } from './resolvers/article-notes.resolver';
import { ArticleCommentsResolver } from './resolvers/article-comments.resolver';
import { ArticleLogsResolver } from './resolvers/article-logs.resolver';
import { RelatedArticlesResolver } from './resolvers/related-articles.resolver';
import { ArticleIssuePagesResolver } from './resolvers/article-issue-pages.resolver';
import { ArticleRoyaltyTypesResolver } from './resolvers/article-royalty-types.resolver';
import { ArticleRoyaltiesResolver } from './resolvers/article-royalties.resolver';
import { ArticleRoyaltyUsersResolver } from './resolvers/article-royalty-users.resolver';
import { RoyaltyParamsResolver } from './resolvers/royalty-params.resolver';
import { RoyaltyParamOptionsResolver } from './resolvers/royalty-param-options.resolver';

@Module({
    imports: [
        TypeOrmModule.forFeature([
            ArticleEntity,
            ArticleArticleKindEntity,
            ArticleCategoryEntity,
            ArticleTagEntity,
            ArticleFileEntity,
            ArticleNoteEntity,
            ArticleCommentEntity,
            ArticleLogEntity,
            RelatedArticleEntity,
            ArticleIssuePageEntity,
            ArticleRoyaltyTypeEntity,
            ArticleRoyaltyEntity,
            ArticleRoyaltyUserEntity,
            RoyaltyParamEntity,
            RoyaltyParamOptionEntity,
        ]),
    ],
    providers: [
        ArticlesService,
        ArticleCategoriesService,
        ArticleTagsService,
        ArticleFilesService,
        ArticleNotesService,
        ArticleCommentsService,
        ArticleLogsService,
        RelatedArticlesService,
        ArticleIssuePagesService,
        ArticleRoyaltyTypesService,
        ArticleRoyaltiesService,
        ArticleRoyaltyUsersService,
        RoyaltyParamsService,
        RoyaltyParamOptionsService,
        ArticlesResolver,
        ArticleCategoriesResolver,
        ArticleTagsResolver,
        ArticleFilesResolver,
        ArticleNotesResolver,
        ArticleCommentsResolver,
        ArticleLogsResolver,
        RelatedArticlesResolver,
        ArticleIssuePagesResolver,
        ArticleRoyaltyTypesResolver,
        ArticleRoyaltiesResolver,
        ArticleRoyaltyUsersResolver,
        RoyaltyParamsResolver,
        RoyaltyParamOptionsResolver,
    ],
})
export class ArticlesModule {}
