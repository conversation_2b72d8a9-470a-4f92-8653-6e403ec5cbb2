import {
    BadRequestException,
    ForbiddenException,
    Injectable,
    InternalServerErrorException,
    NotFoundException,
} from '@nestjs/common';
import { BaseService } from '../../../commons/bases/base.service';
import { ArticleEntity } from '../../../entities/article.entity';
import { InjectDataSource, InjectRepository } from '@nestjs/typeorm';
import { DataSource, In, IsNull, QueryRunner, Repository } from 'typeorm';
import { ArticleSaveInputDto } from '../dtos/article-save-input.dto';
import { ArticleArticleKindEntity } from '../../../entities/article-article-kind.entity';
import slugify from 'slugify';
import { ArticleCategoriesService } from './article-categories.service';
import { WorkflowEntity } from '../../../entities/workflow.entity';
import { ArticleFileEntity } from '../../../entities/article-file.entity';
import { FileEntity } from '../../../entities/file.entity';
import appConf from '../../../configs/app.conf';
import { PseudonymsEntity } from '../../../entities/pseudonyms.entity';
import { BusinessException } from '../../../commons/exceptions/business.exception';
import { ItemStatus } from '../../../commons/enums.common';
import { RelatedArticleEntity } from '../../../entities/related-article.entity';
import { ArticleTagEntity } from '../../../entities/article-tag.entity';
import { TagEntity } from '../../../entities/tag.entity';
import { ArticleNoteEntity } from '../../../entities/article-note.entity';
import { ArticleCategoryEntity } from '../../../entities/article-category.entity';
import { ArticleIssuePageEntity } from '../../../entities/article-issue-page.entity';
import { ArticleCloneInputDto } from '../dtos/article-clone-input.dto';
import { ArticleRoyaltyEntity } from '../../../entities/article-royalty.entity';
import { ArticleRoyaltyUserEntity } from '../../../entities/article-royalty-user.entity';
import { ArticleRoyaltyCreateInput } from '../dtos/article-save-input.dto';
import axios from 'axios';

@Injectable()
export class ArticlesService extends BaseService<ArticleEntity> {
    constructor(
        private readonly articleCategoriesService: ArticleCategoriesService,
        @InjectRepository(ArticleEntity) public readonly repo: Repository<ArticleEntity>,
        @InjectDataSource() private readonly dataSource: DataSource
    ) {
        super(repo);
    }

    /**
     * Extract file URLs from HTML content based on allowed file types
     */
    private extractFileUrlsFromContent(content: string): string[] {
        if (!content) return [];

        // Get allowed file extensions from config
        const allowedExtensions = appConf.ALLOWED_FILE_TYPES.join('|');
        
        // Enhanced regex to match various file URL patterns
        const fileRegex = new RegExp(
            `<(?:img|a|video|audio|source)[^>]*(?:src|href)\\s*=\\s*["']([^"']+\\.(?:${allowedExtensions}))(?:\\?[^"']*)?["'][^>]*>`,
            'gi'
        );
        
        // Also match direct file URLs in text content
        const directUrlRegex = new RegExp(
            `https?://[^\\s<>"']+\\.(?:${allowedExtensions})(?:\\?[^\\s<>"']*)?`,
            'gi'
        );

        const urls: string[] = [];
        let match: RegExpExecArray | null;

        // Extract from HTML tags
        while ((match = fileRegex.exec(content)) !== null) {
            if (!match[1].startsWith('http')) continue;
            urls.push(match[1]);
        }

        // Extract direct URLs from text
        while ((match = directUrlRegex.exec(content)) !== null) {
            urls.push(match[0]);
        }

        // Remove duplicates and return
        return [...new Set(urls)];
    }

    /**
     * Convert the full URL to a relative path for database comparison
     */
    private urlToRelativePath(url: string): string {
        if (!url) return '';

        try {
            const urlObj = new URL(url);
            // Get the pathname part (e.g., /files/images/2025-06-12/1749749738750-994349352_dh2.jpg)
            let pathname = urlObj.pathname;

            // Remove the leading slash if present
            if (pathname.startsWith('/')) {
                pathname = pathname.substring(1);
            }

            // Convert files/ prefix to uploads/ for database comparison
            if (pathname.startsWith('files/')) {
                return pathname.replace('files/', 'uploads/');
            }

            return pathname;
        } catch {
            // If URL parsing fails, try simple string replacement
            const baseUrl = appConf.API_URL;
            if (url.startsWith(baseUrl)) {
                return url.replace(baseUrl, '').replace('files/', 'uploads/');
            }
            return url;
        }
    }

    /**
     * Handle article files management for all file types
     */
    private async handleArticleFiles(
        articleId: number,
        content: string,
        userId: number,
        queryRunner: QueryRunner,
        isNewArticle: boolean = false
    ): Promise<void> {
        if (!content) return;

        // Extract all file URLs from content (not just images)
        const fileUrls = this.extractFileUrlsFromContent(content);
        if (fileUrls.length === 0) return;

        // Convert URLs to relative paths for database comparison
        const relativePaths = fileUrls.map((url) => this.urlToRelativePath(url));

        // Find files in database that match these paths
        const files = await queryRunner.manager.find(FileEntity, {
            where: {
                file_url: In(relativePaths.filter((path) => path)), // Filter out empty paths
            },
        });

        const fileIds = files.map((file) => file.id);

        if (isNewArticle) {
            // For new articles, add all found files with is_royalty = true
            if (fileIds.length > 0) {
                const articleFiles = fileIds.map((fileId) => ({
                    article_id: articleId,
                    file_id: fileId,
                    is_royalty: true, // Auto set is_royalty = true for new articles
                    created_by: userId,
                    created_at: new Date(),
                }));

                await queryRunner.manager.insert(ArticleFileEntity, articleFiles);
            }
        } else {
            // For existing articles, manage the relationship
            // Get current article files (only non-deleted ones)
            const currentArticleFiles = await queryRunner.manager.find(ArticleFileEntity, {
                where: {
                    article_id: articleId,
                    deleted_at: IsNull(), // Only get non-deleted records
                },
            });

            const currentFileIds = currentArticleFiles.map((af) => af.file_id);

            // Find files to add (in new content but not in current relations)
            const filesToAdd = fileIds.filter((fileId) => !currentFileIds.includes(fileId));

            // Find files to remove (in current relations but not in new content)
            const filesToRemove = currentFileIds.filter((fileId) => !fileIds.includes(fileId));

            // Add new file relations
            if (filesToAdd.length > 0) {
                const newArticleFiles = filesToAdd.map((fileId) => ({
                    article_id: articleId,
                    file_id: fileId,
                    is_royalty: true, // Auto set is_royalty = true for new files added to articles
                    created_by: userId,
                    created_at: new Date(),
                }));

                await queryRunner.manager.insert(ArticleFileEntity, newArticleFiles);
            }

            // Soft delete removed file relations
            if (filesToRemove.length > 0) {
                await queryRunner.manager.update(
                    ArticleFileEntity,
                    {
                        article_id: articleId,
                        file_id: In(filesToRemove),
                    },
                    {
                        deleted_at: new Date(),
                        deleted_by: userId,
                    }
                );
            }
        }
    }

    /**
     * Handle article royalties management
     */
    private async handleArticleRoyalties(
        articleId: number,
        royalties: ArticleRoyaltyCreateInput[],
        userId: number,
        queryRunner: QueryRunner
    ): Promise<void> {
        // Get existing royalty IDs for this article
        const existingRoyalties = await queryRunner.manager.find(ArticleRoyaltyEntity, {
            where: { article_id: articleId },
            select: ['id'],
        });

        // Delete existing royalty users first (if any exist)
        if (existingRoyalties.length > 0) {
            const royaltyIds = existingRoyalties.map((r) => r.id);
            await queryRunner.manager.delete(ArticleRoyaltyUserEntity, {
                article_royalty_id: In(royaltyIds),
            });
        }

        // Delete existing royalties
        await queryRunner.manager.delete(ArticleRoyaltyEntity, {
            article_id: articleId,
        });

        // Process each royalty
        for (const royaltyData of royalties) {
            // Create article royalty
            const royaltyToInsert = {
                article_id: articleId,
                type_id: royaltyData.type_id,
                royalty_type_id: royaltyData.royalty_type_id,
                suggest_royalty: royaltyData.suggest_royalty,
                created_by: userId,
                created_at: new Date(),
            };

            const royaltyInsertResult = await queryRunner.manager.insert(ArticleRoyaltyEntity, royaltyToInsert);
            const royaltyId = royaltyInsertResult.identifiers[0].id;

            // Create article royalty users if provided
            if (royaltyData.article_royalty_users && royaltyData.article_royalty_users.length > 0) {
                const royaltyUsers = royaltyData.article_royalty_users.map((userRoyalty) => ({
                    article_royalty_id: royaltyId,
                    user_id: userRoyalty.user_id,
                    percent: userRoyalty.percent,
                    comment: userRoyalty.comment,
                    param_config: userRoyalty.param_config,
                    final_royalty: userRoyalty.final_royalty ?? 0,
                    suggest_royalty: userRoyalty.suggest_royalty ?? 0,
                    file_id: userRoyalty.file_id,
                    created_by: userId,
                    created_at: new Date(),
                }));

                await queryRunner.manager.insert(ArticleRoyaltyUserEntity, royaltyUsers);
            }
        }
    }

    async saveArticle(
        data: ArticleSaveInputDto,
        userId: number,
        queryRunnerProps?: QueryRunner
    ): Promise<ArticleEntity> {
        const { article_kind_ids, ...articleData } = data;

        // Extract only the fields that are actually saved in the database
        const validFields = this.repo.metadata.columns.map((col) => col.propertyName);
        const filteredArticleData = Object.fromEntries(
            Object.entries(articleData).filter(([key]) => validFields.includes(key))
        );

        const queryRunner = queryRunnerProps || this.dataSource.createQueryRunner();
        const isRootTransaction = !queryRunnerProps;

        // Only start transaction if this is the root call
        if (isRootTransaction) {
            await queryRunner.connect();
            await queryRunner.startTransaction();
        }

        // Validate pseudonym before starting transaction operations
        if (!data.pseudonym_id && !data.pseudonym_name) {
            throw new BadRequestException('Pseudonym id or Pseudonym name is required');
        }

        // Batch validation queries to reduce round trips
        const validationPromises: Promise<any>[] = [];

        if (data.pseudonym_id) {
            validationPromises.push(
                queryRunner.manager.findOne(PseudonymsEntity, {
                    where: { id: data.pseudonym_id },
                })
            );
        }

        // Execute all validation queries in parallel
        const [pseudonymValidation] = await Promise.all(validationPromises);

        if (data.pseudonym_id && !pseudonymValidation) {
            throw new BadRequestException('Pseudonym not found');
        }

        let article: ArticleEntity | undefined;

        try {
            // Optimized pseudonym handling
            if (data.pseudonym_id) {
                filteredArticleData.pseudonym_id = data.pseudonym_id;
            } else if (data.pseudonym_name) {
                // Use upsert pattern to avoid race conditions
                const pseudonym = await queryRunner.manager.findOne(PseudonymsEntity, {
                    where: { name: data.pseudonym_name },
                });

                if (!pseudonym) {
                    const pseudonymInsertResult = await queryRunner.manager
                        .createQueryBuilder()
                        .insert()
                        .into(PseudonymsEntity)
                        .values({
                            name: data.pseudonym_name,
                            status_id: ItemStatus.ACTIVE,
                            is_default: false,
                            user_id: userId,
                        })
                        .orIgnore() // Handle race condition if another process creates the same pseudonym
                        .returning('id')
                        .execute();

                    if (pseudonymInsertResult.identifiers.length > 0) {
                        filteredArticleData.pseudonym_id = pseudonymInsertResult.identifiers[0].id;
                    } else {
                        // If insert was ignored, fetch the existing pseudonym
                        const existingPseudonym = await queryRunner.manager.findOneOrFail(PseudonymsEntity, {
                            where: { name: data.pseudonym_name },
                        });
                        filteredArticleData.pseudonym_id = existingPseudonym.id;
                    }
                } else {
                    filteredArticleData.pseudonym_id = pseudonym.id;
                }
            }
            if (data.id) {
                // Update existing article
                filteredArticleData.updated_by = userId;
                filteredArticleData.updated_at = new Date();

                await queryRunner.manager.update(ArticleEntity, data.id, filteredArticleData);
                article = await queryRunner.manager.findOneOrFail(ArticleEntity, {
                    where: { id: data.id },
                });
            } else {
                // Create new article - avoid unnecessary fetch after insert
                const articleToInsert = {
                    ...filteredArticleData,
                    created_by: userId,
                    created_at: new Date(),
                };

                const insertResult = await queryRunner.manager.insert(ArticleEntity, articleToInsert);
                const articleId = insertResult.identifiers[0].id;

                // Create article object without additional DB query
                article = Object.assign(new ArticleEntity(), {
                    ...articleToInsert,
                    id: articleId,
                });
            }

            // Type assertion to help TypeScript understand article is now defined
            const definedArticle = article as ArticleEntity;

            await axios.post(appConf.ARTICLE_REVALIDATE_URL, {
                id: definedArticle.id,
                key: appConf.ARTICLE_REVALIDATE_TOKEN,
            });

            // Batch delete operations for better performance
            const deletePromises: Promise<any>[] = [];

            if (data.related_article_ids && data.related_article_ids.length > 0) {
                deletePromises.push(
                    queryRunner.manager.delete(RelatedArticleEntity, {
                        article_id: definedArticle.id,
                    })
                );
            }

            if (article_kind_ids && article_kind_ids.length > 0) {
                deletePromises.push(
                    queryRunner.manager.delete(ArticleArticleKindEntity, {
                        article_id: definedArticle.id,
                    })
                );
            }

            // Execute all deletes in parallel
            if (deletePromises.length > 0) {
                await Promise.all(deletePromises);
            }

            // Batch insert operations
            const insertPromises: Promise<any>[] = [];

            if (data.related_article_ids && data.related_article_ids.length > 0) {
                const relatedArticles = data.related_article_ids.map((relatedArticleId) => ({
                    article_id: definedArticle.id,
                    related_article_id: relatedArticleId,
                    created_by: userId,
                    created_at: new Date(),
                }));

                insertPromises.push(queryRunner.manager.insert(RelatedArticleEntity, relatedArticles));
            }

            if (article_kind_ids && article_kind_ids.length > 0) {
                const articleKinds = article_kind_ids.map((kindId) => ({
                    article_id: definedArticle.id,
                    article_kind_id: kindId,
                    created_by: userId,
                    created_at: new Date(),
                }));

                insertPromises.push(queryRunner.manager.insert(ArticleArticleKindEntity, articleKinds));
            }

            // Optimized tags handling
            if (data.article_tags && data.article_tags.length > 0) {
                // Add tag deletion to the batch delete operations
                insertPromises.push(
                    queryRunner.manager.delete(ArticleTagEntity, {
                        article_id: definedArticle.id,
                    })
                );

                // Find existing tags in one query
                const existingTags = await queryRunner.manager.find(TagEntity, {
                    where: {
                        name: In(data.article_tags),
                        department_id: data.department_id,
                    },
                });

                const existingTagNames = new Set(existingTags.map((tag) => tag.name));
                const tagsToCreate = data.article_tags.filter((tagName) => !existingTagNames.has(tagName));

                // Bulk create new tags if needed
                let newTags: TagEntity[] = [];
                if (tagsToCreate.length > 0) {
                    const tagEntities = tagsToCreate.map((tag) => ({
                        name: tag,
                        department_id: data.department_id,
                        slug: slugify(tag, { lower: true }),
                        status_id: ItemStatus.ACTIVE,
                        created_by: userId,
                        created_at: new Date(),
                    }));

                    const tagInsertResult = await queryRunner.manager.insert(TagEntity, tagEntities);

                    // Create tag objects from insert result
                    newTags = tagInsertResult.identifiers.map(
                        (identifier, index) =>
                            ({
                                id: identifier.id,
                                ...tagEntities[index],
                            }) as TagEntity
                    );
                }

                // Combine all tags and create article-tag relationships
                const allTags = [...existingTags, ...newTags];
                const articleTags = allTags.map((tag) => ({
                    article_id: definedArticle.id,
                    tag_id: tag.id,
                    created_by: userId,
                    created_at: new Date(),
                }));

                insertPromises.push(queryRunner.manager.insert(ArticleTagEntity, articleTags));
            }

            // Add notes to batch insert operations
            if (data.new_article_notes && data.new_article_notes.length > 0) {
                const newNotes = data.new_article_notes.map((note) => ({
                    article_id: definedArticle.id,
                    content: note,
                    created_by: userId,
                    created_at: new Date(),
                }));

                insertPromises.push(queryRunner.manager.insert(ArticleNoteEntity, newNotes));
            }

            // Execute all insert operations in parallel
            if (insertPromises.length > 0) {
                await Promise.all(insertPromises);
            }

            // Optimized categories handling
            if (data.article_categories && data.article_categories.length > 0) {
                const isSpecialWorkflow = await queryRunner.manager.existsBy(WorkflowEntity, {
                    id: definedArticle.workflow_id,
                    workflow_type_id: 3 /*hardcode*/,
                });

                if (isSpecialWorkflow) {
                    // For special workflow, use the existing setDisplayOrder logic
                    // but optimize by deduplicating and sorting to avoid deadlocks
                    const seen = new Set<number>();
                    const uniqueByCategory = data.article_categories
                        .filter((item) => {
                            if (seen.has(item.category_id)) return false;
                            seen.add(item.category_id);
                            return true;
                        })
                        .sort((a, b) => a.category_id - b.category_id);

                    // Process categories sequentially to avoid deadlocks
                    for (const articleCategory of uniqueByCategory) {
                        await this.articleCategoriesService.setDisplayOrder(
                            {
                                article_id: definedArticle.id,
                                category_id: articleCategory.category_id,
                                display_order: articleCategory.display_order,
                                is_major: articleCategory.is_major,
                            },
                            userId,
                            queryRunner.manager
                        );
                    }
                } else {
                    // For regular workflow, use bulk operations
                    await queryRunner.manager.delete(ArticleCategoryEntity, {
                        article_id: definedArticle.id,
                    });

                    const newCategories = data.article_categories.map((category) => ({
                        article_id: definedArticle.id,
                        category_id: category.category_id,
                        display_order: category.display_order,
                        is_major: category.is_major,
                        created_by: userId,
                        created_at: new Date(),
                    }));

                    await queryRunner.manager.insert(ArticleCategoryEntity, newCategories);
                }
            }

            // Parallel execution of independent operations
            const parallelOperations: Promise<any>[] = [];

            // Handle article files from content
            parallelOperations.push(
                this.handleArticleFiles(
                    definedArticle.id,
                    data.content,
                    userId,
                    queryRunner,
                    !data.id // isNewArticle = true if no id provided
                )
            );

            // Handle article issue pages
            if (data.article_issue_pages && data.article_issue_pages.length > 0) {
                const issuePageData = data.article_issue_pages; // Capture in local variable for type safety

                const issuePageOperation = async () => {
                    await queryRunner.manager.delete(ArticleIssuePageEntity, {
                        article_id: definedArticle.id,
                    });

                    const articleIssuePages = issuePageData.map((articleIssuePage) => ({
                        article_id: definedArticle.id,
                        issue_page_id: articleIssuePage.issue_page_id,
                        display_order: articleIssuePage.display_order,
                        comment: articleIssuePage.comment,
                        position: articleIssuePage.position,
                        created_by: userId,
                        created_at: new Date(),
                    }));

                    await queryRunner.manager.insert(ArticleIssuePageEntity, articleIssuePages);
                };

                parallelOperations.push(issuePageOperation());
            }

            // Execute parallel operations
            if (parallelOperations.length > 0) {
                await Promise.all(parallelOperations);
            }

            // Handle article royalties
            if (data.article_royalties && data.article_royalties.length > 0) {
                await this.handleArticleRoyalties(definedArticle.id, data.article_royalties, userId, queryRunner);
            }

            // Handle article type copies - keep sequential to avoid conflicts
            if (data.article_type_copy_ids && data.article_type_copy_ids.length > 0) {
                const { article_type_copy_ids, is_sync, article_categories, ...baseData } = data;

                // Create copy articles sequentially to avoid conflicts
                for (const articleTypeId of article_type_copy_ids) {
                    await this.saveArticle(
                        {
                            ...baseData,
                            article_type_id: articleTypeId,
                            article_type_copy_ids: undefined,
                            root_article_id: definedArticle.id,
                            article_categories: [],
                        },
                        userId,
                        queryRunner
                    );
                }
            }

            // Final update for is_unclassified flag
            const isUnclassified = !!data.is_unclassified && (data.article_type_copy_ids ?? []).length === 0;
            if (definedArticle.is_unclassified !== isUnclassified) {
                await queryRunner.manager.update(ArticleEntity, definedArticle.id, {
                    is_unclassified: isUnclassified,
                });
                definedArticle.is_unclassified = isUnclassified;
            }

            // Commit transaction only if this is the root call
            if (isRootTransaction) {
                await queryRunner.commitTransaction();
            }

            return definedArticle;
        } catch (error) {
            // Rollback transaction only if this is the root call
            if (isRootTransaction) {
                await queryRunner.rollbackTransaction();
            }

            // Re-throw business logic exceptions as-is
            if (
                error instanceof BadRequestException ||
                error instanceof NotFoundException ||
                error instanceof ForbiddenException ||
                error instanceof BusinessException
            ) {
                throw error;
            }

            // Wrap other errors as internal server errors with more context
            throw new InternalServerErrorException(
                `An error occurred while saving the article${data.id ? ` (ID: ${data.id})` : ' (new article)'}`,
                { cause: error }
            );
        } finally {
            // Release connection only if this is the root call
            if (isRootTransaction) {
                await queryRunner.release();
            }
        }
    }

    async changeLock(id: number, userId: number, lock: boolean) {
        const article = await this.findOne(id);
        if (!article) {
            throw new NotFoundException();
        }
        if (!article.lock_user_id && !lock) {
            throw new ForbiddenException();
        }
        await this.repo.update(
            { id },
            {
                lock_user_id: lock ? userId : null,
                lock_at: lock ? new Date() : null,
            }
        );
    }

    async cloneArticle(id: number, body: ArticleCloneInputDto, userId: number) {
        const article = await this.findOne({ where: { id }, relations: ['childrenArticles'] });
        if (!article) {
            throw new NotFoundException();
        }
        if (article.article_type_id === body.article_type_id) {
            throw new BadRequestException('Cannot clone to the same article type');
        }
        if (article.childrenArticles?.some((child) => child.article_type_id === body.article_type_id)) {
            throw new BadRequestException('Cannot clone when children articles are cloned to the same article type');
        }
        return this.create({
            ...article,
            ...body,
            id: undefined,
            created_by: userId,
            updated_by: undefined,
            updated_at: undefined,
        });
    }
}
