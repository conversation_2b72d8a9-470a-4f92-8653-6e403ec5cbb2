import { Injectable } from '@nestjs/common';
import { BaseService } from '../../../commons/bases/base.service';
import { ArticleFileEntity } from '../../../entities/article-file.entity';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';

@Injectable()
export class ArticleFilesService extends BaseService<ArticleFileEntity> {
    constructor(@InjectRepository(ArticleFileEntity) public readonly repo: Repository<ArticleFileEntity>) {
        super(repo);
    }
}
