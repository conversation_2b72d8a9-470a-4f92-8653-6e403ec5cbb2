import { Injectable, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { EntityManager, In, IsNull, Repository } from 'typeorm';
import { BaseService } from '../../../commons/bases/base.service';
import { ArticleRoyaltyEntity } from '../../../entities/article-royalty.entity';
import { ArticleRoyaltyUserEntity } from '../../../entities/article-royalty-user.entity';
import { ArticleRoyaltiesBatchSaveInputDto, ArticleRoyaltyUserBatchInput } from '../dtos/article-royalty-batch-save-input.dto';

@Injectable()
export class ArticleRoyaltiesService extends BaseService<ArticleRoyaltyEntity> {
    constructor(@InjectRepository(ArticleRoyaltyEntity) public readonly repo: Repository<ArticleRoyaltyEntity>) {
        super(repo);
    }

    /**
     * Batch save article royalties with users
     */
    async saveBatch(
        data: ArticleRoyaltiesBatchSaveInputDto,
        userId: number
    ): Promise<ArticleRoyaltyEntity[]> {
        return this.repo.manager.transaction(async (manager) => {
            const results: ArticleRoyaltyEntity[] = [];

            for (const royaltyData of data.royalties) {
                let royalty: ArticleRoyaltyEntity;

                if (royaltyData.id) {
                    // Update existing royalty
                    const existingRoyalty = await manager.findOne(ArticleRoyaltyEntity, {
                        where: { id: royaltyData.id },
                    });

                    if (!existingRoyalty) {
                        throw new NotFoundException(`Article royalty with ID ${royaltyData.id} not found`);
                    }

                    await manager.update(ArticleRoyaltyEntity, royaltyData.id, {
                        article_id: royaltyData.article_id ?? data.article_id,
                        type_id: royaltyData.type_id,
                        royalty_type_id: royaltyData.royalty_type_id,
                        suggest_royalty: royaltyData.suggest_royalty,
                        title: royaltyData.title,
                        article_statistic: royaltyData.article_statistic,
                        updated_by: userId,
                        updated_at: new Date(),
                    });

                    const updatedRoyalty = await manager.findOne(ArticleRoyaltyEntity, {
                        where: { id: royaltyData.id },
                    });

                    if (!updatedRoyalty) {
                        throw new NotFoundException(`Failed to retrieve updated article royalty with ID ${royaltyData.id}`);
                    }

                    royalty = updatedRoyalty;
                } else {
                    // Create new royalty
                    const newRoyalty = manager.create(ArticleRoyaltyEntity, {
                        article_id: royaltyData.article_id ?? data.article_id,
                        type_id: royaltyData.type_id,
                        royalty_type_id: royaltyData.royalty_type_id,
                        suggest_royalty: royaltyData.suggest_royalty,
                        title: royaltyData.title,
                        article_statistic: royaltyData.article_statistic,
                        created_by: userId,
                        created_at: new Date(),
                    });

                    royalty = await manager.save(newRoyalty);
                }

                // Handle royalty users
                if (royaltyData.article_royalty_users) {
                    await this.handleRoyaltyUsers(
                        manager,
                        royalty.id,
                        royaltyData.article_royalty_users,
                        userId
                    );
                }

                results.push(royalty);
            }

            return results;
        });
    }

    /**
     * Handle royalty users for a specific royalty
     */
    private async handleRoyaltyUsers(
        manager: EntityManager,
        royaltyId: number,
        usersData: ArticleRoyaltyUserBatchInput[],
        userId: number
    ): Promise<void> {
        // Get existing royalty users
        const existingUsers = await manager.find(ArticleRoyaltyUserEntity, {
            where: { article_royalty_id: royaltyId, deleted_at: IsNull() },
        });
        
        const inputUserIds = usersData.filter(u => u.id).map(u => u.id!);

        // Find users to soft delete (exist in DB but not in input)
        const usersToDelete = existingUsers.filter(u => !inputUserIds.includes(u.id));

        // Soft delete removed users
        if (usersToDelete.length > 0) {
            await manager.update(
                ArticleRoyaltyUserEntity,
                { id: In(usersToDelete.map(u => u.id)) },
                {
                    deleted_at: new Date(),
                    deleted_by: userId,
                }
            );
        }

        // Process each user in input
        for (const userData of usersData) {
            if (userData.id) {
                // Update existing user
                const existingUser = existingUsers.find(u => u.id === userData.id);
                if (existingUser) {
                    await manager.update(ArticleRoyaltyUserEntity, userData.id, {
                        user_id: userData.user_id,
                        percent: userData.percent,
                        comment: userData.comment,
                        param_config: userData.param_config,
                        final_royalty: userData.final_royalty,
                        suggest_royalty: userData.suggest_royalty,
                        updated_by: userId,
                        updated_at: new Date(),
                    });
                }
            } else {
                // Create new user
                const newUser = manager.create(ArticleRoyaltyUserEntity, {
                    article_royalty_id: royaltyId,
                    user_id: userData.user_id,
                    percent: userData.percent,
                    comment: userData.comment,
                    param_config: userData.param_config,
                    final_royalty: userData.final_royalty ?? 0,
                    suggest_royalty: userData.suggest_royalty ?? 0,
                    created_by: userId,
                    created_at: new Date(),
                });

                await manager.save(newUser);
            }
        }
    }
}
