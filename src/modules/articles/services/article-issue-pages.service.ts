import { BadRequestException, Injectable, NotFoundException } from '@nestjs/common';
import { BaseService } from '../../../commons/bases/base.service';
import { ArticleIssuePageEntity } from '../../../entities/article-issue-page.entity';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { ArticleIssuePageCreateBatchInputDto } from '../dtos/article-issue-page-create-batch-input.dto';
import { ArticleIssuePageUpdatePageInputDto } from '../dtos/article-issue-page-update-page-input.dto';

@Injectable()
export class ArticleIssuePagesService extends BaseService<ArticleIssuePageEntity> {
    constructor(@InjectRepository(ArticleIssuePageEntity) public readonly repo: Repository<ArticleIssuePageEntity>) {
        super(repo);
    }

    async createBatch(body: ArticleIssuePageCreateBatchInputDto, userId: number): Promise<ArticleIssuePageEntity[]> {
        let maxDisplayOrder =
            (
                await this.findOne({
                    where: { issue_page_id: body.issue_page_id },
                    order: { display_order: 'DESC' },
                    select: ['display_order'],
                })
            )?.display_order || 0;
        const articleIssuePages = body.article_ids.map((articleId) => {
            maxDisplayOrder++;
            return this.repo.create({
                article_id: articleId,
                issue_page_id: body.issue_page_id,
                display_order: maxDisplayOrder,
                created_by: userId,
            });
        });
        return this.repo.manager.transaction(async (manager) => {
            return manager.save(articleIssuePages);
        });
    }

    async updatePage(
        id: number,
        body: ArticleIssuePageUpdatePageInputDto,
        userId: number
    ): Promise<ArticleIssuePageEntity> {
        const oldAip = await this.findOne(id);
        if (!oldAip) throw new NotFoundException();
        if (oldAip.issue_page_id === body.issue_page_id) throw new BadRequestException('Same issue page');
        const maxDisplayOrder =
            (
                await this.findOne({
                    where: { article_id: body.issue_page_id },
                    order: { display_order: 'DESC' },
                    select: ['display_order'],
                })
            )?.display_order || 0;
        oldAip.issue_page_id = body.issue_page_id;
        oldAip.display_order = maxDisplayOrder + 1;
        oldAip.updated_by = userId;
        return this.save(oldAip);
    }
}
