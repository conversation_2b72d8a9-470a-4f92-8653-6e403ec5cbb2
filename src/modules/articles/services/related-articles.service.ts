import { Injectable } from '@nestjs/common';
import { BaseService } from '../../../commons/bases/base.service';
import { RelatedArticleEntity } from '../../../entities/related-article.entity';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';

@Injectable()
export class RelatedArticlesService extends BaseService<RelatedArticleEntity> {
    constructor(@InjectRepository(RelatedArticleEntity) public readonly repo: Repository<RelatedArticleEntity>) {
        super(repo);
    }
}
