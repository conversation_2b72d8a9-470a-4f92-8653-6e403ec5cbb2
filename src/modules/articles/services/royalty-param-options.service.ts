import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { BaseService } from '../../../commons/bases/base.service';
import { RoyaltyParamOptionEntity } from '../../../entities/royalty-param-option.entity';

@Injectable()
export class RoyaltyParamOptionsService extends BaseService<RoyaltyParamOptionEntity> {
    constructor(
        @InjectRepository(RoyaltyParamOptionEntity)
        public readonly repo: Repository<RoyaltyParamOptionEntity>
    ) {
        super(repo);
    }
}
