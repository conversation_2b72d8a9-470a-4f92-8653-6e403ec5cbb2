import { Injectable } from '@nestjs/common';
import { BaseService } from '../../../commons/bases/base.service';
import { ArticleRoyaltyTypeEntity } from '../../../entities/article-royalty-type.entity';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';

@Injectable()
export class ArticleRoyaltyTypesService extends BaseService<ArticleRoyaltyTypeEntity> {
    constructor(
        @InjectRepository(ArticleRoyaltyTypeEntity) public readonly repo: Repository<ArticleRoyaltyTypeEntity>
    ) {
        super(repo);
    }
}
