import { Injectable, NotFoundException } from '@nestjs/common';
import { BaseService } from '../../../commons/bases/base.service';
import { ArticleCategoryEntity } from '../../../entities/article-category.entity';
import { InjectRepository } from '@nestjs/typeorm';
import { EntityManager, Repository } from 'typeorm';
import { ArticleCategorySaveInputDto } from '../dtos/article-category-save-input.dto';

@Injectable()
export class ArticleCategoriesService extends BaseService<ArticleCategoryEntity> {
    constructor(@InjectRepository(ArticleCategoryEntity) public readonly repo: Repository<ArticleCategoryEntity>) {
        super(repo);
    }

    private async setDisplayOrderManager(manager: EntityManager, body: ArticleCategorySaveInputDto, userId: number) {
        // Step 1: Lấy tất cả display_order trong category
        const existingOrders = await manager
            .createQueryBuilder(ArticleCategoryEntity, 'ac')
            .setLock('pessimistic_write')
            .select('ac.display_order', 'display_order')
            .where('ac.category_id = :category_id', { category_id: body.category_id })
            .orderBy('ac.display_order', 'ASC')
            .getMany();

        // Step 2: Tìm `nextGap`
        const displayOrders = existingOrders.map((e) => e.display_order).sort((a, b) => a - b);

        let nextGap = body.display_order + 1;

        for (let i = 0; i < displayOrders.length; i++) {
            const curr = displayOrders[i];
            const next = displayOrders[i + 1];

            if (curr === body.display_order || curr > body.display_order) {
                if (next && next - curr === 1) {
                    nextGap = next + 1; // mở rộng vùng liên tiếp
                } else {
                    break; // gặp khoảng cách -> dừng
                }
            }
        }

        // Step 3: Update các row trong khoảng [body.display_order, nextGap)
        await manager
            .createQueryBuilder()
            .update(ArticleCategoryEntity)
            .set({ display_order: () => 'display_order + 1' })
            .where('category_id = :category_id', { category_id: body.category_id })
            .andWhere('display_order >= :display_order', { display_order: body.display_order })
            .andWhere('display_order < :nextGap', { nextGap })
            .execute();

        // Step 4: Update or insert row hiện tại
        let ac = await manager.findOne(ArticleCategoryEntity, {
            where: { article_id: body.article_id, category_id: body.category_id },
        });
        if (ac?.display_order === body.display_order) return ac;
        if (!ac) {
            ac = manager.create(ArticleCategoryEntity, {
                article_id: body.article_id,
                category_id: body.category_id,
                display_order: body.display_order,
                created_by: userId,
            });
        } else {
            ac.display_order = body.display_order;
            ac.updated_by = userId;
        }
        return await manager.save(ac);
    }

    async setDisplayOrder(
        body: ArticleCategorySaveInputDto,
        userId: number,
        entityManager?: EntityManager
    ): Promise<ArticleCategoryEntity> {
        if (entityManager) {
            return this.setDisplayOrderManager(entityManager, body, userId);
        } else {
            return this.repo.manager.transaction((manager) => this.setDisplayOrderManager(manager, body, userId));
        }
    }

    async saveArticleCategories(
        body: ArticleCategorySaveInputDto,
        userId: number,
        id?: number
    ): Promise<ArticleCategoryEntity> {
        return this.repo.manager.transaction(async (manager) => {
            let articleCategory: ArticleCategoryEntity | null;
            if (body.is_major)
                await manager.update(ArticleCategoryEntity, { article_id: body.article_id }, { is_major: false });
            if (id) {
                articleCategory = await manager.findOneBy(ArticleCategoryEntity, { id });
                if (!articleCategory) throw new NotFoundException();
                articleCategory.updated_by = userId;
            } else {
                articleCategory = manager.create(ArticleCategoryEntity, { body, created_by: userId });
            }
            articleCategory = await manager.save(articleCategory);
            return articleCategory;
        });
    }
}
