import { Injectable } from '@nestjs/common';
import { BaseService } from '../../../commons/bases/base.service';
import { ArticleNoteEntity } from '../../../entities/article-note.entity';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';

@Injectable()
export class ArticleNotesService extends BaseService<ArticleNoteEntity> {
    constructor(@InjectRepository(ArticleNoteEntity) public readonly repo: Repository<ArticleNoteEntity>) {
        super(repo);
    }
}
