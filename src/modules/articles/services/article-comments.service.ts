import { Injectable } from '@nestjs/common';
import { BaseService } from '../../../commons/bases/base.service';
import { ArticleCommentEntity } from '../../../entities/article-comment.entity';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';

@Injectable()
export class ArticleCommentsService extends BaseService<ArticleCommentEntity> {
    constructor(@InjectRepository(ArticleCommentEntity) public readonly repo: Repository<ArticleCommentEntity>) {
        super(repo);
    }
}
