import { Injectable } from '@nestjs/common';
import { BaseService } from '../../../commons/bases/base.service';
import { ArticleLogEntity } from '../../../entities/article-log.entity';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';

@Injectable()
export class ArticleLogsService extends BaseService<ArticleLogEntity> {
    constructor(@InjectRepository(ArticleLogEntity) public readonly repo: Repository<ArticleLogEntity>) {
        super(repo);
    }
}
