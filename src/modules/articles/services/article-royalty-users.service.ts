import { Injectable } from '@nestjs/common';
import { BaseService } from '../../../commons/bases/base.service';
import { ArticleRoyaltyUserEntity } from '../../../entities/article-royalty-user.entity';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';

@Injectable()
export class ArticleRoyaltyUsersService extends BaseService<ArticleRoyaltyUserEntity> {
    constructor(
        @InjectRepository(ArticleRoyaltyUserEntity) public readonly repo: Repository<ArticleRoyaltyUserEntity>
    ) {
        super(repo);
    }
}
