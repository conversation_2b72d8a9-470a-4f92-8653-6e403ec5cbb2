import { Injectable } from '@nestjs/common';
import { BaseService } from '../../../commons/bases/base.service';
import { RoyaltyParamEntity } from '../../../entities/royalty-param.entity';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';

@Injectable()
export class RoyaltyParamsService extends BaseService<RoyaltyParamEntity> {
    constructor(@InjectRepository(RoyaltyParamEntity) public readonly repo: Repository<RoyaltyParamEntity>) {
        super(repo);
    }
}
