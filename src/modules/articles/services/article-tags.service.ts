import { Injectable } from '@nestjs/common';
import { BaseService } from '../../../commons/bases/base.service';
import { ArticleTagEntity } from '../../../entities/article-tag.entity';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';

@Injectable()
export class ArticleTagsService extends BaseService<ArticleTagEntity> {
    constructor(@InjectRepository(ArticleTagEntity) public readonly repo: Repository<ArticleTagEntity>) {
        super(repo);
    }
}
