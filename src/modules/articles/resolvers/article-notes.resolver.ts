import { Args, Int, Mutation, Parent, Query, ResolveField } from '@nestjs/graphql';
import { AuthResolver } from '../../../commons/decorators/graphql.decorators';
import { ArticleNoteEntity } from '../../../entities/article-note.entity';
import { ArticleNotesService } from '../services/article-notes.service';
import { DataLoaderService } from '../../../data-loaders/data-loaders.service';
import { BasePaginationInput } from '../../../commons/bases/base.input';
import { ArticleNotesModel } from '../models/article-notes.model';
import { IPaginatedType } from '../../../commons/bases/base.model';
import { NotFoundException } from '@nestjs/common';
import { AuthUser } from '../../auth/auth.decorator';
import { UserEntity } from '../../../entities/user.entity';
import { ArticleNoteSaveInputDto } from '../dtos/article-note-save-input.dto';
import { ArticleEntity } from '../../../entities/article.entity';

@AuthResolver(ArticleNoteEntity)
export class ArticleNotesResolver {
    constructor(
        private readonly articleNotesService: ArticleNotesService,
        private readonly dataLoader: DataLoaderService
    ) {}

    @ResolveField(() => ArticleEntity, { nullable: true })
    async article(@Parent() parent: ArticleNoteEntity): Promise<ArticleEntity | null> {
        if (!parent.article_id) return null;
        return this.dataLoader.relationBatchOne(ArticleEntity).load(parent.article_id);
    }

    @ResolveField(() => UserEntity, { nullable: true })
    async createdByUser(@Parent() parent: ArticleNoteEntity): Promise<UserEntity | null> {
        if (!parent.created_by) return null;
        return this.dataLoader.relationBatchOne(UserEntity).load(parent.created_by);
    }

    @ResolveField(() => UserEntity, { nullable: true })
    async updatedByUser(@Parent() parent: ArticleNoteEntity): Promise<UserEntity | null> {
        if (!parent.updated_by) return null;
        return this.dataLoader.relationBatchOne(UserEntity).load(parent.updated_by);
    }

    @Query(() => ArticleNotesModel, { name: 'article_notes_list' })
    async index(@Args('body') body: BasePaginationInput): Promise<IPaginatedType<ArticleNoteEntity>> {
        return this.articleNotesService.search(body);
    }

    @Query(() => ArticleNoteEntity, { name: 'article_notes_detail' })
    async view(@Args('id', { type: () => Int }) id: number): Promise<ArticleNoteEntity> {
        return this.articleNotesService.findOne(id).then((res) => {
            if (!res) throw new NotFoundException();
            return res;
        });
    }

    @Mutation(() => ArticleNoteEntity, { name: 'article_notes_create' })
    async store(@Args('body') body: ArticleNoteSaveInputDto, @AuthUser() auth: UserEntity): Promise<ArticleNoteEntity> {
        return this.articleNotesService.create({ ...body, created_by: auth.id });
    }

    @Mutation(() => ArticleNoteEntity, { name: 'article_notes_update' })
    async update(
        @Args('id', { type: () => Int }) id: number,
        @Args('body') body: ArticleNoteSaveInputDto,
        @AuthUser() auth: UserEntity
    ): Promise<ArticleNoteEntity> {
        return this.articleNotesService.updateOne(id, { ...body, updated_by: auth.id });
    }

    @Mutation(() => Boolean, { name: 'article_notes_delete' })
    async destroy(@Args('id', { type: () => Int }) id: number, @AuthUser() auth: UserEntity): Promise<boolean> {
        await this.articleNotesService.softDelete(id, auth.id);
        return true;
    }
}
