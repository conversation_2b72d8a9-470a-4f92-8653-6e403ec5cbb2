import { Args, Int, Mutation, Parent, Query, ResolveField } from '@nestjs/graphql';
import { AuthResolver } from '../../../commons/decorators/graphql.decorators';
import { RelatedArticleEntity } from '../../../entities/related-article.entity';
import { RelatedArticlesService } from '../services/related-articles.service';
import { DataLoaderService } from '../../../data-loaders/data-loaders.service';
import { BasePaginationInput } from '../../../commons/bases/base.input';
import { RelatedArticlesModel } from '../models/related-articles.model';
import { IPaginatedType } from '../../../commons/bases/base.model';
import { NotFoundException } from '@nestjs/common';
import { AuthUser } from '../../auth/auth.decorator';
import { UserEntity } from '../../../entities/user.entity';
import { RelatedArticleSaveInputDto } from '../dtos/related-article-save-input.dto';
import { ArticleEntity } from '../../../entities/article.entity';

@AuthResolver(RelatedArticleEntity)
export class RelatedArticlesResolver {
    constructor(
        private readonly relatedArticlesService: RelatedArticlesService,
        private readonly dataLoader: DataLoaderService
    ) {}

    @ResolveField(() => ArticleEntity, { nullable: true })
    async article(@Parent() parent: RelatedArticleEntity): Promise<ArticleEntity | null> {
        if (!parent.article_id) return null;
        return this.dataLoader.relationBatchOne(ArticleEntity).load(parent.article_id);
    }

    @ResolveField(() => ArticleEntity, { nullable: true })
    async relatedArticle(@Parent() parent: RelatedArticleEntity): Promise<ArticleEntity | null> {
        if (!parent.related_article_id) return null;
        return this.dataLoader.relationBatchOne(ArticleEntity).load(parent.related_article_id);
    }

    @Query(() => RelatedArticlesModel, { name: 'related_articles_list' })
    async index(@Args('body') body: BasePaginationInput): Promise<IPaginatedType<RelatedArticleEntity>> {
        return this.relatedArticlesService.search(body);
    }

    @Query(() => RelatedArticleEntity, { name: 'related_articles_detail' })
    async view(@Args('id', { type: () => Int }) id: number): Promise<RelatedArticleEntity> {
        return this.relatedArticlesService.findOne(id).then((res) => {
            if (!res) throw new NotFoundException();
            return res;
        });
    }

    @Mutation(() => RelatedArticleEntity, { name: 'related_articles_create' })
    async store(
        @Args('body') body: RelatedArticleSaveInputDto,
        @AuthUser() auth: UserEntity
    ): Promise<RelatedArticleEntity> {
        return this.relatedArticlesService.create({ ...body, created_by: auth.id });
    }

    @Mutation(() => RelatedArticleEntity, { name: 'related_articles_update' })
    async update(
        @Args('id', { type: () => Int }) id: number,
        @Args('body') body: RelatedArticleSaveInputDto,
        @AuthUser() auth: UserEntity
    ): Promise<RelatedArticleEntity> {
        return this.relatedArticlesService.updateOne(id, { ...body, updated_by: auth.id });
    }

    @Mutation(() => Boolean, { name: 'related_articles_delete' })
    async destroy(@Args('id', { type: () => Int }) id: number, @AuthUser() auth: UserEntity): Promise<boolean> {
        await this.relatedArticlesService.softDelete(id, auth.id);
        return true;
    }
}
