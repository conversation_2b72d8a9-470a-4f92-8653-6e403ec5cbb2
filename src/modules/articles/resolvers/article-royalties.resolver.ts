import { Args, Int, Mutation, Parent, Query, ResolveField, Resolver } from '@nestjs/graphql';
import { UserEntity } from '../../../entities/user.entity';
import { ArticleRoyaltyEntity } from '../../../entities/article-royalty.entity';
import { ArticleRoyaltiesService } from '../services/article-royalties.service';
import { ArticleRoyaltiesBatchSaveInputDto } from '../dtos/article-royalty-batch-save-input.dto';
import { ArticleEntity } from '../../../entities/article.entity';
import { NotFoundException } from '@nestjs/common';
import { BasePaginationInput } from '../../../commons/bases/base.input';
import { ArticleRoyaltiesModel } from '../models/article-royalties.model';
import { IPaginatedType } from '../../../commons/bases/base.model';
import { ArticleRoyaltySaveInputDto } from '../dtos/article-royalty-save-input.dto';
import { ArticleRoyaltyTypeEntity } from '../../../entities/article-royalty-type.entity';
import { ArticleRoyaltyUserEntity } from '../../../entities/article-royalty-user.entity';
import { DataLoaderService } from '../../../data-loaders/data-loaders.service';
import { AuthUser } from '../../auth/auth.decorator';

@Resolver(ArticleRoyaltyEntity)
export class ArticleRoyaltiesResolver {
    constructor(
        private readonly articleRoyaltiesService: ArticleRoyaltiesService,
        private readonly dataLoader: DataLoaderService
    ) {}

    @ResolveField(() => UserEntity, { nullable: true })
    async createdByUser(@Parent() parent: ArticleRoyaltyEntity): Promise<UserEntity | null> {
        if (!parent.created_by) return null;
        return this.dataLoader.relationBatchOne(UserEntity).load(parent.created_by);
    }

    @ResolveField(() => UserEntity, { nullable: true })
    async updatedByUser(@Parent() parent: ArticleRoyaltyEntity): Promise<UserEntity | null> {
        if (!parent.updated_by) return null;
        return this.dataLoader.relationBatchOne(UserEntity).load(parent.updated_by);
    }

    @ResolveField(() => ArticleEntity, { nullable: true })
    async article(@Parent() parent: ArticleRoyaltyEntity): Promise<ArticleEntity | null> {
        if (!parent.article_id) return null;
        const result = await this.dataLoader.relationBatchOne(ArticleEntity).load(parent.article_id);
        if (!result) throw new NotFoundException('Article not found');
        return result;
    }

    @ResolveField(() => ArticleRoyaltyTypeEntity, { nullable: true })
    async royaltyType(@Parent() parent: ArticleRoyaltyEntity): Promise<ArticleRoyaltyTypeEntity | null> {
        if (!parent.royalty_type_id) return null;
        return this.dataLoader.relationBatchOne(ArticleRoyaltyTypeEntity).load(parent.royalty_type_id);
    }

    @ResolveField(() => [ArticleRoyaltyUserEntity], { nullable: true })
    async articleRoyaltyUsers(@Parent() parent: ArticleRoyaltyEntity): Promise<ArticleRoyaltyUserEntity[]> {
        return this.dataLoader.relationBatchOneMany(ArticleRoyaltyUserEntity, 'articleRoyalty').load(parent.id);
    }

    @Query(() => ArticleRoyaltiesModel, { name: 'article_royalties_list' })
    async index(@Args('body') body: BasePaginationInput): Promise<IPaginatedType<ArticleRoyaltyEntity>> {
        return this.articleRoyaltiesService.search(body);
    }

    @Query(() => ArticleRoyaltyEntity, { name: 'article_royalties_detail' })
    async view(@Args('id', { type: () => Int }) id: number): Promise<ArticleRoyaltyEntity> {
        return this.articleRoyaltiesService.findOne(id).then((res) => {
            if (!res) throw new NotFoundException();
            return res;
        });
    }

    @Mutation(() => ArticleRoyaltyEntity, { name: 'article_royalties_create' })
    async store(
        @Args('body') body: ArticleRoyaltySaveInputDto,
        @AuthUser() auth: UserEntity
    ): Promise<ArticleRoyaltyEntity> {
        return this.articleRoyaltiesService.create({ ...body, created_by: auth.id });
    }

    @Mutation(() => ArticleRoyaltyEntity, { name: 'article_royalties_update' })
    async update(
        @Args('id', { type: () => Int }) id: number,
        @Args('body') body: ArticleRoyaltySaveInputDto,
        @AuthUser() auth: UserEntity
    ): Promise<ArticleRoyaltyEntity> {
        return this.articleRoyaltiesService.updateOne(id, { ...body, updated_by: auth.id });
    }

    @Mutation(() => Boolean, { name: 'article_royalties_delete' })
    async destroy(@Args('id', { type: () => Int }) id: number, @AuthUser() auth: UserEntity): Promise<boolean> {
        await this.articleRoyaltiesService.softDelete(id, auth.id);
        return true;
    }

    @Mutation(() => [ArticleRoyaltyEntity], { name: 'article_royalties_batch_save' })
    async batchSave(
        @Args('body') body: ArticleRoyaltiesBatchSaveInputDto,
        @AuthUser() auth: UserEntity
    ): Promise<ArticleRoyaltyEntity[]> {
        return this.articleRoyaltiesService.saveBatch(body, auth.id);
    }
}
