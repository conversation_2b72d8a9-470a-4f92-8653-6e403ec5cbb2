import { Args, Int, Mutation, Parent, Query, ResolveField } from '@nestjs/graphql';
import { AuthResolver } from '../../../commons/decorators/graphql.decorators';
import { ArticleTagEntity } from '../../../entities/article-tag.entity';
import { ArticleTagsService } from '../services/article-tags.service';
import { DataLoaderService } from '../../../data-loaders/data-loaders.service';
import { BasePaginationInput } from '../../../commons/bases/base.input';
import { ArticleTagsModel } from '../models/article-tags.model';
import { IPaginatedType } from '../../../commons/bases/base.model';
import { NotFoundException } from '@nestjs/common';
import { AuthUser } from '../../auth/auth.decorator';
import { UserEntity } from '../../../entities/user.entity';
import { ArticleTagSaveInputDto } from '../dtos/article-tag-save-input.dto';
import { ArticleEntity } from '../../../entities/article.entity';
import { TagEntity } from '../../../entities/tag.entity';

@AuthResolver(ArticleTagEntity)
export class ArticleTagsResolver {
    constructor(
        private readonly articleTagsService: ArticleTagsService,
        private readonly dataLoader: DataLoaderService
    ) {}

    @ResolveField(() => ArticleEntity, { nullable: true })
    async article(@Parent() parent: ArticleTagEntity): Promise<ArticleEntity | null> {
        if (!parent.article_id) return null;
        return this.dataLoader.relationBatchOne(ArticleEntity).load(parent.article_id);
    }

    @ResolveField(() => TagEntity, { nullable: true })
    async tag(@Parent() parent: ArticleTagEntity): Promise<TagEntity | null> {
        if (!parent.tag_id) return null;
        return this.dataLoader.relationBatchOne(TagEntity).load(parent.tag_id);
    }

    @Query(() => ArticleTagsModel, { name: 'article_tags_list' })
    async index(@Args('body') body: BasePaginationInput): Promise<IPaginatedType<ArticleTagEntity>> {
        return this.articleTagsService.search(body);
    }

    @Query(() => ArticleTagEntity, { name: 'article_tags_detail' })
    async view(@Args('id', { type: () => Int }) id: number): Promise<ArticleTagEntity> {
        return this.articleTagsService.findOne(id).then((res) => {
            if (!res) throw new NotFoundException();
            return res;
        });
    }

    @Mutation(() => ArticleTagEntity, { name: 'article_tags_create' })
    async store(@Args('body') body: ArticleTagSaveInputDto, @AuthUser() auth: UserEntity): Promise<ArticleTagEntity> {
        return this.articleTagsService.create({ ...body, created_by: auth.id });
    }

    @Mutation(() => ArticleTagEntity, { name: 'article_tags_update' })
    async update(
        @Args('id', { type: () => Int }) id: number,
        @Args('body') body: ArticleTagSaveInputDto,
        @AuthUser() auth: UserEntity
    ): Promise<ArticleTagEntity> {
        return this.articleTagsService.updateOne(id, { ...body, updated_by: auth.id });
    }

    @Mutation(() => Boolean, { name: 'article_tags_delete' })
    async destroy(@Args('id', { type: () => Int }) id: number, @AuthUser() auth: UserEntity): Promise<boolean> {
        await this.articleTagsService.softDelete(id, auth.id);
        return true;
    }
}
