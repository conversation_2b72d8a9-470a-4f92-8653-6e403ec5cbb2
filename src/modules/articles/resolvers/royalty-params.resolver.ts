import { Args, Int, Mutation, Parent, Query, ResolveField } from '@nestjs/graphql';
import { AuthResolver } from '../../../commons/decorators/graphql.decorators';
import { RoyaltyParamEntity } from '../../../entities/royalty-param.entity';
import { RoyaltyParamsService } from '../services/royalty-params.service';
import { DataLoaderService } from '../../../data-loaders/data-loaders.service';
import { BasePaginationInput } from '../../../commons/bases/base.input';
import { RoyaltyParamsModel } from '../models/royalty-params.model';
import { IPaginatedType } from '../../../commons/bases/base.model';
import { NotFoundException } from '@nestjs/common';
import { AuthUser } from '../../auth/auth.decorator';
import { UserEntity } from '../../../entities/user.entity';
import { RoyaltyParamSaveInputDto } from '../dtos/royalty-param-save-input.dto';
import { ItemStatus } from '../../../commons/enums.common';
import { DepartmentEntity } from '../../../entities/department.entity';
import { RoyaltyParamOptionEntity } from '../../../entities/royalty-param-option.entity';

@AuthResolver(RoyaltyParamEntity)
export class RoyaltyParamsResolver {
    constructor(
        private readonly royaltyParamsService: RoyaltyParamsService,
        private readonly dataLoader: DataLoaderService
    ) {}

    @ResolveField(() => UserEntity, { nullable: true })
    async created_by(@Parent() parent: RoyaltyParamEntity): Promise<UserEntity | null> {
        if (!parent.created_by) return null;
        return this.dataLoader.relationBatchOne(UserEntity).load(parent.created_by);
    }

    @ResolveField(() => UserEntity, { nullable: true })
    async updated_by(@Parent() parent: RoyaltyParamEntity): Promise<UserEntity | null> {
        if (!parent.updated_by) return null;
        return this.dataLoader.relationBatchOne(UserEntity).load(parent.updated_by);
    }

    @ResolveField(() => UserEntity, { nullable: true })
    async deleted_by(@Parent() parent: RoyaltyParamEntity): Promise<UserEntity | null> {
        if (!parent.deleted_by) return null;
        return this.dataLoader.relationBatchOne(UserEntity).load(parent.deleted_by);
    }

    @ResolveField(() => DepartmentEntity, { nullable: true })
    async department(@Parent() parent: RoyaltyParamEntity): Promise<DepartmentEntity | null> {
        if (!parent.department_id) return null;
        return this.dataLoader.relationBatchOne(DepartmentEntity).load(parent.department_id);
    }

    @ResolveField(() => [RoyaltyParamOptionEntity], { nullable: true })
    async options(@Parent() parent: RoyaltyParamEntity): Promise<RoyaltyParamOptionEntity[]> {
        // Query options directly using the repository
        return this.royaltyParamsService.repo.manager.find(RoyaltyParamOptionEntity, {
            where: { royalty_param_id: parent.id },
            order: { display_order: 'ASC', id: 'ASC' },
        });
    }

    @Query(() => RoyaltyParamsModel, { name: 'royalty_params_list' })
    async index(@Args('body') body: BasePaginationInput): Promise<IPaginatedType<RoyaltyParamEntity>> {
        return this.royaltyParamsService.search(body);
    }

    @Query(() => RoyaltyParamEntity, { name: 'royalty_params_detail' })
    async view(@Args('id', { type: () => Int }) id: number): Promise<RoyaltyParamEntity> {
        return this.royaltyParamsService.findOne(id).then((res) => {
            if (!res) throw new NotFoundException();
            return res;
        });
    }

    @Mutation(() => RoyaltyParamEntity, { name: 'royalty_params_create' })
    async store(
        @Args('body') body: RoyaltyParamSaveInputDto,
        @AuthUser() auth: UserEntity
    ): Promise<RoyaltyParamEntity> {
        return this.royaltyParamsService.create({
            ...body,
            created_by: auth.id,
            status_id: body.status_id ?? ItemStatus.ACTIVE,
            display_order: body.display_order ?? 0,
        });
    }

    @Mutation(() => RoyaltyParamEntity, { name: 'royalty_params_update' })
    async update(
        @Args('id', { type: () => Int }) id: number,
        @Args('body') body: RoyaltyParamSaveInputDto,
        @AuthUser() auth: UserEntity
    ): Promise<RoyaltyParamEntity> {
        return this.royaltyParamsService.updateOne(id, { ...body, updated_by: auth.id });
    }

    @Mutation(() => Boolean, { name: 'royalty_params_delete' })
    async destroy(@Args('id', { type: () => Int }) id: number, @AuthUser() auth: UserEntity): Promise<boolean> {
        await this.royaltyParamsService.softDelete(id, auth.id);
        return true;
    }
}
