import { Args, Int, Mutation, Parent, Query, ResolveField, Resolver } from '@nestjs/graphql';
import { NotFoundException, UseGuards } from '@nestjs/common';
import { RoyaltyParamOptionEntity } from '../../../entities/royalty-param-option.entity';
import { RoyaltyParamOptionsService } from '../services/royalty-param-options.service';
import { DataLoaderService } from '../../../data-loaders/data-loaders.service';
import { AuthUser } from '../../auth/auth.decorator';
import { UserEntity } from '../../../entities/user.entity';
import { RoyaltyParamOptionSaveInputDto } from '../dtos/royalty-param-option-save-input.dto';
import { RoyaltyParamEntity } from '../../../entities/royalty-param.entity';
import { RoyaltyParamOptionsModel } from '../models/royalty-param-options.model';
import { BasePaginationInput } from '../../../commons/bases/base.input';
import { IPaginatedType } from '../../../commons/bases/base.model';
import { JwtAuthGuard } from '../../auth/jwt-auth.guard';

@Resolver(() => RoyaltyParamOptionEntity)
@UseGuards(JwtAuthGuard)
export class RoyaltyParamOptionsResolver {
    constructor(
        private readonly royaltyParamOptionsService: RoyaltyParamOptionsService,
        private readonly dataLoader: DataLoaderService
    ) {}

    @ResolveField(() => RoyaltyParamEntity)
    async royaltyParam(@Parent() parent: RoyaltyParamOptionEntity): Promise<RoyaltyParamEntity> {
        const result = await this.dataLoader.relationBatchOne(RoyaltyParamEntity).load(parent.royalty_param_id);
        if (!result) throw new NotFoundException('Royalty param not found');
        return result;
    }

    @Query(() => RoyaltyParamOptionsModel, { name: 'royalty_param_options_list' })
    async index(@Args('body') body: BasePaginationInput): Promise<IPaginatedType<RoyaltyParamOptionEntity>> {
        return this.royaltyParamOptionsService.search(body);
    }

    @Query(() => RoyaltyParamOptionEntity, { name: 'royalty_param_options_detail' })
    async view(@Args('id', { type: () => Int }) id: number): Promise<RoyaltyParamOptionEntity> {
        return this.royaltyParamOptionsService.findOne(id).then((res) => {
            if (!res) throw new NotFoundException();
            return res;
        });
    }

    @Mutation(() => RoyaltyParamOptionEntity, { name: 'royalty_param_options_create' })
    async store(
        @Args('body') body: RoyaltyParamOptionSaveInputDto,
        @AuthUser() auth: UserEntity
    ): Promise<RoyaltyParamOptionEntity> {
        return this.royaltyParamOptionsService.create({
            ...body,
            created_by: auth.id,
            display_order: body.display_order ?? 0,
        });
    }

    @Mutation(() => RoyaltyParamOptionEntity, { name: 'royalty_param_options_update' })
    async update(
        @Args('id', { type: () => Int }) id: number,
        @Args('body') body: RoyaltyParamOptionSaveInputDto,
        @AuthUser() auth: UserEntity
    ): Promise<RoyaltyParamOptionEntity> {
        return this.royaltyParamOptionsService.updateOne(id, { ...body, updated_by: auth.id });
    }

    @Mutation(() => Boolean, { name: 'royalty_param_options_delete' })
    async destroy(@Args('id', { type: () => Int }) id: number, @AuthUser() auth: UserEntity): Promise<boolean> {
        await this.royaltyParamOptionsService.softDelete(id, auth.id);
        return true;
    }
}
