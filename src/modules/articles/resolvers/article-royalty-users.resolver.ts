import { Args, Int, Mutation, Parent, Query, ResolveField } from '@nestjs/graphql';
import { AuthResolver } from '../../../commons/decorators/graphql.decorators';
import { ArticleRoyaltyUserEntity } from '../../../entities/article-royalty-user.entity';
import { ArticleRoyaltyUsersService } from '../services/article-royalty-users.service';
import { DataLoaderService } from '../../../data-loaders/data-loaders.service';
import { BasePaginationInput } from '../../../commons/bases/base.input';
import { ArticleRoyaltyUsersModel } from '../models/article-royalty-users.model';
import { IPaginatedType } from '../../../commons/bases/base.model';
import { NotFoundException } from '@nestjs/common';
import { AuthUser } from '../../auth/auth.decorator';
import { UserEntity } from '../../../entities/user.entity';
import { ArticleRoyaltyUserSaveInputDto } from '../dtos/article-royalty-user-save-input.dto';
import { ArticleRoyaltyEntity } from '../../../entities/article-royalty.entity';
import { FileEntity } from '../../../entities/file.entity';

@AuthResolver(ArticleRoyaltyUserEntity)
export class ArticleRoyaltyUsersResolver {
    constructor(
        private readonly articleRoyaltyUsersService: ArticleRoyaltyUsersService,
        private readonly dataLoader: DataLoaderService
    ) {}

    @ResolveField(() => UserEntity, { nullable: true })
    async createdByUser(@Parent() parent: ArticleRoyaltyUserEntity): Promise<UserEntity | null> {
        if (!parent.created_by) return null;
        return this.dataLoader.relationBatchOne(UserEntity).load(parent.created_by);
    }

    @ResolveField(() => UserEntity, { nullable: true })
    async updatedByUser(@Parent() parent: ArticleRoyaltyUserEntity): Promise<UserEntity | null> {
        if (!parent.updated_by) return null;
        return this.dataLoader.relationBatchOne(UserEntity).load(parent.updated_by);
    }

    @ResolveField(() => ArticleRoyaltyEntity)
    async articleRoyalty(@Parent() parent: ArticleRoyaltyUserEntity): Promise<ArticleRoyaltyEntity> {
        const result = await this.dataLoader.relationBatchOne(ArticleRoyaltyEntity).load(parent.article_royalty_id);
        if (!result) throw new NotFoundException('Article royalty not found');
        return result;
    }

    @ResolveField(() => UserEntity)
    async user(@Parent() parent: ArticleRoyaltyUserEntity): Promise<UserEntity> {
        const result = await this.dataLoader.relationBatchOne(UserEntity).load(parent.user_id);
        if (!result) throw new NotFoundException('User not found');
        return result;
    }

    @ResolveField(() => FileEntity, { nullable: true })
    async file(@Parent() parent: ArticleRoyaltyUserEntity): Promise<FileEntity | null> {
        if (!parent.file_id) return null;
        return this.dataLoader.relationBatchOne(FileEntity).load(parent.file_id);
    }

    @Query(() => ArticleRoyaltyUsersModel, { name: 'article_royalty_users_list' })
    async index(@Args('body') body: BasePaginationInput): Promise<IPaginatedType<ArticleRoyaltyUserEntity>> {
        return this.articleRoyaltyUsersService.search(body);
    }

    @Query(() => ArticleRoyaltyUserEntity, { name: 'article_royalty_users_detail' })
    async view(@Args('id', { type: () => Int }) id: number): Promise<ArticleRoyaltyUserEntity> {
        return this.articleRoyaltyUsersService.findOne(id).then((res) => {
            if (!res) throw new NotFoundException();
            return res;
        });
    }

    @Mutation(() => ArticleRoyaltyUserEntity, { name: 'article_royalty_users_create' })
    async store(
        @Args('body') body: ArticleRoyaltyUserSaveInputDto,
        @AuthUser() auth: UserEntity
    ): Promise<ArticleRoyaltyUserEntity> {
        return this.articleRoyaltyUsersService.create({ ...body, created_by: auth.id });
    }

    @Mutation(() => ArticleRoyaltyUserEntity, { name: 'article_royalty_users_update' })
    async update(
        @Args('id', { type: () => Int }) id: number,
        @Args('body') body: ArticleRoyaltyUserSaveInputDto,
        @AuthUser() auth: UserEntity
    ): Promise<ArticleRoyaltyUserEntity> {
        return this.articleRoyaltyUsersService.updateOne(id, { ...body, updated_by: auth.id });
    }

    @Mutation(() => Boolean, { name: 'article_royalty_users_delete' })
    async destroy(@Args('id', { type: () => Int }) id: number, @AuthUser() auth: UserEntity): Promise<boolean> {
        await this.articleRoyaltyUsersService.softDelete(id, auth.id);
        return true;
    }
}
