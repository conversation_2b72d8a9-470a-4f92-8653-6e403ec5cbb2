import { Args, Int, Mutation, Parent, Query, ResolveField } from '@nestjs/graphql';
import { AuthResolver } from '../../../commons/decorators/graphql.decorators';
import { ArticleCategoryEntity } from '../../../entities/article-category.entity';
import { ArticleCategoriesService } from '../services/article-categories.service';
import { DataLoaderService } from '../../../data-loaders/data-loaders.service';
import { BasePaginationInput } from '../../../commons/bases/base.input';
import { ArticleCategoriesModel } from '../models/article-categories.model';
import { IPaginatedType } from '../../../commons/bases/base.model';
import { NotFoundException } from '@nestjs/common';
import { AuthUser } from '../../auth/auth.decorator';
import { UserEntity } from '../../../entities/user.entity';
import { ArticleCategorySaveInputDto } from '../dtos/article-category-save-input.dto';
import { ArticleEntity } from '../../../entities/article.entity';
import { CategoryEntity } from '../../../entities/category.entity';

@AuthResolver(ArticleCategoryEntity)
export class ArticleCategoriesResolver {
    constructor(
        private readonly articleCategoriesService: ArticleCategoriesService,
        private readonly dataLoader: DataLoaderService
    ) {}

    @ResolveField(() => ArticleEntity, { nullable: true })
    async article(@Parent() parent: ArticleCategoryEntity): Promise<ArticleEntity | null> {
        if (!parent.article_id) return null;
        return this.dataLoader.relationBatchOne(ArticleEntity).load(parent.article_id);
    }

    @ResolveField(() => CategoryEntity, { nullable: true })
    async category(@Parent() parent: ArticleCategoryEntity): Promise<CategoryEntity | null> {
        if (!parent.category_id) return null;
        return this.dataLoader.relationBatchOne(CategoryEntity).load(parent.category_id);
    }

    @Query(() => ArticleCategoriesModel, { name: 'article_categories_list' })
    async index(@Args('body') body: BasePaginationInput): Promise<IPaginatedType<ArticleCategoryEntity>> {
        return this.articleCategoriesService.search(body);
    }

    @Query(() => ArticleCategoryEntity, { name: 'article_categories_detail' })
    async view(@Args('id', { type: () => Int }) id: number): Promise<ArticleCategoryEntity> {
        return this.articleCategoriesService.findOne(id).then((res) => {
            if (!res) throw new NotFoundException();
            return res;
        });
    }

    @Mutation(() => ArticleCategoryEntity, { name: 'article_categories_create' })
    async store(
        @Args('body') body: ArticleCategorySaveInputDto,
        @AuthUser() auth: UserEntity
    ): Promise<ArticleCategoryEntity> {
        return this.articleCategoriesService.saveArticleCategories(body, auth.id);
    }

    @Mutation(() => ArticleCategoryEntity, { name: 'article_categories_update' })
    async update(
        @Args('id', { type: () => Int }) id: number,
        @Args('body') body: ArticleCategorySaveInputDto,
        @AuthUser() auth: UserEntity
    ): Promise<ArticleCategoryEntity> {
        return this.articleCategoriesService.saveArticleCategories(body, auth.id, id);
    }

    @Mutation(() => Boolean, { name: 'article_categories_delete' })
    async destroy(@Args('id', { type: () => Int }) id: number, @AuthUser() auth: UserEntity): Promise<boolean> {
        await this.articleCategoriesService.softDelete(id, auth.id);
        return true;
    }

    @Mutation(() => ArticleCategoryEntity, { name: 'article_categories_set_display_order' })
    async setDisplayOrder(
        @Args('body') body: ArticleCategorySaveInputDto,
        @AuthUser() auth: UserEntity
    ): Promise<ArticleCategoryEntity> {
        return this.articleCategoriesService.setDisplayOrder(body, auth.id);
    }
}
