import { Args, Int, Mutation, Parent, Query, ResolveField } from '@nestjs/graphql';
import { AuthResolver } from '../../../commons/decorators/graphql.decorators';
import { ArticleCommentEntity } from '../../../entities/article-comment.entity';
import { ArticleCommentsService } from '../services/article-comments.service';
import { DataLoaderService } from '../../../data-loaders/data-loaders.service';
import { BasePaginationInput } from '../../../commons/bases/base.input';
import { ArticleCommentsModel } from '../models/article-comments.model';
import { IPaginatedType } from '../../../commons/bases/base.model';
import { NotFoundException } from '@nestjs/common';
import { AuthUser } from '../../auth/auth.decorator';
import { UserEntity } from '../../../entities/user.entity';
import { ArticleCommentSaveInputDto } from '../dtos/article-comment-save-input.dto';
import { ArticleEntity } from '../../../entities/article.entity';

@AuthResolver(ArticleCommentEntity)
export class ArticleCommentsResolver {
    constructor(
        private readonly articleCommentsService: ArticleCommentsService,
        private readonly dataLoader: DataLoaderService
    ) {}

    @ResolveField(() => ArticleEntity, { nullable: true })
    async article(@Parent() parent: ArticleCommentEntity): Promise<ArticleEntity | null> {
        if (!parent.article_id) return null;
        return this.dataLoader.relationBatchOne(ArticleEntity).load(parent.article_id);
    }

    @Query(() => ArticleCommentsModel, { name: 'article_comments_list' })
    async index(@Args('body') body: BasePaginationInput): Promise<IPaginatedType<ArticleCommentEntity>> {
        return this.articleCommentsService.search(body);
    }

    @Query(() => ArticleCommentEntity, { name: 'article_comments_detail' })
    async view(@Args('id', { type: () => Int }) id: number): Promise<ArticleCommentEntity> {
        return this.articleCommentsService.findOne(id).then((res) => {
            if (!res) throw new NotFoundException();
            return res;
        });
    }

    @Mutation(() => ArticleCommentEntity, { name: 'article_comments_create' })
    async store(
        @Args('body') body: ArticleCommentSaveInputDto,
        @AuthUser() auth: UserEntity
    ): Promise<ArticleCommentEntity> {
        return this.articleCommentsService.create({ ...body, created_by: auth.id });
    }

    @Mutation(() => ArticleCommentEntity, { name: 'article_comments_update' })
    async update(
        @Args('id', { type: () => Int }) id: number,
        @Args('body') body: ArticleCommentSaveInputDto,
        @AuthUser() auth: UserEntity
    ): Promise<ArticleCommentEntity> {
        return this.articleCommentsService.updateOne(id, { ...body, updated_by: auth.id });
    }

    @Mutation(() => Boolean, { name: 'article_comments_delete' })
    async destroy(@Args('id', { type: () => Int }) id: number, @AuthUser() auth: UserEntity): Promise<boolean> {
        await this.articleCommentsService.softDelete(id, auth.id);
        return true;
    }
}
