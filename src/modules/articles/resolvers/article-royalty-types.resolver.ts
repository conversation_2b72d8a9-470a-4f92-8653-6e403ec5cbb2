import { Args, Int, Mutation, Parent, Query, ResolveField } from '@nestjs/graphql';
import { AuthResolver } from '../../../commons/decorators/graphql.decorators';
import { ArticleRoyaltyTypeEntity } from '../../../entities/article-royalty-type.entity';
import { ArticleRoyaltyTypesService } from '../services/article-royalty-types.service';
import { DataLoaderService } from '../../../data-loaders/data-loaders.service';
import { BasePaginationInput } from '../../../commons/bases/base.input';
import { ArticleRoyaltyTypesModel } from '../models/article-royalty-types.model';
import { IPaginatedType } from '../../../commons/bases/base.model';
import { NotFoundException } from '@nestjs/common';
import { AuthUser } from '../../auth/auth.decorator';
import { UserEntity } from '../../../entities/user.entity';
import { ArticleRoyaltyTypeSaveInputDto } from '../dtos/article-royalty-type-save-input.dto';

@AuthResolver(ArticleRoyaltyTypeEntity)
export class ArticleRoyaltyTypesResolver {
    constructor(
        private readonly articleRoyaltyTypesService: ArticleRoyaltyTypesService,
        private readonly dataLoader: DataLoaderService
    ) {}

    @ResolveField(() => UserEntity, { nullable: true })
    async createdByUser(@Parent() parent: ArticleRoyaltyTypeEntity): Promise<UserEntity | null> {
        if (!parent.created_by) return null;
        return this.dataLoader.relationBatchOne(UserEntity).load(parent.created_by);
    }

    @ResolveField(() => UserEntity, { nullable: true })
    async updatedByUser(@Parent() parent: ArticleRoyaltyTypeEntity): Promise<UserEntity | null> {
        if (!parent.updated_by) return null;
        return this.dataLoader.relationBatchOne(UserEntity).load(parent.updated_by);
    }

    @Query(() => ArticleRoyaltyTypesModel, { name: 'article_royalty_types_list' })
    async index(@Args('body') body: BasePaginationInput): Promise<IPaginatedType<ArticleRoyaltyTypeEntity>> {
        return this.articleRoyaltyTypesService.search(body);
    }

    @Query(() => ArticleRoyaltyTypeEntity, { name: 'article_royalty_types_detail' })
    async view(@Args('id', { type: () => Int }) id: number): Promise<ArticleRoyaltyTypeEntity> {
        return this.articleRoyaltyTypesService.findOne(id).then((res) => {
            if (!res) throw new NotFoundException();
            return res;
        });
    }

    @Mutation(() => ArticleRoyaltyTypeEntity, { name: 'article_royalty_types_create' })
    async store(
        @Args('body') body: ArticleRoyaltyTypeSaveInputDto,
        @AuthUser() auth: UserEntity
    ): Promise<ArticleRoyaltyTypeEntity> {
        return this.articleRoyaltyTypesService.create({ ...body, created_by: auth.id });
    }

    @Mutation(() => ArticleRoyaltyTypeEntity, { name: 'article_royalty_types_update' })
    async update(
        @Args('id', { type: () => Int }) id: number,
        @Args('body') body: ArticleRoyaltyTypeSaveInputDto,
        @AuthUser() auth: UserEntity
    ): Promise<ArticleRoyaltyTypeEntity> {
        return this.articleRoyaltyTypesService.updateOne(id, { ...body, updated_by: auth.id });
    }

    @Mutation(() => Boolean, { name: 'article_royalty_types_delete' })
    async destroy(@Args('id', { type: () => Int }) id: number, @AuthUser() auth: UserEntity): Promise<boolean> {
        await this.articleRoyaltyTypesService.softDelete(id, auth.id);
        return true;
    }
}
