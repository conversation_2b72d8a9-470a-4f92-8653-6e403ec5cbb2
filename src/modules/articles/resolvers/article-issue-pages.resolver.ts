import { Args, Int, Mutation, Parent, Query, ResolveField } from '@nestjs/graphql';
import { In } from 'typeorm';
import { AuthResolver } from '../../../commons/decorators/graphql.decorators';
import { ArticleIssuePageEntity } from '../../../entities/article-issue-page.entity';
import { ArticleIssuePagesService } from '../services/article-issue-pages.service';
import { DataLoaderService } from '../../../data-loaders/data-loaders.service';
import { BasePaginationInput } from '../../../commons/bases/base.input';
import { ArticleIssuePagesModel } from '../models/article-issue-pages.model';
import { IPaginatedType } from '../../../commons/bases/base.model';
import { NotFoundException } from '@nestjs/common';
import { AuthUser } from '../../auth/auth.decorator';
import { UserEntity } from '../../../entities/user.entity';
import { ArticleIssuePageSaveInputDto } from '../dtos/article-issue-page-save-input.dto';
import { ArticleEntity } from '../../../entities/article.entity';
import { IssuePageEntity } from '../../../entities/issue-page.entity';
import { ArticleIssuePageCreateBatchInputDto } from '../dtos/article-issue-page-create-batch-input.dto';
import { ArticleIssuePageUpdatePageInputDto } from '../dtos/article-issue-page-update-page-input.dto';
import { ArticleIssuePageBatchUpdatePositionInputDto } from '../dtos/article-issue-page-batch-update-position-input.dto';

@AuthResolver(ArticleIssuePageEntity)
export class ArticleIssuePagesResolver {
    constructor(
        private readonly articleIssuePagesService: ArticleIssuePagesService,
        private readonly dataLoader: DataLoaderService
    ) {}

    @ResolveField(() => ArticleEntity)
    async article(@Parent() parent: ArticleIssuePageEntity): Promise<ArticleEntity | null> {
        if (!parent.article_id) return null;
        return this.dataLoader.relationBatchOne(ArticleEntity).load(parent.article_id);
    }

    @ResolveField(() => IssuePageEntity, { nullable: true })
    async issuePage(@Parent() parent: ArticleIssuePageEntity): Promise<IssuePageEntity | null> {
        if (!parent.issue_page_id) return null;
        return this.dataLoader.relationBatchOne(IssuePageEntity).load(parent.issue_page_id);
    }

    @Query(() => ArticleIssuePagesModel, { name: 'article_issue_pages_list' })
    async index(@Args('body') body: BasePaginationInput): Promise<IPaginatedType<ArticleIssuePageEntity>> {
        return this.articleIssuePagesService.search(body);
    }

    @Query(() => ArticleIssuePageEntity, { name: 'article_issue_pages_detail' })
    async view(@Args('id', { type: () => Int }) id: number): Promise<ArticleIssuePageEntity> {
        return this.articleIssuePagesService.findOne(id).then((res) => {
            if (!res) throw new NotFoundException();
            return res;
        });
    }

    @Mutation(() => ArticleIssuePageEntity, { name: 'article_issue_pages_create' })
    async store(
        @Args('body') body: ArticleIssuePageSaveInputDto,
        @AuthUser() auth: UserEntity
    ): Promise<ArticleIssuePageEntity> {
        return this.articleIssuePagesService.create({ ...body, created_by: auth.id });
    }

    @Mutation(() => ArticleIssuePageEntity, { name: 'article_issue_pages_update' })
    async update(
        @Args('id', { type: () => Int }) id: number,
        @Args('body') body: ArticleIssuePageSaveInputDto,
        @AuthUser() auth: UserEntity
    ): Promise<ArticleIssuePageEntity> {
        return this.articleIssuePagesService.updateOne(id, { ...body, updated_by: auth.id });
    }

    @Mutation(() => Boolean, { name: 'article_issue_pages_delete' })
    async destroy(@Args('id', { type: () => Int }) id: number, @AuthUser() auth: UserEntity): Promise<boolean> {
        await this.articleIssuePagesService.softDelete(id, auth.id);
        return true;
    }

    @Mutation(() => [ArticleIssuePageEntity], { name: 'article_issue_pages_create_batch' })
    async createBatch(
        @Args('body') body: ArticleIssuePageCreateBatchInputDto,
        @AuthUser() auth: UserEntity
    ): Promise<ArticleIssuePageEntity[]> {
        return this.articleIssuePagesService.createBatch(body, auth.id);
    }

    //update page
    @Mutation(() => ArticleIssuePageEntity, { name: 'article_issue_pages_update_page' })
    async updatePage(
        @Args('id', { type: () => Int }) id: number,
        @Args('body') body: ArticleIssuePageUpdatePageInputDto,
        @AuthUser() auth: UserEntity
    ): Promise<ArticleIssuePageEntity> {
        return this.articleIssuePagesService.updatePage(id, body, auth.id);
    }

    //update position (batch)
    @Mutation(() => [ArticleIssuePageEntity], { name: 'article_issue_pages_update_position' })
    async updatePosition(
        @Args('body') body: ArticleIssuePageBatchUpdatePositionInputDto,
        @AuthUser() auth: UserEntity
    ): Promise<ArticleIssuePageEntity[]> {
        const ids = body.items.map((item) => item.id);

        // Verify all entities exist
        const existingEntities = await this.articleIssuePagesService.find({
            where: { id: In(ids) } as any,
        });
        if (existingEntities.length !== ids.length) {
            throw new NotFoundException('One or more article issue pages not found');
        }

        // Perform batch update
        const updatePromises = body.items.map((item) =>
            this.articleIssuePagesService.updateOne(item.id, {
                position: item.position,
                updated_by: auth.id,
            })
        );

        return Promise.all(updatePromises);
    }
}
