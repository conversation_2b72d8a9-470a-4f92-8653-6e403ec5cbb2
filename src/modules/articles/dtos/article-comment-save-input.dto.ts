import { Field, InputType, Int } from '@nestjs/graphql';
import { IsEmail, IsEnum, IsNotEmpty, IsString } from 'class-validator';
import { validationMessagesLang } from '../../../languages/validation-messages.lang';
import { IdExists } from '../../../commons/validators/id-exists.validator';
import { BaseUpdateInputDto } from '../../../commons/bases/base.input';
import { ItemStatus } from '../../../commons/enums.common';
import { ArticleEntity } from '../../../entities/article.entity';
import { AutoSanitize, SanitizeEmail } from '../../../commons/decorators/sanitize.decorators';

@InputType()
@AutoSanitize()
export class ArticleCommentSaveInputDto extends BaseUpdateInputDto {
    @Field(() => Int)
    @IsNotEmpty({ message: validationMessagesLang.REQUIRED })
    @IdExists(ArticleEntity)
    article_id: number;

    @Field()
    @IsNotEmpty({ message: validationMessagesLang.REQUIRED })
    @IsString()
    sender_name: string;

    @Field()
    @IsNotEmpty({ message: validationMessagesLang.REQUIRED })
    @IsEmail()
    @SanitizeEmail()
    sender_email: string;

    @Field()
    @IsNotEmpty({ message: validationMessagesLang.REQUIRED })
    @IsString()
    content: string;

    @Field(() => Int)
    @IsNotEmpty({ message: validationMessagesLang.REQUIRED })
    @IsEnum(ItemStatus)
    status_id: ItemStatus;

    @Field()
    @IsNotEmpty({ message: validationMessagesLang.REQUIRED })
    @IsString()
    ip_address: string;

    @Field()
    @IsNotEmpty({ message: validationMessagesLang.REQUIRED })
    @IsString()
    user_agent: string;
}
