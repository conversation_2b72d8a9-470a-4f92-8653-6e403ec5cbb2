import { Field, InputType, Int } from '@nestjs/graphql';
import { IsArray, IsNotEmpty, IsOptional, ValidateNested } from 'class-validator';
import { Type } from 'class-transformer';
import { validationMessagesLang } from '../../../languages/validation-messages.lang';
import { IdExists } from '../../../commons/validators/id-exists.validator';
import { ArticleIssuePageEntity } from '../../../entities/article-issue-page.entity';
import { AutoSanitize } from '../../../commons/decorators/sanitize.decorators';
import GraphQLJSON from 'graphql-type-json';

@InputType()
@AutoSanitize()
export class ArticleIssuePagePositionItem {
    @Field(() => Int)
    @IsNotEmpty({ message: validationMessagesLang.REQUIRED })
    @IdExists(ArticleIssuePageEntity)
    id: number;

    @Field(() => GraphQLJSON, { nullable: true })
    @IsOptional()
    position?: any;
}

@InputType()
@AutoSanitize()
export class ArticleIssuePageBatchUpdatePositionInputDto {
    @Field(() => [ArticleIssuePagePositionItem])
    @IsArray()
    @ValidateNested({ each: true })
    @Type(() => ArticleIssuePagePositionItem)
    items: ArticleIssuePagePositionItem[];
}
