import { Field, InputType, Int } from '@nestjs/graphql';
import { IsEnum } from 'class-validator';
import { AutoSanitize } from '../../../commons/decorators/sanitize.decorators';
import { ArticleTypes } from '../../../entities/workflow-permission-article-type.entity';

@InputType()
@AutoSanitize()
export class ArticleCloneInputDto {
    @Field(() => Int)
    @IsEnum(ArticleTypes)
    article_type_id: ArticleTypes;
}
