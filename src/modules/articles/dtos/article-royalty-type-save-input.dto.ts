import { Field, InputType, Int } from '@nestjs/graphql';
import { IsBoolean, IsEnum, IsInt, IsNotEmpty, IsOptional, IsString, <PERSON><PERSON>ength, Min } from 'class-validator';
import { validationMessagesLang } from '../../../languages/validation-messages.lang';
import { BaseUpdateInputDto } from '../../../commons/bases/base.input';
import { AutoSanitize } from '../../../commons/decorators/sanitize.decorators';
import { ArticleTypes } from '../../../entities/workflow-permission-article-type.entity';
import { MoreThanEqualField } from '../../../commons/validators/more-than-equal-field.validator';
import { ItemStatus } from '../../../commons/enums.common';

@InputType()
@AutoSanitize()
export class ArticleRoyaltyTypeSaveInputDto extends BaseUpdateInputDto {
    @Field()
    @IsNotEmpty({ message: validationMessagesLang.REQUIRED })
    @IsString()
    @MaxLength(255)
    name: string;

    @Field({ nullable: true })
    @IsOptional()
    @IsString()
    @MaxLength(255)
    desc?: string;

    @Field(() => Int)
    @IsNotEmpty({ message: validationMessagesLang.REQUIRED })
    @IsEnum(ArticleTypes)
    article_type_id: ArticleTypes;

    @Field(() => Int)
    @IsNotEmpty({ message: validationMessagesLang.REQUIRED })
    @IsInt()
    @Min(0)
    from_royalty: number;

    @Field(() => Int)
    @IsNotEmpty({ message: validationMessagesLang.REQUIRED })
    @IsInt()
    @Min(0)
    @MoreThanEqualField('from_royalty', { message: 'To royalty must be greater than or equal to from royalty' })
    to_royalty: number;

    @Field({ defaultValue: false })
    @IsOptional()
    @IsBoolean()
    is_default?: boolean;

    @Field(() => Int)
    @IsNotEmpty({ message: validationMessagesLang.REQUIRED })
    @IsEnum(ItemStatus)
    status_id: ItemStatus;
}
