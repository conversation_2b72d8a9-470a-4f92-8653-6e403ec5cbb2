import { Field, InputType, Int } from '@nestjs/graphql';
import GraphQLJSON from 'graphql-type-json';
import { IsArray, IsInt, IsNotEmpty, IsO<PERSON>al, <PERSON>String, <PERSON>, <PERSON> } from 'class-validator';
import { validationMessagesLang } from '../../../languages/validation-messages.lang';
import { BaseUpdateInputDto } from '../../../commons/bases/base.input';
import { AutoSanitize } from '../../../commons/decorators/sanitize.decorators';
import { IdExists } from '../../../commons/validators/id-exists.validator';
import { ArticleRoyaltyEntity } from '../../../entities/article-royalty.entity';
import { UserEntity } from '../../../entities/user.entity';
import { FileEntity } from '../../../entities/file.entity';

@InputType()
@AutoSanitize()
export class ArticleRoyaltyUserSaveInputDto extends BaseUpdateInputDto {
    @Field(() => Int)
    @IsNotEmpty({ message: validationMessagesLang.REQUIRED })
    @IdExists(ArticleRoyaltyEntity)
    article_royalty_id: number;

    @Field(() => Int)
    @IsNotEmpty({ message: validationMessagesLang.REQUIRED })
    @IdExists(UserEntity)
    user_id: number;

    @Field(() => Int)
    @IsNotEmpty({ message: validationMessagesLang.REQUIRED })
    @IsInt()
    @Min(0)
    @Max(100)
    percent: number;

    @Field({ nullable: true })
    @IsOptional()
    @IsString()
    comment?: string;

    @Field(() => GraphQLJSON, { nullable: true })
    @IsOptional()
    @IsArray()
    param_config?: Array<{ value: string; label: string }>;

    @Field(() => Int, { nullable: true })
    @IsOptional()
    @IsInt()
    @Min(0)
    final_royalty?: number;

    @Field(() => Int, { nullable: true })
    @IsOptional()
    @IsInt()
    @Min(0)
    suggest_royalty?: number;

    @Field(() => Int, { nullable: true })
    @IsOptional()
    @IsInt()
    @IdExists(FileEntity)
    file_id?: number;
}
