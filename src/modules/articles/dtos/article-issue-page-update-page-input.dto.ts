import { Field, InputType, Int } from '@nestjs/graphql';
import { IdExists } from '../../../commons/validators/id-exists.validator';
import { IssuePageEntity } from '../../../entities/issue-page.entity';
import { AutoSanitize } from '../../../commons/decorators/sanitize.decorators';

@InputType()
@AutoSanitize()
export class ArticleIssuePageUpdatePageInputDto {
    @Field(() => Int)
    @IdExists(IssuePageEntity)
    issue_page_id: number;
}
