import { Field, InputType, Int } from '@nestjs/graphql';
import { IsEnum, IsOptional } from 'class-validator';
import { TypesettingStatus } from '../../../entities/article.entity';
import { AutoSanitize } from '../../../commons/decorators/sanitize.decorators';

@InputType()
@AutoSanitize()
export class ArticleChangeTypesettingStatusInputDto {
    @Field(() => Int, { nullable: true })
    @IsOptional()
    @IsEnum(TypesettingStatus)
    typesetting_status_id?: TypesettingStatus;
}
