import { Field, InputType, Int } from '@nestjs/graphql';
import { IsInt, IsNotEmpty, IsOptional, IsPositive, IsString } from 'class-validator';
import { BaseUpdateInputDto } from '../../../commons/bases/base.input';
import { validationMessagesLang } from '../../../languages/validation-messages.lang';
import { IdExists } from '../../../commons/validators/id-exists.validator';
import { ArticleEntity } from '../../../entities/article.entity';
import { IssuePageEntity } from '../../../entities/issue-page.entity';
import { AutoSanitize } from '../../../commons/decorators/sanitize.decorators';
import GraphQLJSON from 'graphql-type-json';

@InputType()
@AutoSanitize()
export class ArticleIssuePageSaveInputDto extends BaseUpdateInputDto {
    @Field(() => Int)
    @IsNotEmpty({ message: validationMessagesLang.REQUIRED })
    @IdExists(ArticleEntity)
    article_id: number;

    @Field(() => Int, { nullable: true })
    @IsOptional()
    @IdExists(IssuePageEntity)
    issue_page_id?: number;

    @Field(() => Int)
    @IsNotEmpty({ message: validationMessagesLang.REQUIRED })
    @IsInt()
    @IsPositive()
    display_order: number;

    @Field({ nullable: true })
    @IsOptional()
    @IsString()
    comment?: string;

    @Field(() => GraphQLJSON, { nullable: true })
    @IsOptional()
    position?: any;
}
