import { Field, InputType, Int } from '@nestjs/graphql';
import { IsNotEmpty } from 'class-validator';
import { validationMessagesLang } from '../../../languages/validation-messages.lang';
import { IdExists } from '../../../commons/validators/id-exists.validator';
import { BaseUpdateInputDto } from '../../../commons/bases/base.input';
import { ArticleEntity } from '../../../entities/article.entity';
import { TagEntity } from '../../../entities/tag.entity';
import { AutoSanitize } from '../../../commons/decorators/sanitize.decorators';

@InputType()
@AutoSanitize()
export class ArticleTagSaveInputDto extends BaseUpdateInputDto {
    @Field(() => Int)
    @IsNotEmpty({ message: validationMessagesLang.REQUIRED })
    @IdExists(ArticleEntity)
    article_id: number;

    @Field(() => Int)
    @IsNotEmpty({ message: validationMessagesLang.REQUIRED })
    @IdExists(TagEntity)
    tag_id: number;
}
