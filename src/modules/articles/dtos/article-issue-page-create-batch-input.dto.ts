import { Field, InputType, Int } from '@nestjs/graphql';
import { ArrayNotEmpty, IsArray, IsInt } from 'class-validator';
import { IdExists } from '../../../commons/validators/id-exists.validator';
import { ArticleEntity } from '../../../entities/article.entity';
import { IssuePageEntity } from '../../../entities/issue-page.entity';
import { AutoSanitize } from '../../../commons/decorators/sanitize.decorators';

@InputType()
@AutoSanitize()
export class ArticleIssuePageCreateBatchInputDto {
    @Field(() => [Int])
    @IsArray()
    @ArrayNotEmpty()
    @IsInt({ each: true })
    @IdExists(ArticleEntity, { each: true })
    article_ids: number[];

    @Field(() => Int)
    @IdExists(IssuePageEntity)
    issue_page_id: number;
}
