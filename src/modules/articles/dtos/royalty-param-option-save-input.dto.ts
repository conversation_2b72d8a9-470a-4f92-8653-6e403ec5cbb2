import { Field, InputType, Int } from '@nestjs/graphql';
import GraphQLJSON from 'graphql-type-json';
import { IsArray, IsInt, IsNotEmpty, IsOptional, IsString, Min } from 'class-validator';
import { validationMessagesLang } from '../../../languages/validation-messages.lang';
import { BaseUpdateInputDto } from '../../../commons/bases/base.input';
import { AutoSanitize } from '../../../commons/decorators/sanitize.decorators';
import { IdExists } from '../../../commons/validators/id-exists.validator';
import { RoyaltyParamEntity } from '../../../entities/royalty-param.entity';

@InputType()
@AutoSanitize()
export class RoyaltyParamOptionSaveInputDto extends BaseUpdateInputDto {
    @Field(() => String)
    @IsNotEmpty({ message: validationMessagesLang.REQUIRED })
    @IsString()
    name: string;

    @Field(() => String)
    @IsNotEmpty({ message: validationMessagesLang.REQUIRED })
    @IsString()
    value: string;

    @Field(() => Int)
    @IsNotEmpty({ message: validationMessagesLang.REQUIRED })
    @IsInt()
    @Min(0)
    ratio: number;

    @Field(() => Int, { nullable: true })
    @IsOptional()
    @IsInt()
    @Min(0)
    display_order?: number;

    @Field(() => GraphQLJSON, { nullable: true })
    @IsOptional()
    @IsArray()
    @IsInt({ each: true })
    user_ids?: number[];

    @Field(() => Int)
    @IsNotEmpty({ message: validationMessagesLang.REQUIRED })
    @IsInt()
    @IdExists(RoyaltyParamEntity)
    royalty_param_id: number;
}
