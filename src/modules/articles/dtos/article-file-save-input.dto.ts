import { Field, InputType, Int } from '@nestjs/graphql';
import { IsBoolean, IsInt, IsNotEmpty, IsOptional, Min } from 'class-validator';
import { validationMessagesLang } from '../../../languages/validation-messages.lang';
import { IdExists } from '../../../commons/validators/id-exists.validator';
import { BaseUpdateInputDto } from '../../../commons/bases/base.input';
import { ArticleEntity } from '../../../entities/article.entity';
import { FileEntity } from '../../../entities/file.entity';
import { UserEntity } from '../../../entities/user.entity';
import { AutoSanitize } from '../../../commons/decorators/sanitize.decorators';

@InputType()
@AutoSanitize()
export class ArticleFileSaveInputDto extends BaseUpdateInputDto {
    @Field(() => Int)
    @IsNotEmpty({ message: validationMessagesLang.REQUIRED })
    @IdExists(ArticleEntity)
    article_id: number;

    @Field(() => Int)
    @IsNotEmpty({ message: validationMessagesLang.REQUIRED })
    @IdExists(FileEntity)
    file_id: number;

    @Field({ defaultValue: false })
    @IsOptional()
    @IsBoolean()
    is_royalty?: boolean;

    @Field(() => Int, { nullable: true })
    @IsOptional()
    @IdExists(UserEntity)
    author_id?: number;

    @Field(() => Int, { nullable: true })
    @IsOptional()
    @IsInt()
    @Min(0)
    royalty_cost?: number;
}

@InputType()
@AutoSanitize()
export class ArticleFileIsRoyaltyUpdateInputDto {
    @Field()
    @IsNotEmpty({ message: validationMessagesLang.REQUIRED })
    @IsBoolean()
    is_royalty: boolean;
}
