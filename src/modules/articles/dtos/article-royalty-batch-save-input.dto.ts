import { Field, InputType, Int } from '@nestjs/graphql';
import { Type } from 'class-transformer';
import { IsArray, IsEnum, IsInt, IsNotEmpty, IsOptional, IsString, Max, Min, ValidateNested } from 'class-validator';
import Graph<PERSON><PERSON>SON from 'graphql-type-json';
import { validationMessagesLang } from '../../../languages/validation-messages.lang';
import { AutoSanitize } from '../../../commons/decorators/sanitize.decorators';
import { IdExists } from '../../../commons/validators/id-exists.validator';
import { ArticleEntity } from '../../../entities/article.entity';
import { ArticleRoyaltyTypeEntity } from '../../../entities/article-royalty-type.entity';
import { UserEntity } from '../../../entities/user.entity';
import { RoyaltyType } from '../../../entities/article-royalty.entity';

@InputType()
export class ArticleRoyaltyUserBatchInput {
    @Field(() => Int, { nullable: true })
    @IsOptional()
    id?: number;

    @Field(() => Int)
    @IsNotEmpty({ message: validationMessagesLang.REQUIRED })
    @IdExists(UserEntity)
    user_id: number;

    @Field(() => Int)
    @IsNotEmpty({ message: validationMessagesLang.REQUIRED })
    @IsInt()
    @Min(0)
    @Max(100)
    percent: number;

    @Field({ nullable: true })
    @IsOptional()
    @IsString()
    comment?: string;

    @Field(() => GraphQLJSON, { nullable: true })
    @IsOptional()
    param_config?: Array<{ value: string; label: string }>;

    @Field(() => Int, { nullable: true })
    @IsOptional()
    @IsInt()
    @Min(0)
    final_royalty?: number;

    @Field(() => Int, { nullable: true })
    @IsOptional()
    @IsInt()
    @Min(0)
    suggest_royalty?: number;
}

@InputType()
export class ArticleRoyaltyBatchInput {
    @Field(() => Int, { nullable: true })
    @IsOptional()
    id?: number;

    @Field(() => Int, { nullable: true })
    @IsOptional()
    @IdExists(ArticleEntity)
    article_id?: number;

    @Field(() => Int)
    @IsNotEmpty({ message: validationMessagesLang.REQUIRED })
    @IsEnum(RoyaltyType)
    type_id: RoyaltyType;

    @Field(() => Int, { nullable: true })
    @IsOptional()
    @IdExists(ArticleRoyaltyTypeEntity)
    royalty_type_id?: number;

    @Field(() => Int)
    @IsNotEmpty({ message: validationMessagesLang.REQUIRED })
    @IsInt()
    @Min(0)
    suggest_royalty: number;

    @Field({ nullable: true })
    @IsOptional()
    @IsString()
    title?: string;

    @Field(() => Int, { nullable: true })
    @IsOptional()
    @IsInt()
    @Min(0)
    article_statistic?: number;

    @Field(() => [ArticleRoyaltyUserBatchInput], { nullable: true })
    @IsOptional()
    @IsArray()
    @ValidateNested({ each: true })
    @Type(() => ArticleRoyaltyUserBatchInput)
    article_royalty_users?: ArticleRoyaltyUserBatchInput[];
}

@InputType()
@AutoSanitize()
export class ArticleRoyaltiesBatchSaveInputDto {
    @Field(() => Int, { nullable: true })
    @IsOptional()
    @IdExists(ArticleEntity)
    article_id?: number;

    @Field(() => [ArticleRoyaltyBatchInput])
    @IsArray()
    @ValidateNested({ each: true })
    @Type(() => ArticleRoyaltyBatchInput)
    royalties: ArticleRoyaltyBatchInput[];
}