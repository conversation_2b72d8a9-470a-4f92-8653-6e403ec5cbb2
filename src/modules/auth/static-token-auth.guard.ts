import { CanActivate, ExecutionContext, Injectable, UnauthorizedException } from '@nestjs/common';
import { GqlExecutionContext } from '@nestjs/graphql';
import appConf from '../../configs/app.conf';

@Injectable()
export class StaticTokenAuthGuard implements CanActivate {
    canActivate(context: ExecutionContext): boolean {
        const isRest = context.getType() === 'http';
        const request = isRest
            ? context.switchToHttp().getRequest()
            : GqlExecutionContext.create(context).getContext().req;

        // Kiểm tra Authorization header
        const authHeader = request.headers.authorization;
        if (!authHeader || !authHeader.startsWith('Bearer ')) {
            throw new UnauthorizedException('Missing or invalid authorization token');
        }

        const token = authHeader.replace('Bearer ', '');

        // Ki<PERSON><PERSON> tra static token
        if (!appConf.STATIC_BEARER_TOKEN) {
            throw new UnauthorizedException('Static bearer token not configured');
        }

        if (token !== appConf.STATIC_BEARER_TOKEN) {
            throw new UnauthorizedException('Invalid static bearer token');
        }
        return true;
    }
}
