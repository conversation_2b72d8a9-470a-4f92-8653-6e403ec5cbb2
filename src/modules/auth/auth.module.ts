import { Global, Module } from '@nestjs/common';
import { JwtModule } from '@nestjs/jwt';
import { PassportModule } from '@nestjs/passport';
import { JwtStrategy } from './jwt.strategy';
import { AuthResolver } from './auth.resolver';
import { AuthService } from './services/auth.service';
import { TypeOrmModule } from '@nestjs/typeorm';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { UserEntity } from '../../entities/user.entity';
import { PersonalTokenEntity } from '../../entities/personal-token.entity';
import { PersonalTokenService } from './services/personal-token.service';
import { IdExistsConstraint } from '../../commons/validators/id-exists.validator';
import { JwtAuthGuard } from './jwt-auth.guard';
import { StaticTokenAuthGuard } from './static-token-auth.guard';
import { UniqueEmailByRoleConstraint } from '../../commons/validators/unique-email.validator';
import { UniquePhoneNumberByRoleConstraint } from '../../commons/validators/unique-phone_number.validator';
import { IsUniqueConstraint } from '../../commons/validators/is-unique.validator';
import { IsSureUniqueConstraint } from '../../commons/validators/is-sure-unique.validator';
import { IsUniqueByConstraint } from '../../commons/validators/is-unique-by.validator';
import { DevicesModule } from '../devices/devices.module';
import appConf from '../../configs/app.conf';
import { UniqueWorkflowByTypeConstraint } from '../../commons/validators/unique-workflow-by-type.validator';
import { HttpModule } from '@nestjs/axios';

@Global()
@Module({
    imports: [
        TypeOrmModule.forFeature([UserEntity, PersonalTokenEntity]),
        PassportModule.register({ defaultStrategy: 'jwt' }),
        JwtModule.registerAsync({
            imports: [ConfigModule],
            inject: [ConfigService],
            useFactory: () => {
                const atSecret = appConf.AT_SECRET;
                if (!atSecret) {
                    throw new Error('AT_SECRET is not defined in environment variables');
                }
                const rtSecret = appConf.RT_SECRET;
                if (!rtSecret) {
                    throw new Error('RT_SECRET is not defined in environment variables');
                }
                const staticBearerToken = appConf.STATIC_BEARER_TOKEN;
                if (!staticBearerToken) {
                    throw new Error('STATIC_BEARER_TOKEN is not defined in environment variables');
                }
                return {
                    secret: atSecret,
                    signOptions: { expiresIn: appConf.AT_TIMEOUT },
                };
            },
        }),
        DevicesModule,
        HttpModule.register({
            timeout: 10000,
        }),
    ],
    providers: [
        JwtStrategy,
        JwtAuthGuard,
        StaticTokenAuthGuard,
        AuthService,
        AuthResolver,
        PersonalTokenService,
        IdExistsConstraint,
        UniqueEmailByRoleConstraint,
        UniquePhoneNumberByRoleConstraint,
        IsUniqueConstraint,
        IsSureUniqueConstraint,
        IsUniqueByConstraint,
        UniqueWorkflowByTypeConstraint,
    ],
    exports: [
        JwtStrategy,
        JwtAuthGuard,
        StaticTokenAuthGuard,
        JwtModule,
        AuthService,
        PersonalTokenService,
        IdExistsConstraint,
        UniqueEmailByRoleConstraint,
        UniquePhoneNumberByRoleConstraint,
        IsUniqueConstraint,
        IsSureUniqueConstraint,
        UniqueWorkflowByTypeConstraint,
        HttpModule,
    ],
})
export class AuthModule {}
