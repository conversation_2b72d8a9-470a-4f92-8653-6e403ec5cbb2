import { Injectable } from '@nestjs/common';
import { PassportStrategy } from '@nestjs/passport';
import { ExtractJwt, Strategy } from 'passport-jwt';
import appConf from '../../configs/app.conf';
import { IJwtPayload } from './services/auth.service';

@Injectable()
export class JwtStrategy extends PassportStrategy(Strategy) {
    constructor() {
        super({
            jwtFromRequest: ExtractJwt.fromAuthHeaderAsBearerToken(),
            ignoreExpiration: false,
            secretOrKey: appConf.AT_SECRET,
        });
    }

    async validate(payload: IJwtPayload): Promise<IJwtPayload> {
        return payload;
    }
}
