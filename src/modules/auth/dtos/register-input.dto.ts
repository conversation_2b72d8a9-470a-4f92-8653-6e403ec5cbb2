import { IsBoolean, IsEmail, IsEnum, IsISO8601, IsNot<PERSON>mpty, IsOptional, IsString } from 'class-validator';
import { Field, InputType, Int } from '@nestjs/graphql';
import { Genders, UserEntity, UserRoles } from '../../../entities/user.entity';
import { EqualField } from '../../../commons/validators/equal-field.validator';
import { IsPassword } from '../../../commons/validators/is-password.validator';
import { userValidationMessages, validationMessagesLang } from '../../../languages/validation-messages.lang';
import { IsUserName } from '../../../commons/validators/is-user-name.validator';
import { IsUnique } from '../../../commons/validators/is-unique.validator';
import { IdExists } from '../../../commons/validators/id-exists.validator';
import { AutoSanitize, SanitizeEmail, SanitizeSafe } from '../../../commons/decorators/sanitize.decorators';
import { FileEntity } from '../../../entities/file.entity';

@InputType()
@AutoSanitize() // Tự động áp dụng SanitizeBasic cho các field không có decorator sanitize
export class RegisterInputDto {
    @Field()
    @IsString()
    @IsNotEmpty({ message: validationMessagesLang.REQUIRED })
    @IsUserName()
    @IsUnique(UserEntity)
    @SanitizeSafe() // Sanitize username: trim, escape, stripLow, toLowerCase
    user_name: string;

    @Field()
    @IsNotEmpty({ message: userValidationMessages.REQUIRED_PASSWORD })
    @IsString()
    @IsPassword()
    password: string;

    @Field()
    @IsNotEmpty({ message: userValidationMessages.REQUIRED_CONFIRM_PASSWORD })
    @EqualField('password', { message: userValidationMessages.CONFIRM_PASSWORD })
    confirm_password: string;

    @Field()
    @IsString()
    @IsNotEmpty({ message: validationMessagesLang.REQUIRED })
    full_name: string;

    @Field({ nullable: true })
    @IsOptional()
    @IsString()
    @IsUnique(UserEntity)
    phone?: string;

    @Field({ nullable: true })
    @IsOptional()
    @IsEmail({}, { message: userValidationMessages.INVALID_EMAIL })
    @IsString()
    @IsUnique(UserEntity)
    @SanitizeEmail() // Sanitize email: trim, normalizeEmail, toLowerCase
    email?: string;

    @Field({ nullable: true })
    @IsOptional()
    @IsString()
    address?: string;

    @Field(() => Int, { nullable: true })
    @IsOptional()
    @IdExists(FileEntity)
    avatar_id?: number;

    @Field(() => Int, { nullable: true })
    @IsOptional()
    @IsEnum(Genders)
    gender_id?: Genders;

    @Field(() => Int)
    @IsEnum(UserRoles)
    role_id: UserRoles;

    @Field({ nullable: true })
    @IsOptional()
    @IsString()
    telegram_id?: string;

    @Field({ nullable: true })
    @IsOptional()
    @IsString()
    @IsISO8601({})
    birthday?: string;

    @Field({ nullable: true })
    @IsOptional()
    @IsBoolean()
    email_notify?: boolean;

    @Field({ nullable: true })
    @IsOptional()
    @IsBoolean()
    telegram_notify?: boolean;

    @Field({ nullable: true })
    @IsOptional()
    @IsBoolean()
    zalo_notify?: boolean;

    @Field({ nullable: true })
    @IsOptional()
    @IsBoolean()
    email_verified?: boolean;

    @Field({ nullable: true })
    @IsOptional()
    @IsBoolean()
    phone_verified?: boolean;

    @Field({ nullable: true })
    @IsOptional()
    @IsBoolean()
    require_change_password?: boolean;
}
