import { IsNotEmpty, IsString } from 'class-validator';
import { Field, InputType } from '@nestjs/graphql';
import { AutoSanitize, SanitizeSafe } from '../../../commons/decorators/sanitize.decorators';

@InputType()
@AutoSanitize() // Tự động áp dụng SanitizeBasic cho các field không có decorator sanitize
export class LoginInputDto {
    @Field()
    @IsString()
    @IsNotEmpty()
    @SanitizeSafe() // Sanitize username: trim, escape, stripLow, toLowerCase
    user_name: string;

    @Field()
    @IsString()
    @IsNotEmpty()
    password: string;
}
