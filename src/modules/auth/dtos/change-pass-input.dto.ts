import { IsNotEmpty, IsString } from 'class-validator';
import { Field, InputType } from '@nestjs/graphql';
import { EqualField } from '../../../commons/validators/equal-field.validator';
import { userValidationMessages } from '../../../languages/validation-messages.lang';
import { IsPassword } from '../../../commons/validators/is-password.validator';
import { AutoSanitize } from '../../../commons/decorators/sanitize.decorators';

@InputType()
@AutoSanitize()
export class ChangePassInputDto {
    @Field()
    @IsString()
    @IsNotEmpty()
    old_password: string;

    @Field()
    @IsNotEmpty({ message: userValidationMessages.REQUIRED_PASSWORD })
    @IsString()
    @IsPassword()
    password: string;

    @Field()
    @IsNotEmpty({ message: userValidationMessages.REQUIRED_CONFIRM_PASSWORD })
    @EqualField('password', { message: userValidationMessages.CONFIRM_PASSWORD })
    confirm_password: string;
}
