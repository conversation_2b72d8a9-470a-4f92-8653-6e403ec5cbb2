import { IsEmail, IsEnum, <PERSON>NotEmpty, IsString } from 'class-validator';
import { Field, InputType, Int } from '@nestjs/graphql';
import { UserRoles } from '../../../entities/user.entity';
import { AutoSanitize, SanitizeEmail } from '../../../commons/decorators/sanitize.decorators';

@InputType()
@AutoSanitize() // Tự động áp dụng SanitizeBasic cho các field không có decorator sanitize
export class ForgotPasswordDto {
    @Field()
    @IsString()
    @IsNotEmpty()
    @IsEmail({})
    @SanitizeEmail() // Sanitize email: trim, normalizeEmail, toLowerCase
    email: string;

    @Field(() => Int)
    @IsEnum(UserRoles)
    role_id: UserRoles;
}
