import { Field, ObjectType, ID, GraphQLISODateTime } from '@nestjs/graphql';
import { UserRoles } from '../../../entities/user.entity';

@ObjectType()
export class TokenPayloadModel {
    @Field(() => String)
    access_token: string;

    @Field(() => String)
    refresh_token: string;
}

@ObjectType()
export class AuthPayloadModel {
    @Field(() => ID)
    id: number;

    @Field()
    full_name: string;

    @Field(() => String, { nullable: true })
    avatar?: string | null;

    @Field(() => UserRoles)
    role_id: UserRoles;

    @Field(() => GraphQLISODateTime)
    created_at: Date;

    @Field(() => TokenPayloadModel)
    token: TokenPayloadModel;

    @Field(() => Boolean)
    is_require_change_password: boolean;
}
