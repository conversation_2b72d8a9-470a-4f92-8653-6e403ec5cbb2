import { ExecutionContext, ForbiddenException, Injectable, UnauthorizedException } from '@nestjs/common';
import { GqlExecutionContext } from '@nestjs/graphql';
import { AuthGuard } from '@nestjs/passport';
import { AuthService } from './services/auth.service';
import { PersonalTokenService } from './services/personal-token.service';
import { compareToken } from '../../commons/helpers/hash-code.helper';
import { UserRoles } from '../../entities/user.entity';
import { ROLE_KEY } from '../../commons/decorators/roles.decorator';
import { Reflector } from '@nestjs/core';

@Injectable()
export class JwtAuthGuard extends AuthGuard('jwt') {
    constructor(
        private readonly authService: AuthService,
        private readonly personalTokenService: PersonalTokenService,
        private readonly reflector: Reflector
    ) {
        super();
    }

    async canActivate(context: ExecutionContext): Promise<boolean> {
        const isRest = context.getType() === 'http';
        const request = isRest
            ? context.switchToHttp().getRequest()
            : GqlExecutionContext.create(context).getContext().req;
        // Kiểm tra Authorization header
        const authHeader = request.headers.authorization;
        if (!authHeader || !authHeader.startsWith('Bearer ')) {
            throw new UnauthorizedException('Missing or invalid authorization token');
        }

        const token = authHeader.replace('Bearer ', '');
        const canActivate = await super.canActivate(context);
        if (!canActivate) {
            throw new UnauthorizedException('Invalid user authentication');
        }
        if (!request.user || !request.user.id) {
            throw new UnauthorizedException('Invalid user authentication');
        }
        const userId = Number(request.user.id);

        // Kiểm tra danh sách token hợp lệ từ DB
        const personalTokens = await this.personalTokenService.getListField('access_token', {
            user_id: userId,
            is_enable: true,
        } as any);

        let isValidToken = false;
        for (const storedToken of personalTokens) {
            if (await compareToken(token, storedToken)) {
                isValidToken = true;
                break;
            }
        }

        if (!isValidToken) {
            throw new UnauthorizedException('Invalid access token');
        }

        // Lấy thông tin user từ DB
        const user = await this.authService.findOne(userId);
        if (!user) {
            throw new UnauthorizedException('User not found');
        }

        // Check quyền truy cập
        const requiredRoles =
            this.reflector.get<UserRoles[]>(ROLE_KEY, context.getHandler()) ||
            this.reflector.get<UserRoles[]>(ROLE_KEY, context.getClass());

        if (requiredRoles && !requiredRoles.includes(user.role_id)) {
            throw new ForbiddenException();
        }

        // Gán user vào request.user
        request.user = user;
        return true;
    }

    getRequest(context: ExecutionContext) {
        const isRest = context.getType() === 'http';
        return isRest ? context.switchToHttp().getRequest() : GqlExecutionContext.create(context).getContext().req;
    }
}
