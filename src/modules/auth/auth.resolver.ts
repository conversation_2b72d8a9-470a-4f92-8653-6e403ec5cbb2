import { Args, Context, Mutation, Resolver, ResolveField, Parent } from '@nestjs/graphql';
import { AuthService } from './services/auth.service';
import { UserEntity } from '../../entities/user.entity';
import { RegisterInputDto } from './dtos/register-input.dto';
import { LoginInputDto } from './dtos/login-input.dto';
import { AuthUser } from './auth.decorator';
import { AuthPayloadModel } from './models/auth-payload.model';
import { Request } from 'express';
import { AuthMutation, AuthQuery } from '../../commons/decorators/graphql.decorators';
import { BadRequestException } from '@nestjs/common';
import { RefreshInputDto } from './dtos/refresh-input.dto';
import { ProfileInputDto } from './dtos/profile-input.dto';
import { ChangePassInputDto } from './dtos/change-pass-input.dto';
import { ForgotPasswordDto } from './dtos/forgot-password.dto';
import { ResetPasswordDto } from './dtos/reset-password.dto';
import { addMonths } from 'date-fns';
import { ProfileModel } from './models/profile.model';
import { UserDepartmentEntity } from '../../entities/user-department.entity';
import { GroupEntity } from '../../entities/group.entity';
import { PseudonymsEntity } from '../../entities/pseudonyms.entity';
import { FileEntity } from '../../entities/file.entity';

@Resolver(() => ProfileModel)
export class AuthResolver {
    constructor(private readonly authService: AuthService) {}

    @Mutation(() => AuthPayloadModel, { name: 'auth_login' })
    async login(@Args('body') body: LoginInputDto, @Context('req') req: Request): Promise<AuthPayloadModel> {
        return this.authService.login(body, req);
    }

    @AuthQuery(() => Boolean, { name: 'auth_logout' })
    async logout(@AuthUser() auth: UserEntity, @Context('req') req: Request): Promise<Boolean> {
        const token = req?.headers?.authorization?.split(' ')[1] || null;
        if (!token) throw new BadRequestException();
        await this.authService.logout(auth, token);
        return true;
    }

    @Mutation(() => AuthPayloadModel, { name: 'auth_refresh' })
    async refreshToken(@Args('body') body: RefreshInputDto): Promise<AuthPayloadModel> {
        return this.authService.refreshToken(body.refresh_token);
    }

    @Mutation(() => Boolean, { name: 'auth_register' })
    async register(@Args('body') body: RegisterInputDto): Promise<Boolean> {
        await this.authService.register(body);
        return true;
    }

    @AuthQuery(() => ProfileModel, { name: 'auth_profile' })
    async profile(@AuthUser() auth: UserEntity) {
        const now = new Date();
        const threeMonthsFromChangePassword = auth.change_password_at ? addMonths(auth.change_password_at, 1) : null;
        const isRequireChangePassword =
            !auth.change_password_at ||
            (threeMonthsFromChangePassword !== null && threeMonthsFromChangePassword <= now);
        return { ...auth, is_require_change_password: isRequireChangePassword };
    }

    @ResolveField(() => [UserDepartmentEntity], { nullable: true })
    async userDepartments(@Parent() user: UserEntity) {
        const userWithDepts = await this.authService.findOne({
            where: { id: user.id },
            relations: ['userDepartments', 'userDepartments.department'],
        });
        return userWithDepts?.userDepartments;
    }

    @ResolveField(() => [GroupEntity], { nullable: true })
    async groups(@Parent() user: UserEntity) {
        const userWithGroups = await this.authService.findOne({
            where: { id: user.id },
            relations: ['groups'],
        });
        return userWithGroups?.groups;
    }

    @ResolveField(() => [PseudonymsEntity], { nullable: true })
    async pseudonyms(@Parent() user: UserEntity) {
        const userWithPseudonyms = await this.authService.findOne({
            where: { id: user.id },
            relations: ['pseudonyms'],
        });
        return userWithPseudonyms?.pseudonyms;
    }

    @ResolveField(() => FileEntity, { nullable: true })
    async avatar(@Parent() user: UserEntity) {
        const userWithAvatar = await this.authService.findOne({
            where: { id: user.id },
            relations: ['avatar'],
        });
        return userWithAvatar?.avatar;
    }

    @AuthMutation(() => UserEntity, { name: 'auth_update' })
    async update(@AuthUser() auth: UserEntity, @Args('body') body: ProfileInputDto): Promise<UserEntity> {
        return this.authService.updateOne(auth.id, { ...body, updated_by: auth.id });
    }

    @AuthMutation(() => Boolean, { name: 'auth_change_pass' })
    async changePass(@AuthUser() auth: UserEntity, @Args('body') body: ChangePassInputDto): Promise<boolean> {
        await this.authService.changePassword(body, auth);
        return true;
    }

    @Mutation(() => String, { name: 'auth_forgot_pass' })
    async forgotPass(@Args('body') body: ForgotPasswordDto): Promise<string> {
        return this.authService.forgotPassword(body);
    }

    @Mutation(() => Boolean, { name: 'auth_reset_pass' })
    async resetPass(@Args('body') body: ResetPasswordDto): Promise<boolean> {
        await this.authService.resetPassword(body);
        return true;
    }
}
