import { Injectable } from '@nestjs/common';
import { Repository } from 'typeorm';
import { InjectRepository } from '@nestjs/typeorm';
import { BaseService } from '../../../commons/bases/base.service';
import { PersonalTokenEntity } from '../../../entities/personal-token.entity';

@Injectable()
export class PersonalTokenService extends BaseService<PersonalTokenEntity> {
    constructor(
        @InjectRepository(PersonalTokenEntity)
        private readonly repo: Repository<PersonalTokenEntity>
    ) {
        super(repo);
    }
}
