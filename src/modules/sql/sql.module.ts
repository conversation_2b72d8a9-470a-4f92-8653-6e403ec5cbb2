import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { PortletEntity } from '../../entities/portlet.entity';
import { SqlValidationService } from './services/sql-validation.service';
import { SqlExecutionService } from './services/sql-execution.service';
import { PortletSqlService } from './services/portlet-sql.service';
import { SqlResolver } from './sql.resolver';

@Module({
    imports: [TypeOrmModule.forFeature([PortletEntity])],
    providers: [SqlValidationService, SqlExecutionService, PortletSqlService, SqlResolver],
    exports: [SqlValidationService, SqlExecutionService, PortletSqlService],
})
export class SqlModule {}
