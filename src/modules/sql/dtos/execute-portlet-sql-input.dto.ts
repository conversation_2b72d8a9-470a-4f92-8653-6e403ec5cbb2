import { Field, InputType, Int } from '@nestjs/graphql';
import GraphQLJSON from 'graphql-type-json';
import { IsNotEmpty, IsNumber, IsOptional, IsString } from 'class-validator';
import { DepartmentEntity } from '../../../entities/department.entity';
import { IdExists } from '../../../commons/validators/id-exists.validator';

@InputType()
export class ExecutePortletSqlInputDto {
    @Field()
    @IsNotEmpty({ message: 'Portlet code is required' })
    @IsString({ message: 'Portlet code must be a string' })
    code: string;

    @Field(() => Int)
    @IsNotEmpty({ message: 'Department ID is required' })
    @IsNumber({}, { message: 'Department ID must be a number' })
    @IdExists(DepartmentEntity)
    department_id: number;

    @Field(() => GraphQLJSON, { nullable: true })
    @IsOptional()
    parameters?: Record<string, any>;
}
