import { Field, InputType } from '@nestjs/graphql';
import GraphQLJSON from 'graphql-type-json';
import { IsNotEmpty, IsOptional, IsString } from 'class-validator';

@InputType()
export class ExecuteSqlInputDto {
    @Field()
    @IsNotEmpty({ message: 'SQL query is required' })
    @IsString({ message: 'SQL query must be a string' })
    sql: string;

    @Field(() => GraphQLJSON, { nullable: true })
    @IsOptional()
    parameters?: Record<string, any>;
}
