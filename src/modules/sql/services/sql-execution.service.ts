import { BadRequestException, Injectable, InternalServerErrorException } from '@nestjs/common';
import { DataSource } from 'typeorm';
import { SqlValidationService } from './sql-validation.service';

@Injectable()
export class SqlExecutionService {
    constructor(
        private readonly dataSource: DataSource,
        private readonly sqlValidationService: SqlValidationService
    ) {}

    /**
     * Executes a SQL query after validating it
     * @param sql The SQL query to execute
     * @param parameters Optional parameters for the SQL query
     * @returns The query results
     */
    async executeSql(sql: string | undefined, parameters?: Record<string, any>): Promise<any> {
        if (!sql) {
            throw new BadRequestException('SQL query is null');
        }

        try {
            let finalSql = sql;
            let queryParams: any[] = [];

            // Process parameters if provided
            if (parameters && Object.keys(parameters).length > 0) {
                const processedSql = this.processParameterizedSql(sql, parameters);
                finalSql = processedSql.sql;
                queryParams = processedSql.params;
                console.log('Original SQL:', sql);
                console.log('Processed SQL:', finalSql);
                console.log('Parameters:', queryParams);
            }

            // Validate the final SQL query (after parameter processing)
            const validationResult = this.sqlValidationService.validateSql(finalSql);
            if (!validationResult.isValid) {
                throw new BadRequestException(validationResult.error);
            }

            // Execute the SQL query
            if (queryParams.length > 0) {
                return this.dataSource.query(finalSql, queryParams);
            } else {
                return this.dataSource.query(finalSql);
            }
        } catch (error) {
            console.error('SQL Execution Error:', error);
            console.error('Original SQL:', sql);
            console.error('Parameters:', parameters);
            throw new InternalServerErrorException(`Error executing SQL query: ${error.message}`);
        }
    }

    /**
     * Processes parameterized SQL by replacing named parameters with positional parameters
     * @param sql The SQL query with named parameters (e.g., :paramName)
     * @param parameters The parameter values
     * @returns Processed SQL and parameter array
     */
    private processParameterizedSql(sql: string, parameters: Record<string, any>): { sql: string; params: any[] } {
        const params: any[] = [];
        let processedSql = sql.trim();
        let parameterIndex = 1;

        // Sort parameters by key length (descending) to avoid partial replacements
        const sortedParams = Object.entries(parameters).sort(([a], [b]) => b.length - a.length);

        for (const [key, value] of sortedParams) {
            // Create pattern to match :paramName with optional whitespace
            const paramPattern = new RegExp(`:\\s*${key}\\b`, 'g');

            // Check if this parameter exists in the SQL
            if (paramPattern.test(processedSql)) {
                // Reset the regex for replacement
                const replacePattern = new RegExp(`:\\s*${key}\\b`, 'g');

                // Replace with PostgreSQL parameter syntax ($1, $2, etc.)
                processedSql = processedSql.replace(replacePattern, `$${parameterIndex}`);

                // Add the parameter value to the array
                params.push(value);
                parameterIndex++;
            }
        }

        // Clean up any extra whitespace
        processedSql = processedSql.replace(/\s+/g, ' ').trim();

        return { sql: processedSql, params };
    }
}
