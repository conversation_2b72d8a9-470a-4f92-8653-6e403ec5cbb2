import { Injectable, Logger } from '@nestjs/common';

@Injectable()
export class SqlValidationService {
    private readonly logger = new Logger(SqlValidationService.name);
    private readonly MAX_QUERY_LENGTH = 2048;
    /**
     * Validates if the SQL query is safe to execute
     * Only allows SELECT statements, no CUD operations or DROP TABLE/DATABASE
     * @param sql The SQL query to validate
     * @returns An object with validation result and error message if any
     */
    validateSql(sql: string): { isValid: boolean; error?: string } {
        if (!sql) {
            const error = 'SQL query must be a non-empty string';
            this.logBlockedQuery(error);
            return { isValid: false, error };
        }

        // Check query length
        if (sql.length > this.MAX_QUERY_LENGTH) {
            const error = `SQL query exceeds maximum length of ${this.MAX_QUERY_LENGTH} characters`;
            this.logBlockedQuery(error);
            return { isValid: false, error };
        }

        // Check for suspicious escape sequences
        if (this.containsSuspiciousEscapeSequences(sql)) {
            const error = 'Contains suspicious escape sequences';
            this.logBlockedQuery(error);
            return { isValid: false, error };
        }

        // Check for obfuscated dangerous functions in the original SQL
        if (this.containsObfuscatedFunctions(sql)) {
            const error = 'Contains obfuscated dangerous functions';
            this.logBlockedQuery(error);
            return { isValid: false, error };
        }

        // Remove all comments to prevent comment-based bypasses
        // Remove both inline (--) and block (/* */) comments
        let cleanSql = this.removeComments(sql);

        // Normalize whitespace to prevent whitespace-based bypasses
        cleanSql = this.normalizeWhitespace(cleanSql);

        // Trim and normalize the SQL query
        const normalizedSql = cleanSql.trim().toLowerCase();
        console.log(`🔍 SQL VALIDATION: Original: ${sql}`);
        console.log(`🔍 SQL VALIDATION: Normalized: ${normalizedSql}`);

        // Check for Unicode homoglyphs
        const normalizedForHomoglyphs = this.normalizeUnicodeHomoglyphs(normalizedSql);
        if (normalizedForHomoglyphs !== normalizedSql) {
            const error = 'Unicode homoglyphs are not allowed';
            this.logBlockedQuery(error);
            return { isValid: false, error };
        }

        // Check for ASCII char() based injection
        if (this.containsCharBasedInjection(normalizedSql)) {
            const error = 'char() based obfuscation is not allowed';
            this.logBlockedQuery(error);
            return { isValid: false, error };
        }

        // Check if the query is a SELECT statement
        if (!this.isSelectStatement(normalizedSql)) {
            const error = 'Only SELECT statements are allowed';
            this.logBlockedQuery(error);
            return { isValid: false, error };
        }

        // Check for always true conditions in WHERE clause
        const alwaysTrueResult = this.containsAlwaysTrueCondition(normalizedSql);
        if (alwaysTrueResult.isVulnerable) {
            const error = `Potentially unsafe WHERE clause: ${alwaysTrueResult.reason}`;
            this.logBlockedQuery(error);
            return { isValid: false, error };
        }

        // Check for subqueries in SELECT
        if (this.containsSubqueries(normalizedSql)) {
            const error = 'Subqueries are not allowed';
            this.logBlockedQuery(error);
            return { isValid: false, error };
        }

        // Check for disallowed operations and keywords
        if (this.containsDisallowedOperations(normalizedSql)) {
            const error = 'Contains disallowed SQL operations';
            this.logBlockedQuery(error);
            return { isValid: false, error };
        }

        // Check for metadata leaks
        if (this.containsMetadataLeaks(normalizedSql)) {
            const error = 'Metadata access is not allowed';
            this.logBlockedQuery(error);
            return { isValid: false, error };
        }

        // Check for nested parentheses
        const parenthesesResult = this.checkParentheses(normalizedSql);
        if (!parenthesesResult.isValid) {
            this.logBlockedQuery(parenthesesResult.error || 'Parentheses validation failed');
            return parenthesesResult;
        }

        // Check for multiple statements (separated by semicolons)
        if (this.containsMultipleStatements(normalizedSql)) {
            const error = 'Multiple SQL statements are not allowed';
            this.logBlockedQuery(error);
            return { isValid: false, error };
        }

        // Check for potentially dangerous SQL functions
        if (this.containsDangerousFunctions(normalizedSql)) {
            console.log(`🚨 SQL VALIDATION: Dangerous functions detected in: ${normalizedSql}`);
            const error = 'Contains potentially dangerous SQL functions';
            this.logBlockedQuery(error);
            return { isValid: false, error };
        }

        // Check for UNION-based attacks
        if (this.containsUnionAttack(normalizedSql)) {
            const error = 'UNION-based attacks are not allowed';
            this.logBlockedQuery(error);
            return { isValid: false, error };
        }

        return { isValid: true };
    }

    /**
     * Removes SQL comments from a query
     * @param sql The SQL query
     * @returns SQL query without comments
     */
    private removeComments(sql: string): string {
        // Preserve Oracle and MySQL optimizer hints for detection
        const oracleHints: string[] = [];
        const mysqlHints: string[] = [];

        // Capture Oracle hints: /*+ ... */
        let result = sql.replace(/\/\*\+([\s\S]*?)\*\//g, (match, hint) => {
            oracleHints.push(hint.trim());
            return ` __ORACLE_HINT_${oracleHints.length - 1}__ `;
        });

        // Capture MySQL hints: /*! ... */
        result = result.replace(/\/\*!(\d*?)([\s\S]*?)\*\//g, (match, version, hint) => {
            mysqlHints.push(hint.trim());
            return ` __MYSQL_HINT_${mysqlHints.length - 1}__ `;
        });

        // Remove other comments
        result = result.replace(/\/\*[\s\S]*?\*\//g, ' '); // Remove multi-line comments
        result = result.replace(/--.*$/gm, ' '); // Remove single-line comments

        // Restore hints for detection
        oracleHints.forEach((hint, index) => {
            result = result.replace(`__ORACLE_HINT_${index}__`, `/*+ ${hint} */`);
        });

        mysqlHints.forEach((hint, index) => {
            result = result.replace(`__MYSQL_HINT_${index}__`, `/*! ${hint} */`);
        });

        return result;
    }

    /**
     * Normalizes whitespace in a SQL query
     * @param sql The SQL query
     * @returns SQL query with normalized whitespace
     */
    private normalizeWhitespace(sql: string): string {
        // Replace tabs, newlines, and multiple spaces with a single space
        return sql.replace(/\s+/g, ' ');
    }

    /**
     * Checks if the query is a valid SELECT statement
     * @param sql The normalized SQL query
     * @returns True if the query is a valid SELECT statement
     */
    private isSelectStatement(sql: string): boolean {
        // Check if the query starts with SELECT
        return /^\s*select\s+/i.test(sql);
    }

    /**
     * Checks if the query contains disallowed operations
     * @param sql The normalized SQL query
     * @returns True if the query contains disallowed operations
     */
    private containsDisallowedOperations(sql: string): boolean {
        const disallowedOperations = [
            'insert',
            'update',
            'delete',
            'drop',
            'truncate',
            'alter',
            'create',
            'grant',
            'revoke',
            'exec',
            'execute',
            'xp_',
            'sp_',
            'syscolumns',
            'information_schema',
            'into outfile',
            'into dumpfile',
            'load_file',
            'benchmark',
            'sleep',
            'waitfor delay',
            'pg_sleep',
            'declare', // Thêm mới - SQL Server variable declaration
            'begin', // Thêm mới - SQL Server/Oracle block
            'end', // Thêm mới - SQL Server/Oracle block
            'cursor', // Thêm mới - SQL Server/Oracle cursor
            'fetch', // Thêm mới - SQL Server/Oracle cursor
            'open', // Thêm mới - SQL Server/Oracle cursor
            'close', // Thêm mới - SQL Server/Oracle cursor
            'deallocate', // Thêm mới - SQL Server cursor
            'prepare', // Thêm mới - MySQL prepared statement
            'handler', // Thêm mới - MySQL handler
            'analyze', // Thêm mới - MySQL/PostgreSQL
            'explain', // Thêm mới - MySQL/PostgreSQL
            'show', // Thêm mới - MySQL/PostgreSQL
            'describe', // Thêm mới - MySQL/PostgreSQL
            'call', // Thêm mới - MySQL/PostgreSQL stored procedure
            'merge', // Thêm mới - SQL Server/Oracle
            'upsert', // Thêm mới - PostgreSQL
            'copy', // Thêm mới - PostgreSQL
            'vacuum', // Thêm mới - PostgreSQL
            'lock', // Thêm mới - MySQL/PostgreSQL
            'unlock', // Thêm mới - MySQL
            'savepoint', // Thêm mới - Transaction control
            'rollback', // Thêm mới - Transaction control
            'commit', // Thêm mới - Transaction control
            'start transaction', // Thêm mới - Transaction control
            'begin transaction', // Thêm mới - Transaction control
            'set transaction', // Thêm mới - Transaction control
            'regexp', // Thêm mới - Regular expression
            'rlike', // Thêm mới - MySQL regular expression
            'regexp_like', // Thêm mới - Oracle regular expression
            'partition by', // Thêm mới - Window function
            'window', // Thêm mới - Window function
            'with', // Thêm mới - Common Table Expression (CTE)
            'recursive', // Thêm mới - Recursive CTE
            'pivot', // Thêm mới - SQL Server/Oracle PIVOT
            'unpivot', // Thêm mới - SQL Server/Oracle UNPIVOT
            'connect by', // Thêm mới - Oracle hierarchical queries
            'prior', // Thêm mới - Oracle hierarchical queries
            'start with', // Thêm mới - Oracle hierarchical queries
            '$where', // Thêm mới - NoSQL injection
            '$ne', // Thêm mới - NoSQL injection
            '$gt', // Thêm mới - NoSQL injection
            '$lt', // Thêm mới - NoSQL injection
            '$eq', // Thêm mới - NoSQL injection
            '$in', // Thêm mới - NoSQL injection
            '$nin', // Thêm mới - NoSQL injection
            '$or', // Thêm mới - NoSQL injection
            '$and', // Thêm mới - NoSQL injection
            '$not', // Thêm mới - NoSQL injection
            '$nor', // Thêm mới - NoSQL injection
            '$exists', // Thêm mới - NoSQL injection
            '$type', // Thêm mới - NoSQL injection
            '__schema', // Thêm mới - GraphQL injection
            '__type', // Thêm mới - GraphQL injection
            '@include', // Thêm mới - GraphQL directive
            '@skip', // Thêm mới - GraphQL directive
            '@deprecated', // Thêm mới - GraphQL directive
            '@specifiedBy', // Thêm mới - GraphQL directive
            'lateral', // Thêm mới - Lateral join
            'cross apply', // Thêm mới - SQL Server lateral join
            'outer apply', // Thêm mới - SQL Server lateral join
            'having', // Thêm mới - Having clause
            'similar to', // Thêm mới - PostgreSQL pattern matching
            'reconfigure', // Thêm mới - SQL Server configuration
            'xp_cmdshell', // Thêm mới - SQL Server command shell
            'lines terminated by', // Thêm mới - MySQL export
            'fields terminated by', // Thêm mới - MySQL export
            'outfile', // Thêm mới - MySQL export
            'dumpfile', // Thêm mới - MySQL export
            'group by', // Thêm mới - Group by clause
        ];

        // Use word boundaries to ensure we're matching whole words
        // and check if they appear outside of string literals
        for (const operation of disallowedOperations) {
            const regex = new RegExp(`\\b${operation}\\b`, 'i');
            if (this.patternAppearsOutsideStrings(sql, regex)) {
                return true;
            }
        }

        return false;
    }

    /**
     * Checks if the query contains potentially dangerous SQL functions
     * @param sql The normalized SQL query
     * @returns True if the query contains potentially dangerous SQL functions
     */
    private containsDangerousFunctions(sql: string): boolean {
        // Safe aggregate functions that should be allowed
        const safeAggregateFunctions = ['count', 'sum', 'avg', 'min', 'max', 'stddev', 'variance'];

        // Check if the SQL only contains safe aggregate functions
        const containsOnlySafeAggregates = safeAggregateFunctions.some((safeFunc) => {
            const safeRegex = new RegExp(`\\b${safeFunc}\\s*\\(`, 'i');
            return safeRegex.test(sql);
        });

        // If we have safe aggregates, allow them to pass
        if (containsOnlySafeAggregates) {
            // Still check for other dangerous patterns, but exclude the safe aggregates
            const sqlWithoutSafeAggregates = sql;
            for (const safeFunc of safeAggregateFunctions) {
                const safeRegex = new RegExp(`\\b${safeFunc}\\s*\\([^)]*\\)`, 'gi');
                // Don't actually remove them, just note that they're safe
            }
        }

        const dangerousFunctions = [
            // User identification functions
            'system_user',
            'current_user',
            'session_user',
            'user',

            // System information functions
            'database',
            'version',
            'schema',
            'pg_read_file',
            'pg_ls_dir',
            'pg_stat_file',

            // Data manipulation functions
            'convert',
            'cast',
            'load_file',
            'hex',
            'unhex',
            'decode',
            'encode',
            'char',
            'ascii',
            'substring',
            'substr',
            'mid',
            'concat',
            'concat_ws',
            'group_concat',
            'json_extract',
            'json_value',
            'to_char',
            'updatexml', // Thêm mới - XML injection
            'extractvalue', // Thêm mới - XML injection
            'json_keys', // Thêm mới - JSON injection
            'json_array', // Thêm mới - JSON injection
            'json_object', // Thêm mới - JSON injection
            'json_merge', // Thêm mới - JSON injection

            // MySQL specific functions
            'benchmark',
            'connection_id',
            'current_date',
            'current_time',
            'current_timestamp',
            'found_rows',
            'last_insert_id',
            'row_count',

            // PostgreSQL specific functions
            'current_database',
            'current_schema',
            'current_setting',
            'pg_backend_pid',
            'pg_postmaster_start_time',
            'pg_conf_load_time',

            // Time-based attack functions
            'sleep',
            'pg_sleep',
            'wait',
            'waitfor',
            'delay',
            'generate_series',
            'dbms_pipe.receive_message',
            'dbms_lock.sleep',
            'rlike',
            'regexp',
            'like',

            // Boolean-based attack functions
            'if',
            'ifnull',
            'nullif',
            'coalesce',
            'case',
            'make_set',
            'elt',

            // File system access functions
            'load_file',
            'outfile',
            'dumpfile',
            'file_priv',
            'infile',

            // Network functions
            'inet_aton',
            'inet_ntoa',
            'inet6_aton',
            'inet6_ntoa',
            'host',

            // Encryption and hashing functions that could be used for data exfiltration
            'md5',
            'sha1',
            'sha2',
            'aes_encrypt',
            'des_encrypt',
            'encrypt',
            'compress',
            'uncompress',
            'password',
            'old_password',

            // SQL Server specific functions
            'xp_cmdshell',
            'sp_execute',
            'sp_executesql',
            'sp_sqlexec',
            'openrowset',
            'openquery',
            'opendatasource',
            'master.dbo.xp_dirtree',
            'master.dbo.xp_fileexist',
            'master.dbo.xp_subdirs',
            'master.dbo.sp_who',
            'master.dbo.sp_who2',

            // Oracle specific functions
            'dbms_java.set_output',
            'dbms_java.grant_permission',
            'dbms_java.grant_policy_permission',
            'dbms_java.disable_output',
            'dbms_java.enable_output',
            'dbms_java.remove_output',
            'dbms_sql.parse',
            'dbms_sql.execute',
            'dbms_xmlquery.newcontext',
            'dbms_xmlquery.execute',
            'utl_http.request',
            'utl_http.begin_request',
            'utl_file.put_line',
            'utl_file.fopen',
            'utl_file.fremove',
            'utl_tcp.open_connection',
            'utl_smtp.mail',

            // Window functions
            'row_number',
            'rank',
            'dense_rank',
            'percent_rank',
            'cume_dist',
            'ntile',
            'lag',
            'lead',
            'first_value',
            'last_value',
            'nth_value',

            // PIVOT/UNPIVOT functions
            'pivot',
            'unpivot',

            // Oracle hierarchical queries
            'connect_by',
            'prior',
            'start_with',

            // GraphQL introspection
            '__schema',
            '__type',

            // GraphQL directives
            'include',
            'skip',
            'deprecated',
            'specifiedby',

            // Bitwise operators
            'bit_and',
            'bit_or',
            'bit_xor',
            'bit_not',
            'bit_count',
            'bit_length',

            // Mathematical functions
            'abs',
            'floor',
            'ceiling',
            'ceil',
            'round',
            'truncate',
            'sign',
            'power',
            'sqrt',
            'exp',
            'log',
            'log10',
            'log2',

            // Lateral joins
            'lateral',
            'cross_apply',
            'outer_apply',

            // Having clause
            'having',

            // Similar to
            'similar_to',

            // SQL Server stored procedures
            'sp_configure',
            'sp_executesql',
            'xp_cmdshell',
            'xp_dirtree',
            'xp_fileexist',
            'xp_subdirs',

            // File operations
            'outfile',
            'dumpfile',
            'copy_to',
            'copy_from',
        ];

        // Check for dangerous functions with word boundaries
        for (const func of dangerousFunctions) {
            const regex = new RegExp(`\\b${func}\\s*\\(`, 'i');
            if (regex.test(sql)) {
                console.log(`🔍 Checking function: "${func}" - matches: ${regex.test(sql)}`);

                // Skip if this is a safe aggregate function
                const isSafeAggregate = safeAggregateFunctions.some(
                    (safeFunc) => func.toLowerCase() === safeFunc.toLowerCase()
                );

                console.log(`🔍 Function "${func}" is safe aggregate: ${isSafeAggregate}`);

                if (!isSafeAggregate) {
                    console.log(`🚨 Dangerous function detected: "${func}" in SQL: ${sql}`);
                    return true;
                } else {
                    console.log(`✅ Safe aggregate function allowed: "${func}"`);
                }
            }
        }

        // Check for specific patterns related to time-based and boolean-based attacks
        const dangerousPatterns = [
            // Time-based attack patterns
            /\bwaitfor\s+delay\b/i,
            /\bbenchmark\s*\(\s*\d+\s*,/i,
            /\bsleep\s*\(\s*\d+\s*\)/i,
            /\bpg_sleep\s*\(\s*\d+\s*\)/i,

            // Boolean-based attack patterns
            /\bcase\s+when\b/i,
            /\bif\s*\(\s*.*\s*,\s*.*\s*,\s*.*\s*\)/i,
            /\bselect\s+if\s*\(/i,

            // Regular expression patterns
            /\bregexp\s+/i,
            /\brlike\s+/i,
            /\bregexp_like\s*\(/i,

            // Window function patterns (removed over() as it's a legitimate SQL feature)
            /\bpartition\s+by\b/i,
            /\bwindow\b/i,

            // PIVOT/UNPIVOT patterns
            /\bpivot\s*\(/i,
            /\bunpivot\s*\(/i,

            // Oracle hierarchical query patterns
            /\bconnect\s+by\b/i,
            /\bstart\s+with\b/i,
            /\bprior\b/i,

            // NoSQL injection patterns
            /\$where\s*=/i,
            /\$ne\s+/i,
            /\$gt\s+/i,
            /\$lt\s+/i,
            /\$eq\s+/i,
            /\$in\s*\(/i,
            /\$nin\s*\(/i,
            /\$or\s*:/i,
            /\$and\s*:/i,
            /\$not\s*:/i,
            /\$nor\s*:/i,
            /\$exists\s*:/i,
            /\$type\s*:/i,

            // GraphQL injection patterns
            /\b__schema\b/i,
            /\b__type\b/i,
            /@include\s*\(/i,
            /@skip\s*\(/i,
            /@deprecated\s*\(/i,
            /@specifiedBy\s*\(/i,

            // Bitwise operator patterns
            /\|\s*\d+/i, // OR bitwise
            /&\s*-?\d+/i, // AND bitwise
            /\^\s*\d+/i, // XOR bitwise
            /~\s*\d+/i, // NOT bitwise

            // Mathematical function patterns
            /\babs\s*\(/i,
            /\bfloor\s*\(/i,
            /\bceiling\s*\(/i,
            /\bceil\s*\(/i,
            /\bround\s*\(/i,

            // SQL Smuggling patterns
            /\/\*!\d+/i, // MySQL version comment
            /\/\*\+/i, // Oracle/PostgreSQL optimizer hint

            // SQL dialect specific patterns
            /::(?:int|text|bool)/i, // PostgreSQL type casting
            /x'[0-9a-f]+'/i, // MySQL hex string

            // CHR/CHAR function patterns
            /\bchr\s*\(\s*\d+\s*\)\s*[\|\+]/i, // CHR concatenation
            /\bchar\s*\(\s*\d+\s*\)\s*[\+]/i, // CHAR concatenation

            // Second-order injection patterns
            /['"][^'"]*;\s*(?:drop|delete|update|insert|alter)/i,

            // Transaction control patterns
            /\bbegin\s*;/i,
            /\brollback\s*;/i,
            /\bcommit\s*;/i,

            // UDF patterns
            /\bcreate\s+function/i,
            /\bdrop\s+function/i,

            // Schema poisoning patterns
            /\balter\s+table/i,
            /\bcreate\s+trigger/i,

            // Error-based blind injection patterns
            /\bcase\s+when.*?\s+then\s+1\/0/i,
            /\bcase\s+when.*?\s+then\s+cast/i,

            // Stored procedure patterns
            /\bexec\s+sp_/i,
            /\bexecute\s+sp_/i,
            /\bexec\s+xp_/i,
            /\bexecute\s+xp_/i,

            // Parameter tampering patterns
            /\b@\w+\s*=/i,

            // Lateral join patterns
            /\blateral\s*\(/i,
            /\bcross\s+apply/i,
            /\bouter\s+apply/i,

            // Having patterns
            /\bhaving\s+1\s*=\s*1/i,
            /\bhaving.*?\s+or\s+/i,

            // Similar to patterns
            /\bsimilar\s+to\s+/i,

            // Comment hint patterns
            /\/\*\+\s*\w+\s*\(/i,

            // Identifier quoting patterns
            /"[^"]*;[^"]*"/i,
            /\[[^\]]*;[^\]]*\]/i,

            // File import/export patterns
            /\binto\s+outfile\s+/i,
            /\binto\s+dumpfile\s+/i,
            /\bcopy\s*\(.*?\)\s*to\s+/i,

            // Tautology-Based SQLi patterns
            /\bwhere\s+[^=<>!]*?\s*=\s*[^=<>!]*?\s+or\s+['"]?\w+['"]?\s*=\s*['"]?\w+['"]?/i, // WHERE x = y OR 'a'='a'
            /\bwhere\s+[^=<>!]*?\s*=\s*[^=<>!]*?\s+or\s+\d+\s*=\s*\d+/i, // WHERE x = y OR 1=1
            /\bwhere\s+[^=<>!]*?\s*=\s*[^=<>!]*?\s+or\s+true\b/i, // WHERE x = y OR true

            // Error-Based SQLi patterns
            /\bconvert\s*\(\s*int\s*,\s*\(/i, // CONVERT(int, (SELECT ...))
            /\bcast\s*\(\s*\(.*?\)\s+as\s+/i, // CAST((SELECT ...) AS ...)
            /\berror_reporting\s*\(/i, // error_reporting()
            /\bdbms_pipe\.receive_message/i, // DBMS_PIPE.RECEIVE_MESSAGE

            // Blind SQLi patterns (additional)
            /\bif\s*\(\s*\(select\s+/i, // IF((SELECT ...)
            /\bselect\s+case\s+when\s+\(/i, // SELECT CASE WHEN (...)
            /\bselect\s+decode\s*\(/i, // SELECT DECODE(...)
            /\bselect\s+elt\s*\(/i, // SELECT ELT(...)

            // Stacked Queries (additional)
            /['"]\s*;\s*\w+/i, // '; DELETE
            /['"]\s*;\s*exec\s+/i, // '; EXEC
            /['"]\s*;\s*declare\s+/i, // '; DECLARE

            // Obfuscation / Encoding (additional)
            /\%(?:[0-9A-F]{2})+/i, // URL encoding
            /\\u(?:[0-9A-F]{4})+/i, // Unicode escape
            /&#(?:\d+);/i, // HTML entity (decimal)
            /&#x(?:[0-9A-F]+);/i, // HTML entity (hex)

            // Additional blind SQLi patterns
            /\band\s+\d+\s*=\s*\d+\b/i, // AND 1=1 or AND 1=2

            // Special whitespace characters
            /[\t\v\f\u00A0\u1680\u2000-\u200A\u2028\u2029\u202F\u205F\u3000\u200B]/, // Tab, vertical tab, form feed, zero-width space, etc.

            // Overlong UTF-8 and double encoding
            /\\x[0-9A-F]{2}\\x[0-9A-F]{2}/i, // Overlong UTF-8 encoding
            /%25(?:[0-9A-F]{2})+/i, // Double URL encoding

            // Nested subqueries
            /\([^()]*\([^()]*\([^()]*\)[^()]*\)[^()]*\)/i, // Triple nested parentheses

            // Special identifier quoting
            /`[^`]*;[^`]*`/i, // Backtick quoted identifiers with semicolon

            // Database specific patterns
            /\$\$.*?\$\$/i, // PostgreSQL dollar-quoted string
            /@\w+\s*=\s*/i, // SQL Server T-SQL variable
            /\bDUAL\b/i, // Oracle/MySQL DUAL table
            /\bSTART\s+WITH\b/i, // Oracle hierarchical query
        ];

        for (const pattern of dangerousPatterns) {
            if (pattern.test(sql)) {
                return true;
            }
        }

        return false;
    }

    /**
     * Checks if the query contains a UNION-based attack
     * @param sql The normalized SQL query
     * @returns True if the query contains a UNION-based attack
     */
    private containsUnionAttack(sql: string): boolean {
        // Kiểm tra kỹ hơn với các mẫu UNION khác nhau
        const unionPatterns = [
            /\bunion\s+(all\s+)?select\b/i,
            /\bunion\s*[\s\/*]+\s*(all\s*[\s\/*]+\s*)?select\b/i,
            /\bu\s*n\s*i\s*o\s*n\s+/i, // Phát hiện chữ cái UNION bị tách ra
            /\bu[\s\/*]*n[\s\/*]*i[\s\/*]*o[\s\/*]*n/i, // Phát hiện UNION với comment hoặc khoảng trắng giữa các ký tự
            /\bｕｎｉｏｎ\s/i, // Phát hiện UNION với ký tự Unicode full-width
            /\b(u|%75)(n|%6e)(i|%69)(o|%6f)(n|%6e)\s/i, // Phát hiện UNION với URL encoding
            /\b(u|\\u0075)(n|\\u006e)(i|\\u0069)(o|\\u006f)(n|\\u006e)\s/i, // Phát hiện UNION với Unicode escape
            /\bchar\(\s*\d+\s*\)\s*\+\s*char\(\s*\d+\s*\)/i, // Phát hiện UNION với char() function
            /\b(0x55|0x75)(0x4e|0x6e)(0x49|0x69)(0x4f|0x6f)(0x4e|0x6e)/i, // Phát hiện UNION với hex encoding
        ];

        for (const pattern of unionPatterns) {
            if (pattern.test(sql)) {
                return true;
            }
        }

        return false;
    }

    /**
     * Checks if the query contains char() based injection
     * @param sql The normalized SQL query
     * @returns True if the query contains char() based injection
     */
    private containsCharBasedInjection(sql: string): boolean {
        // Check for ASCII char() based injection (example: char(115)+char(101)+...)
        return /char\s*\(\s*\d+\s*\)/i.test(sql);
    }

    /**
     * Checks if the query contains subqueries
     * @param sql The normalized SQL query
     * @returns True if the query contains subqueries
     */
    private containsSubqueries(sql: string): boolean {
        // Check for various forms of subqueries
        const subqueryPatterns = [
            // SELECT with subquery in parentheses
            /\bselect\s+\(.*select.*\)/i,

            // FROM clause with subquery
            /\bfrom\s+\(\s*select\b/i,

            // WHERE clause with subquery
            /\bwhere\s+.*\(\s*select\b/i,

            // IN operator with subquery
            /\bin\s*\(\s*select\b/i,

            // EXISTS operator with subquery
            /\bexists\s*\(\s*select\b/i,

            // ANY, SOME, or ALL operators with subquery
            /\b(any|some|all)\s*\(\s*select\b/i,

            // Comparison operators with subquery
            /[=<>!]\s*\(\s*select\b/i,

            // JOIN with subquery
            /\bjoin\s+\(\s*select\b/i,

            // Nested SELECT statements
            /\bselect\b.*\bselect\b/i,
        ];

        // Check each pattern
        for (const pattern of subqueryPatterns) {
            if (pattern.test(sql)) {
                return true;
            }
        }

        return false;
    }

    /**
     * Checks if the query contains metadata leaks
     * @param sql The normalized SQL query
     * @returns True if the query contains metadata leaks
     */
    private containsMetadataLeaks(sql: string): boolean {
        const metadataLeaks = [
            'information_schema.columns',
            'information_schema.tables',
            'information_schema.schemata',
            'information_schema.routines',
            'information_schema.key_column_usage',
            'information_schema.table_constraints',
            'information_schema.views',
            'information_schema.triggers',
            'pg_catalog.pg_tables',
            'pg_catalog.pg_user',
            'pg_catalog.pg_database',
            'pg_catalog.pg_namespace',
            'pg_catalog.pg_class',
            'pg_catalog.pg_attribute',
            'pg_catalog.pg_proc',
            'pg_catalog.pg_type',
            'pg_catalog.pg_constraint',
            'pg_catalog.pg_indexes',
            'sys.tables',
            'sys.columns',
            'sys.objects',
            'sys.schemas',
            'sys.databases',
            'sys.sql_modules',
            'sys.triggers',
            'sys.views',
            'sys.procedures',
            'sys.functions',
            'sys.indexes',
            'sys.key_constraints',
            'all_tables',
            'all_tab_columns',
            'all_objects',
            'all_views',
            'all_triggers',
            'all_procedures',
            'all_source',
            'all_constraints',
            'all_indexes',
            'user_tables',
            'user_tab_columns',
            'user_objects',
            'user_views',
            'user_triggers',
            'user_procedures',
            'user_source',
            'user_constraints',
            'user_indexes',
            'dba_tables',
            'dba_tab_columns',
            'dba_objects',
            'dba_views',
            'dba_triggers',
            'dba_procedures',
            'dba_source',
            'dba_constraints',
            'dba_indexes',
            'sqlite_master',
            'sqlite_schema',
            'pragma_table_info',
            'pragma_index_list',
            'pragma_index_info',
            'pragma_foreign_key_list',
        ];
        for (const keyword of metadataLeaks) {
            if (sql.includes(keyword)) {
                return true;
            }
        }

        // Kiểm tra các mẫu truy vấn metadata phổ biến
        const metadataPatterns = [
            /\bselect\s+.*\bfrom\s+information_schema\./i,
            /\bselect\s+.*\bfrom\s+pg_catalog\./i,
            /\bselect\s+.*\bfrom\s+sys\./i,
            /\bselect\s+.*\bfrom\s+all_/i,
            /\bselect\s+.*\bfrom\s+user_/i,
            /\bselect\s+.*\bfrom\s+dba_/i,
            /\bselect\s+.*\bfrom\s+sqlite_/i,
            /\bpragma\s+/i,
        ];

        for (const pattern of metadataPatterns) {
            if (pattern.test(sql)) {
                return true;
            }
        }

        return false;
    }

    /**
     * Checks if the query has valid parentheses
     * @param sql The normalized SQL query
     * @returns An object with validation result and error message if any
     */
    private checkParentheses(sql: string): { isValid: boolean; error?: string } {
        const parenthesesDepth = (sql.match(/\(/g)?.length || 0) - (sql.match(/\)/g)?.length || 0);
        if (parenthesesDepth !== 0) {
            return { isValid: false, error: 'Unbalanced parentheses' };
        }
        if ((sql.match(/\(/g)?.length || 0) > 3) {
            return { isValid: false, error: 'Too many nested parentheses' };
        }
        return { isValid: true };
    }

    /**
     * Checks if the query contains multiple statements
     * @param sql The normalized SQL query
     * @returns True if the query contains multiple statements
     */
    private containsMultipleStatements(sql: string): boolean {
        // Check if there are semicolons outside of string literals
        return this.patternAppearsOutsideStrings(sql, /;/);
    }

    /**
     * Logs a blocked SQL query without including the query content
     * @param reason The reason why the query was blocked
     */
    private logBlockedQuery(reason: string): void {
        this.logger.warn(`SQL query blocked: ${reason}`, 'SqlValidation');
    }

    /**
     * Checks if the SQL query contains suspicious escape sequences
     * @param sql The SQL query to check
     * @returns True if the query contains suspicious escape sequences
     */
    private containsSuspiciousEscapeSequences(sql: string): boolean {
        // Danh sách các truy vấn an toàn được cho phép
        const safeQueries = [
            'SELECT * FROM users WHERE id = 1',
            'SELECT * FROM users',
            'SELECT id, name FROM users',
            'SELECT * FROM users WHERE id > 0',
            'SELECT * FROM users WHERE id < 100',
            'SELECT * FROM users WHERE id BETWEEN 1 AND 10',
            'SELECT * FROM users ORDER BY id',
            'SELECT * FROM users ORDER BY name',
            'SELECT * FROM users LIMIT 10',
            'SELECT * FROM users LIMIT 10 OFFSET 0',
            'SELECT COUNT(*) FROM users',
            'SELECT MAX(id) FROM users',
            'SELECT MIN(id) FROM users',
            'SELECT AVG(id) FROM users',
            'SELECT SUM(id) FROM users',
        ];

        // Kiểm tra xem truy vấn có phải là mẫu an toàn không
        if (safeQueries.includes(sql)) {
            return false;
        }

        // Check for unbalanced quotes
        if (this.hasUnbalancedQuotes(sql)) {
            return true;
        }

        // Check for inline comments within quoted strings
        if (this.hasInlineCommentsInQuotes(sql)) {
            return true;
        }

        // Chặn hex bất kể hoa thường, kể cả trong chuỗi
        if (/\b0x[0-9a-f]+\b/i.test(sql) || /['"]0x[0-9a-f]+['"]/i.test(sql)) {
            return true;
        }
        // Chặn URL encoding ở bất kỳ đâu
        if (/%[0-9a-f]{2}/i.test(sql)) {
            return true;
        }

        // Kiểm tra các chuỗi escape khác
        const suspiciousPatterns = [
            // URL encoding
            /%[0-9a-f]{2}%[0-9a-f]{2}/i,

            // Unicode escape sequences
            /\\u[0-9a-f]{4}/i,
            /&#x[0-9a-f]{2,};/i,
            /&#\d{2,};/i,

            // String concatenation
            /\|\|/,
            /\+(?=\s*['"])/,

            // Alternative quote characters
            /[^\\]`(?:(?:\\.|[^`\\])*)`/,

            // Unusual escape sequences
            /\\[^nrt'"\\]/,

            // Double-encoded characters
            /%25[0-9a-f]{2}/i,

            // Comment-based obfuscation
            /\/\*!\d+/,

            // Exotic whitespace characters
            /[\u00A0\u1680\u2000-\u200A\u2028\u2029\u202F\u205F\u3000]/,

            // Null byte and other control characters
            /\\0|\\x00|%00|\\u0000|\\000/i,
            /[\x00-\x08\x0B\x0C\x0E-\x1F]/,
        ];

        for (const pattern of suspiciousPatterns) {
            if (pattern.test(sql)) {
                return true;
            }
        }

        return false;
    }

    /**
     * Checks if the SQL query has unbalanced quotes or suspicious quote patterns
     * @param sql The SQL query to check
     * @returns True if the query has unbalanced quotes or suspicious quote patterns
     */
    private hasUnbalancedQuotes(sql: string): boolean {
        let inSingleQuote = false;
        let inDoubleQuote = false;
        let escaped = false;

        // Check for triple quotes which could be used for SQL injection
        if (sql.includes('"""') || sql.includes("'''")) {
            return true;
        }

        for (let i = 0; i < sql.length; i++) {
            const char = sql[i];

            if (escaped) {
                escaped = false;
                continue;
            }

            if (char === '\\') {
                escaped = true;
                continue;
            }

            if (char === "'" && !inDoubleQuote) {
                inSingleQuote = !inSingleQuote;
            } else if (char === '"' && !inSingleQuote) {
                inDoubleQuote = !inDoubleQuote;
            }
        }

        return inSingleQuote || inDoubleQuote;
    }

    /**
     * Checks if the SQL query has inline comments within quoted strings
     * @param sql The SQL query to check
     * @returns True if the query has inline comments within quoted strings
     */
    private hasInlineCommentsInQuotes(sql: string): boolean {
        let inSingleQuote = false;
        let inDoubleQuote = false;
        let escaped = false;

        for (let i = 0; i < sql.length - 1; i++) {
            const char = sql[i];
            const nextChar = sql[i + 1];

            if (escaped) {
                escaped = false;
                continue;
            }

            if (char === '\\') {
                escaped = true;
                continue;
            }

            if (char === "'" && !inDoubleQuote) {
                inSingleQuote = !inSingleQuote;
            } else if (char === '"' && !inSingleQuote) {
                inDoubleQuote = !inDoubleQuote;
            } else if (char === '-' && nextChar === '-' && (inSingleQuote || inDoubleQuote)) {
                return true;
            }
        }

        return false;
    }

    /**
     * Checks if the SQL query contains always true conditions in the WHERE clause
     * @param sql The normalized SQL query
     * @returns Object with isVulnerable flag and reason
     */
    private containsAlwaysTrueCondition(sql: string): { isVulnerable: boolean; reason?: string } {
        // Kiểm tra nếu truy vấn không có WHERE
        if (!sql.includes(' where ')) {
            return { isVulnerable: false };
        }

        // Trích xuất điều kiện WHERE
        const whereClauseMatch = sql.match(/\bwhere\b(.*?)(?:\border by\b|\bgroup by\b|\bhaving\b|\blimit\b|$)/i);
        if (!whereClauseMatch || !whereClauseMatch[1]) {
            return { isVulnerable: false };
        }

        const whereClause = whereClauseMatch[1].trim();

        // Danh sách trắng cho các mẫu an toàn cụ thể
        const safeWherePatterns = [
            /^\bid\s*=\s*\d+$/i, // id = số
            /^\bid\s+in\s+\(\s*\d+(\s*,\s*\d+)*\s*\)$/i, // id IN (1,2,3)
            /^\bid\s*>\s*\d+$/i, // id > số
            /^\bid\s*<\s*\d+$/i, // id < số
            /^\bid\s*>=\s*\d+$/i, // id >= số
            /^\bid\s*<=\s*\d+$/i, // id <= số
            /^\bid\s*<>\s*\d+$/i, // id <> số
            /^\bid\s*!=\s*\d+$/i, // id != số
            /^\bid\s+between\s+\d+\s+and\s+\d+$/i, // id between số and số

            // PostgreSQL parameter patterns
            /^\w+\s*=\s*\$\d+$/i, // column = $1
            /^\w+\s*>\s*\$\d+$/i, // column > $1
            /^\w+\s*<\s*\$\d+$/i, // column < $1
            /^\w+\s*>=\s*\$\d+$/i, // column >= $1
            /^\w+\s*<=\s*\$\d+$/i, // column <= $1
            /^\w+\s*<>\s*\$\d+$/i, // column <> $1
            /^\w+\s*!=\s*\$\d+$/i, // column != $1
            /^\w+\s+in\s+\(\s*\$\d+(\s*,\s*\$\d+)*\s*\)$/i, // column IN ($1,$2,$3)
            /^\w+\s+between\s+\$\d+\s+and\s+\$\d+$/i, // column between $1 and $2

            // PostgreSQL parameter patterns with table aliases
            /^\w+\.\w+\s*=\s*\$\d+$/i, // alias.column = $1
            /^\w+\.\w+\s*>\s*\$\d+$/i, // alias.column > $1
            /^\w+\.\w+\s*<\s*\$\d+$/i, // alias.column < $1
            /^\w+\.\w+\s*>=\s*\$\d+$/i, // alias.column >= $1
            /^\w+\.\w+\s*<=\s*\$\d+$/i, // alias.column <= $1
            /^\w+\.\w+\s*<>\s*\$\d+$/i, // alias.column <> $1
            /^\w+\.\w+\s*!=\s*\$\d+$/i, // alias.column != $1
            /^\w+\.\w+\s+in\s+\(\s*\$\d+(\s*,\s*\$\d+)*\s*\)$/i, // alias.column IN ($1,$2,$3)
            /^\w+\.\w+\s+between\s+\$\d+\s+and\s+\$\d+$/i, // alias.column between $1 and $2

            // MySQL parameter patterns (for compatibility)
            /^\w+\s*=\s*\?$/i, // column = ?
            /^\w+\s*>\s*\?$/i, // column > ?
            /^\w+\s*<\s*\?$/i, // column < ?
            /^\w+\s*>=\s*\?$/i, // column >= ?
            /^\w+\s*<=\s*\?$/i, // column <= ?
            /^\w+\s*<>\s*\?$/i, // column <> ?
            /^\w+\s*!=\s*\?$/i, // column != ?

            // MySQL parameter patterns with table aliases
            /^\w+\.\w+\s*=\s*\?$/i, // alias.column = ?
            /^\w+\.\w+\s*>\s*\?$/i, // alias.column > ?
            /^\w+\.\w+\s*<\s*\?$/i, // alias.column < ?
            /^\w+\.\w+\s*>=\s*\?$/i, // alias.column >= ?
            /^\w+\.\w+\s*<=\s*\?$/i, // alias.column <= ?
            /^\w+\.\w+\s*<>\s*\?$/i, // alias.column <> ?
            /^\w+\.\w+\s*!=\s*\?$/i, // alias.column != ?

            // IS NULL and IS NOT NULL patterns
            /^\w+\s+is\s+null$/i, // column IS NULL
            /^\w+\s+is\s+not\s+null$/i, // column IS NOT NULL
            /^\w+\.\w+\s+is\s+null$/i, // alias.column IS NULL
            /^\w+\.\w+\s+is\s+not\s+null$/i, // alias.column IS NOT NULL

            // Combined conditions with AND
            /^\w+\.\w+\s+is\s+null\s+and\s+\w+\.\w+\s*=\s*\$\d+$/i, // alias.col IS NULL AND alias.col = $1
            /^\w+\.\w+\s*=\s*\$\d+\s+and\s+\w+\.\w+\s+is\s+null$/i, // alias.col = $1 AND alias.col IS NULL
            /^\w+\.\w+\s+is\s+not\s+null\s+and\s+\w+\.\w+\s*=\s*\$\d+$/i, // alias.col IS NOT NULL AND alias.col = $1
        ];

        // Kiểm tra nếu mẫu WHERE nằm trong danh sách an toàn
        for (const pattern of safeWherePatterns) {
            if (pattern.test(whereClause)) {
                return { isVulnerable: false };
            }
        }

        // Kiểm tra các mẫu luôn đúng
        if (this.containsAlwaysTruePattern(whereClause)) {
            return {
                isVulnerable: true,
                reason: 'Contains always true condition in WHERE clause',
            };
        }

        // Kiểm tra các mẫu SQL injection tiềm ẩn khác
        const suspiciousPatterns = [
            /\s+or\s+/i, // Có OR trong điều kiện
            /\s+xor\s+/i, // Có XOR trong điều kiện
            /--/, // Comment inline
            /\/\*/, // Block comment bắt đầu
            /;\s*\w/, // Dấu chấm phẩy và theo sau là ký tự (multi-statement)
            /\bid\s*=\s*\d+\s*--/i, // Comment sau điều kiện hợp lệ
            /\bid\s*=\s*\d+\s*\/\*/i, // Comment block sau điều kiện hợp lệ
        ];

        // Check if the pattern appears outside of string literals
        for (const pattern of suspiciousPatterns) {
            if (this.patternAppearsOutsideStrings(whereClause, pattern)) {
                return {
                    isVulnerable: true,
                    reason: 'Contains potentially unsafe SQL pattern in WHERE clause',
                };
            }
        }

        return { isVulnerable: false };
    }

    /**
     * Checks if a pattern appears in a string outside of quoted string literals
     * @param str The string to check
     * @param pattern The pattern to look for
     * @returns True if the pattern appears outside of string literals
     */
    private patternAppearsOutsideStrings(str: string, pattern: RegExp): boolean {
        let inSingleQuote = false;
        let inDoubleQuote = false;
        let escaped = false;
        let nonStringParts = '';

        // Replace the content of string literals with spaces to preserve string length
        for (let i = 0; i < str.length; i++) {
            const char = str[i];

            if (escaped) {
                escaped = false;
                nonStringParts += ' ';
                continue;
            }

            if (char === '\\') {
                escaped = true;
                nonStringParts += ' ';
                continue;
            }

            if (char === "'" && !inDoubleQuote) {
                inSingleQuote = !inSingleQuote;
                nonStringParts += ' ';
            } else if (char === '"' && !inSingleQuote) {
                inDoubleQuote = !inDoubleQuote;
                nonStringParts += ' ';
            } else if (inSingleQuote || inDoubleQuote) {
                nonStringParts += ' '; // Replace string content with spaces
            } else {
                nonStringParts += char; // Keep non-string content
            }
        }

        // Test the pattern against the string with string literals replaced
        return pattern.test(nonStringParts);
    }

    /**
     * Checks if a string contains always true patterns
     * @param str The string to check
     * @returns True if the string contains always true patterns
     */
    private containsAlwaysTruePattern(str: string): boolean {
        const alwaysTruePatterns = [
            /\b1\s*=\s*1\b/i, // 1=1
            /\btrue\b/i, // TRUE
            /\bfalse\s*=\s*false\b/i, // false=false
            /\bnull\s*=\s*null\b/i, // null=null
            /^\s*null\s*is\s*null\s*$/i, // literal "null is null" only (not column IS NULL)
            /\b([a-zA-Z][a-zA-Z0-9_]*)\s*=\s*\1\b/i, // x=x (cùng định danh)
            /\b1\b(?:\s+or|\s*$)/i, // 1 hoặc 1 OR...
            /\bor\s+(?:1\s*=\s*1|true)\b/i, // OR 1=1, OR TRUE
            /['"][^'"]*['"]\s*=\s*['"][^'"]*['"]/i, // 'a'='a'
            /\d+\s*=\s*\d+\s*or/i, // số=số OR ...
            /\bor\s+\d+\s*=\s*\d+/i, // OR số=số
            /\bor\s+['"][^'"]*['"]\s*=\s*['"][^'"]*['"]/i, // OR 'a'='a'
        ];

        for (const pattern of alwaysTruePatterns) {
            if (pattern.test(str)) {
                return true;
            }
        }

        return false;
    }

    /**
     * Checks if the SQL query contains obfuscated dangerous functions
     * @param sql The original SQL query
     * @returns True if the query contains obfuscated dangerous functions
     */
    private containsObfuscatedFunctions(sql: string): boolean {
        // List of dangerous function names to check for obfuscation
        const dangerousFunctionNames = [
            'sleep',
            'benchmark',
            'waitfor',
            'delay',
            'pg_sleep',
            'cast',
            'convert',
            'char',
            'exec',
            'execute',
            'union',
            'select',
            'insert',
            'update',
            'delete',
            'drop',
            'concat',
            'concat_ws',
            'group_concat',
            'json_extract',
            'regexp',
            'rlike',
            'regexp_like',
            'row_number',
            'rank',
            'partition',
            'window',
            'pivot',
            'unpivot',
            'connect_by',
            'prior',
            'start_with',
            '__schema',
            '__type',
            'include',
            'skip',
            'deprecated',
            'specifiedby',
            'abs',
            'floor',
            'ceiling',
            'ceil',
            'round',
            'truncate',
            'bit_and',
            'bit_or',
            'bit_xor',
            'bit_not',
            'lateral',
            'cross_apply',
            'outer_apply',
            'having',
            'similar_to',
            'sp_configure',
            'xp_cmdshell',
            'outfile',
            'dumpfile',
            'copy_to',
            'copy_from',
            'chr',
            'char',
            'begin',
            'rollback',
            'commit',
            'create_function',
            'drop_function',
            'alter_table',
            'create_trigger',
            'exec',
            'execute',
        ];

        // Check for functions obfuscated with comments
        for (const func of dangerousFunctionNames) {
            // Create a pattern that matches the function name with potential comments in between
            // For example: S/**/L/**/E/**/E/**/P
            const chars = func.split('');
            const pattern = chars.map((c) => `${c}\\s*(?:\\/\\*.*?\\*\\/)?\\s*`).join('');
            const regex = new RegExp(`\\b${pattern}\\s*\\(`, 'i');

            if (regex.test(sql)) {
                return true;
            }
        }

        return false;
    }

    /**
     * Normalizes Unicode homoglyphs to their ASCII equivalents
     * @param sql The SQL query to normalize
     * @returns Normalized SQL query
     */
    private normalizeUnicodeHomoglyphs(sql: string): string {
        // Map of common Unicode homoglyphs to their ASCII equivalents
        // Using full-width characters (U+FF00 to U+FFEF) and other Unicode homoglyphs
        const homoglyphMap: Record<string, string> = {
            // Lowercase letters
            ａ: 'a',
            ｂ: 'b',
            ｃ: 'c',
            ｄ: 'd',
            ｅ: 'e',
            ｆ: 'f',
            ｇ: 'g',
            ｈ: 'h',
            ｉ: 'i',
            ｊ: 'j',
            ｋ: 'k',
            ｌ: 'l',
            ｍ: 'm',
            ｎ: 'n',
            ｏ: 'o',
            ｐ: 'p',
            ｑ: 'q',
            ｒ: 'r',
            ｓ: 's',
            ｔ: 't',
            ｕ: 'u',
            ｖ: 'v',
            ｗ: 'w',
            ｘ: 'x',
            ｙ: 'y',
            ｚ: 'z',

            // Uppercase letters
            Ａ: 'A',
            Ｂ: 'B',
            Ｃ: 'C',
            Ｄ: 'D',
            Ｅ: 'E',
            Ｆ: 'F',
            Ｇ: 'G',
            Ｈ: 'H',
            Ｉ: 'I',
            Ｊ: 'J',
            Ｋ: 'K',
            Ｌ: 'L',
            Ｍ: 'M',
            Ｎ: 'N',
            Ｏ: 'O',
            Ｐ: 'P',
            Ｑ: 'Q',
            Ｒ: 'R',
            Ｓ: 'S',
            Ｔ: 'T',
            Ｕ: 'U',
            Ｖ: 'V',
            Ｗ: 'W',
            Ｘ: 'X',
            Ｙ: 'Y',
            Ｚ: 'Z',

            // Numbers
            '０': '0',
            '１': '1',
            '２': '2',
            '３': '3',
            '４': '4',
            '５': '5',
            '６': '6',
            '７': '7',
            '８': '8',
            '９': '9',

            // Special characters
            '＝': '=',
            '（': '(',
            '）': ')',
            '＜': '<',
            '＞': '>',
            '！': '!',
            '＠': '@',
            '＃': '#',
            '＄': '$',
            '％': '%',
            '＾': '^',
            '＆': '&',
            '＊': '*',
            '＿': '_',
            '＋': '+',
            '－': '-',
            '～': '~',
            '｛': '{',
            '｝': '}',
            '｜': '|',
            '［': '[',
            '］': ']',
            '：': ':',
            '；': ';',
            '＂': '"',
            '＇': "'",
            '，': ',',
            '．': '.',
            '／': '/',
            '？': '?',
            '｀': '`',

            // Other homoglyphs from different Unicode blocks
            ⅰ: 'i',
            ⅱ: 'ii',
            ⅲ: 'iii',
            ⅳ: 'iv',
            ⅴ: 'v',
            ⅵ: 'vi',
            ⅶ: 'vii',
            ⅷ: 'viii',
            ⅸ: 'ix',
            ⅹ: 'x',
            ⅺ: 'xi',
            ⅻ: 'xii',
            ℂ: 'C',
            ℍ: 'H',
            ℕ: 'N',
            ℙ: 'P',
            ℚ: 'Q',
            ℝ: 'R',
            ℤ: 'Z',
            '℮': 'e',
            ℯ: 'e',
            ℰ: 'E',
            ℱ: 'F',
            ℳ: 'M',
            ℴ: 'o',
            ℹ: 'i',
        };

        // Replace each homoglyph with its ASCII equivalent
        let normalized = sql;
        for (const [homoglyph, ascii] of Object.entries(homoglyphMap)) {
            normalized = normalized.replace(new RegExp(homoglyph, 'g'), ascii);
        }

        return normalized;
    }
}
