import { Injectable } from '@nestjs/common';
import { BaseService } from '../../commons/bases/base.service';
import { PortletEntity } from '../../entities/portlet.entity';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';

@Injectable()
export class PortletsService extends BaseService<PortletEntity> {
    constructor(@InjectRepository(PortletEntity) public readonly repo: Repository<PortletEntity>) {
        super(repo);
    }
}
