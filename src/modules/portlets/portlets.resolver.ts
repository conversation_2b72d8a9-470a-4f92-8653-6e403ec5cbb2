import { <PERSON>rg<PERSON>, Int, Parent, ResolveField, Resolver } from '@nestjs/graphql';
import { NotFoundException } from '@nestjs/common';
import { AuthMutation, StaticAuthQuery } from '../../commons/decorators/graphql.decorators';
import { DataLoaderService } from '../../data-loaders/data-loaders.service';
import { PortletEntity } from '../../entities/portlet.entity';
import { PortletsService } from './portlets.service';
import { PortletSaveInputDto } from './dtos/portlet-save-input.dto';
import { PortletsModel } from './models/portlets.model';
import { BasePaginationInput } from '../../commons/bases/base.input';
import { IPaginatedType } from '../../commons/bases/base.model';
import { AuthUser } from '../auth/auth.decorator';
import { UserEntity } from '../../entities/user.entity';
import { DepartmentEntity } from '../../entities/department.entity';

@Resolver(() => PortletEntity)
export class PortletsResolver {
    constructor(
        private readonly portletsService: PortletsService,
        private readonly dataLoader: DataLoaderService
    ) {}

    @ResolveField(() => DepartmentEntity, { nullable: true })
    async department(@Parent() parent: PortletEntity): Promise<DepartmentEntity | null> {
        if (!parent.department_id) return null;
        return this.dataLoader.relationBatchOne(DepartmentEntity).load(parent.department_id);
    }

    @StaticAuthQuery(() => PortletsModel, { name: 'portlets_list' })
    async index(@Args('body') body: BasePaginationInput): Promise<IPaginatedType<PortletEntity>> {
        return this.portletsService.search(body);
    }

    @StaticAuthQuery(() => PortletEntity, { name: 'portlet_detail' })
    async detail(@Args('id', { type: () => Int }) id: number): Promise<PortletEntity> {
        const portlet = await this.portletsService.findOne(id);
        if (!portlet) throw new NotFoundException();
        return portlet;
    }

    @AuthMutation(() => PortletEntity, { name: 'portlet_create' })
    async create(@Args('body') body: PortletSaveInputDto, @AuthUser() auth: UserEntity): Promise<PortletEntity> {
        return this.portletsService.create({ ...body, created_by: auth.id });
    }

    @AuthMutation(() => PortletEntity, { name: 'portlet_update' })
    async update(
        @Args('body') body: PortletSaveInputDto,
        @Args('id', { type: () => Int }) id: number,
        @AuthUser() auth: UserEntity
    ): Promise<PortletEntity> {
        return this.portletsService.updateOne(id, { ...body, updated_by: auth.id });
    }

    @AuthMutation(() => Boolean, { name: 'portlet_delete' })
    async delete(@Args('id', { type: () => Int }) id: number, @AuthUser() auth: UserEntity): Promise<boolean> {
        await this.portletsService.softDelete(id, auth.id);
        return true;
    }
}
