import { Module } from '@nestjs/common';
import { PortletsService } from './portlets.service';
import { PortletsResolver } from './portlets.resolver';
import { PortletEntity } from '../../entities/portlet.entity';
import { TypeOrmModule } from '@nestjs/typeorm';

@Module({
    providers: [PortletsService, PortletsResolver],
    imports: [TypeOrmModule.forFeature([PortletEntity])],
})
export class PortletsModule {}
