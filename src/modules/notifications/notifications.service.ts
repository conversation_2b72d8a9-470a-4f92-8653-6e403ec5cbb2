import { Injectable } from '@nestjs/common';
import { BaseService } from '../../commons/bases/base.service';
import { NotificationEntity } from '../../entities/notification.entity';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';

@Injectable()
export class NotificationsService extends BaseService<NotificationEntity> {
    constructor(
        @InjectRepository(NotificationEntity)
        public readonly repo: Repository<NotificationEntity>
    ) {
        super(repo);
    }
}
