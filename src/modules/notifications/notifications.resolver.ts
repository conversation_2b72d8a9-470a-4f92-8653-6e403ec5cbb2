import { Args, Query, Resolver } from '@nestjs/graphql';
import { NotificationsModel } from './models/notifications.model';
import { BasePaginationInput } from '../../commons/bases/base.input';
import { IPaginatedType } from '../../commons/bases/base.model';
import { NotificationsService } from './notifications.service';
import { NotificationEntity } from '../../entities/notification.entity';

@Resolver(() => NotificationEntity)
export class NotificationsResolver {
    constructor(private readonly notificationsService: NotificationsService) {}

    @Query(() => NotificationsModel, { name: 'notifications_list' })
    async index(@Args('body') body: BasePaginationInput): Promise<IPaginatedType<NotificationEntity>> {
        return this.notificationsService.search(body);
    }
}
