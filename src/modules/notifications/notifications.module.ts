import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { NotificationEntity } from '../../entities/notification.entity';
import { NotificationsService } from './notifications.service';
import { NotificationsResolver } from './notifications.resolver';

@Module({
    imports: [TypeOrmModule.forFeature([NotificationEntity])],
    providers: [NotificationsService, NotificationsResolver],
})
export class NotificationsModule {}
