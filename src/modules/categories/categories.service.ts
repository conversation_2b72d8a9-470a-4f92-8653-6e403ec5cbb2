import { Injectable } from '@nestjs/common';
import { BaseService } from '../../commons/bases/base.service';
import { CategoryEntity } from '../../entities/category.entity';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';

@Injectable()
export class CategoriesService extends BaseService<CategoryEntity> {
    constructor(@InjectRepository(CategoryEntity) private readonly repo: Repository<CategoryEntity>) {
        super(repo);
    }
}
