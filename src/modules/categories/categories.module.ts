import { Module } from '@nestjs/common';
import { CategoriesService } from './categories.service';
import { CategoriesResolver } from './categories.resolver';
import { TypeOrmModule } from '@nestjs/typeorm';
import { CategoryEntity } from '../../entities/category.entity';

@Module({
    providers: [CategoriesService, CategoriesResolver],
    imports: [TypeOrmModule.forFeature([CategoryEntity])],
})
export class CategoriesModule {}
