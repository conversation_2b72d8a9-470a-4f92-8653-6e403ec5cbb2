import { Module } from '@nestjs/common';
import { DepartmentsService } from './services/departments.service';
import { DepartmentsResolver } from './resolvers/departments.resolver';
import { DepartmentEntity } from '../../entities/department.entity';
import { TypeOrmModule } from '@nestjs/typeorm';

@Module({
    providers: [DepartmentsService, DepartmentsResolver],
    imports: [TypeOrmModule.forFeature([DepartmentEntity])],
})
export class DepartmentsModule {}
