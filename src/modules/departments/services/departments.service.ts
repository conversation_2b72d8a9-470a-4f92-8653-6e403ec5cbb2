import { Injectable } from '@nestjs/common';
import { BaseService } from '../../../commons/bases/base.service';
import { DepartmentEntity } from '../../../entities/department.entity';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';

@Injectable()
export class DepartmentsService extends BaseService<DepartmentEntity> {
    constructor(@InjectRepository(DepartmentEntity) public readonly repo: Repository<DepartmentEntity>) {
        super(repo);
    }
}
