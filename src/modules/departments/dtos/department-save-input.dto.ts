import { Field, InputType, Int } from '@nestjs/graphql';
import { IsEnum, IsInt, IsNotEmpty, IsOptional, IsPositive, IsString } from 'class-validator';
import { groupValidationMessages, validationMessagesLang } from '../../../languages/validation-messages.lang';
import { IdExists } from '../../../commons/validators/id-exists.validator';
import { IsUnique } from '../../../commons/validators/is-unique.validator';
import { GroupEntity } from '../../../entities/group.entity';
import { BaseUpdateInputDto } from '../../../commons/bases/base.input';
import { DepartmentEntity } from '../../../entities/department.entity';
import { ItemStatus } from '../../../commons/enums.common';
import { AutoSanitize } from '../../../commons/decorators/sanitize.decorators';
import { Languages } from '../../../entities/category.entity';

@InputType()
@AutoSanitize()
export class DepartmentSaveInputDto extends BaseUpdateInputDto {
    @Field()
    @IsNotEmpty({ message: validationMessagesLang.REQUIRED })
    @IsString()
    @IsUnique(GroupEntity, { message: groupValidationMessages.IS_EXISTS })
    name: string;

    @Field(() => Int)
    @IsEnum(ItemStatus)
    status_id: ItemStatus;

    @Field(() => Int, { nullable: true })
    @IsOptional()
    @IsNotEmpty()
    @IdExists(DepartmentEntity)
    parent_id?: number;

    @Field({ nullable: true })
    @IsOptional()
    @IsString()
    desc?: string;

    @Field(() => Int)
    @IsNotEmpty()
    @IsInt()
    @IsPositive()
    display_order: number;

    @Field(() => Int, { nullable: true })
    @IsOptional()
    @IsInt()
    @IsPositive()
    @IsEnum(Languages)
    language_id?: number;
}
