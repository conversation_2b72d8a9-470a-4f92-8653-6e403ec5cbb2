import { Injectable } from '@nestjs/common';
import { BaseService } from '../../../commons/bases/base.service';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { WorkflowPermissionEntity } from '../../../entities/workflow-permission.entity';

@Injectable()
export class WorkflowPermissionsService extends BaseService<WorkflowPermissionEntity> {
    constructor(
        @InjectRepository(WorkflowPermissionEntity) public readonly repo: Repository<WorkflowPermissionEntity>
    ) {
        super(repo);
    }
}
