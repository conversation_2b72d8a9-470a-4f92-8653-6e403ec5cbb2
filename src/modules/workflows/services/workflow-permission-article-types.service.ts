import { Injectable } from '@nestjs/common';
import { BaseService } from '../../../commons/bases/base.service';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { WorkflowPermissionArticleTypeEntity } from '../../../entities/workflow-permission-article-type.entity';

@Injectable()
export class WorkflowPermissionArticleTypesService extends BaseService<WorkflowPermissionArticleTypeEntity> {
    constructor(
        @InjectRepository(WorkflowPermissionArticleTypeEntity)
        public readonly repo: Repository<WorkflowPermissionArticleTypeEntity>
    ) {
        super(repo);
    }
}
