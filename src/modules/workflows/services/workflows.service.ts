import { ForbiddenException, Injectable, NotFoundException } from '@nestjs/common';
import { BaseService } from '../../../commons/bases/base.service';
import { WorkflowEntity, WorkflowType } from '../../../entities/workflow.entity';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { QueryDeepPartialEntity } from 'typeorm/query-builder/QueryPartialEntity';

@Injectable()
export class WorkflowsService extends BaseService<WorkflowEntity> {
    constructor(@InjectRepository(WorkflowEntity) public readonly repo: Repository<WorkflowEntity>) {
        super(repo);
    }

    async destroy(id: number, deletedBy: number): Promise<Boolean> {
        const workflow = await this.findOne(id);
        if (!workflow) throw new NotFoundException();
        if (workflow.workflow_type_id !== WorkflowType.EDITING) throw new ForbiddenException();
        await this.repo.update(id, {
            deleted_at: new Date(),
            deleted_by: deletedBy,
        });
        return true;
    }
}
