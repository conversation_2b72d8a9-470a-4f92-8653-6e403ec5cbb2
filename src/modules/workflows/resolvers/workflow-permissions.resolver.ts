import { WorkflowEntity } from '../../../entities/workflow.entity';
import { AuthResolver } from '../../../commons/decorators/graphql.decorators';
import { DataLoaderService } from '../../../data-loaders/data-loaders.service';
import { Args, Int, Mutation, Parent, Query, ResolveField } from '@nestjs/graphql';
import { DepartmentEntity } from '../../../entities/department.entity';
import { BasePaginationInput } from '../../../commons/bases/base.input';
import { IPaginatedType } from '../../../commons/bases/base.model';
import { NotFoundException } from '@nestjs/common';
import { WorkflowPermissionsService } from '../services/workflow-permissions.service';
import { WorkflowPermissionEntity } from '../../../entities/workflow-permission.entity';
import { WorkflowPermissionArticleTypeEntity } from '../../../entities/workflow-permission-article-type.entity';
import { WorkflowPermissionsModel } from '../models/workflow-permissions.model';
import { WorkflowPermissionSaveInputDto } from '../dtos/workflow-permission-save-input.dto';
import { AuthUser } from '../../auth/auth.decorator';
import { UserEntity } from '../../../entities/user.entity';

@AuthResolver(WorkflowPermissionEntity)
export class WorkflowPermissionsResolver {
    constructor(
        private readonly workflowPermissionsService: WorkflowPermissionsService,
        private readonly dataLoader: DataLoaderService
    ) {}

    @ResolveField(() => DepartmentEntity, { nullable: true })
    async department(@Parent() parent: WorkflowEntity): Promise<DepartmentEntity | null> {
        if (!parent.department_id) return null;
        return this.dataLoader.relationBatchOne(DepartmentEntity).load(parent.department_id);
    }

    @ResolveField(() => [WorkflowPermissionArticleTypeEntity], { nullable: true })
    async articleTypes(@Parent() parent: WorkflowEntity): Promise<WorkflowPermissionArticleTypeEntity[]> {
        //TODO:
        // return this.dataLoader
        //     .relationBatchOneMany(WorkflowPermissionArticleTypeEntity, 'workflowPermission')
        //     .load(parent.id);
        return [];
    }

    @Query(() => WorkflowPermissionsModel, { name: 'workflow_permissions_list' })
    async list(@Args('body') body: BasePaginationInput): Promise<IPaginatedType<WorkflowPermissionEntity>> {
        return this.workflowPermissionsService.search(body);
    }

    @Query(() => WorkflowPermissionEntity, { name: 'workflow_permissions_detail' })
    async view(@Args('id', { type: () => Int }) id: number): Promise<WorkflowPermissionEntity> {
        return this.workflowPermissionsService.findOne(id).then((res) => {
            if (!res) {
                throw new NotFoundException();
            }
            return res;
        });
    }

    @Mutation(() => WorkflowPermissionEntity, { name: 'workflow_permissions_create' })
    async store(
        @Args('body') body: WorkflowPermissionSaveInputDto,
        @AuthUser() auth: UserEntity
    ): Promise<WorkflowPermissionEntity> {
        return this.workflowPermissionsService.create({ ...body, created_by: auth.id });
    }

    @Mutation(() => WorkflowPermissionEntity, { name: 'workflow_permissions_update' })
    async update(
        @Args('id', { type: () => Int }) id: number,
        @Args('body') body: WorkflowPermissionSaveInputDto,
        @AuthUser() auth: UserEntity
    ): Promise<WorkflowPermissionEntity> {
        return this.workflowPermissionsService.updateOne(id, { ...body, updated_by: auth.id });
    }

    @Mutation(() => Boolean, { name: 'workflow_permissions_delete' })
    async destroy(@Args('id', { type: () => Int }) id: number, @AuthUser() auth: UserEntity): Promise<Boolean> {
        await this.workflowPermissionsService.softDelete(id, auth.id);
        return true;
    }
}
