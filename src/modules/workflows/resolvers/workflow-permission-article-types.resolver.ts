import { AuthResolver } from '../../../commons/decorators/graphql.decorators';
import { DataLoaderService } from '../../../data-loaders/data-loaders.service';
import { Args, Int, Mutation, Parent, Query, ResolveField } from '@nestjs/graphql';
import { WorkflowPermissionArticleTypeEntity } from '../../../entities/workflow-permission-article-type.entity';
import { WorkflowPermissionArticleTypesService } from '../services/workflow-permission-article-types.service';
import { WorkflowPermissionEntity } from '../../../entities/workflow-permission.entity';
import { BasePaginationInput } from '../../../commons/bases/base.input';
import { NotFoundException } from '@nestjs/common';
import { ArticleTypeSaveInputDto } from '../dtos/article-type-save-input.dto';
import { UserDepartmentEntity } from '../../../entities/user-department.entity';

@AuthResolver(WorkflowPermissionArticleTypeEntity)
export class WorkflowPermissionArticleTypesResolver {
    constructor(
        private readonly articleTypesService: WorkflowPermissionArticleTypesService,
        private readonly dataLoader: DataLoaderService
    ) {}

    @ResolveField(() => WorkflowPermissionEntity, { nullable: true })
    async workflowPermission(
        @Parent() parent: WorkflowPermissionArticleTypeEntity
    ): Promise<WorkflowPermissionEntity | null> {
        if (!parent.workflow_permission_id) return null;
        return this.dataLoader.relationBatchOne(WorkflowPermissionEntity).load(parent.workflow_permission_id);
    }

    @ResolveField(() => UserDepartmentEntity, { nullable: true })
    async userDepartment(@Parent() parent: WorkflowPermissionArticleTypeEntity): Promise<UserDepartmentEntity | null> {
        if (!parent.user_department_id) return null;
        return this.dataLoader.relationBatchOne(UserDepartmentEntity).load(parent.user_department_id);
    }

    @Query(() => [WorkflowPermissionArticleTypeEntity], { name: 'workflow_permission_article_types_list' })
    async list(@Args('body') body: BasePaginationInput): Promise<WorkflowPermissionArticleTypeEntity[]> {
        return this.articleTypesService.findAllBy(body);
    }

    @Query(() => WorkflowPermissionArticleTypeEntity, { name: 'workflow_permission_article_types_detail' })
    async view(@Args('id', { type: () => Int }) id: number): Promise<WorkflowPermissionArticleTypeEntity> {
        return this.articleTypesService.findOne(id).then((res) => {
            if (!res) {
                throw new NotFoundException();
            }
            return res;
        });
    }

    @Mutation(() => WorkflowPermissionArticleTypeEntity, { name: 'workflow_permission_article_types_create' })
    async store(@Args('body') body: ArticleTypeSaveInputDto): Promise<WorkflowPermissionArticleTypeEntity> {
        return this.articleTypesService.create(body);
    }

    @Mutation(() => WorkflowPermissionArticleTypeEntity, { name: 'workflow_permission_article_types_update' })
    async update(
        @Args('id', { type: () => Int }) id: number,
        @Args('body') body: ArticleTypeSaveInputDto
    ): Promise<WorkflowPermissionArticleTypeEntity> {
        return this.articleTypesService.updateOne(id, body);
    }

    @Mutation(() => Boolean, { name: 'workflow_permission_article_types_delete' })
    async destroy(@Args('id', { type: () => Int }) id: number): Promise<Boolean> {
        await this.articleTypesService.delete(id);
        return true;
    }
}
