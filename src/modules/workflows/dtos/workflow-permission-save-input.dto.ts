import { Field, InputType, Int } from '@nestjs/graphql';
import {
    ArrayNotEmpty,
    IsArray,
    IsEnum,
    IsInt,
    IsNotEmpty,
    IsOptional,
    IsString,
    ValidateNested,
} from 'class-validator';
import { groupValidationMessages, validationMessagesLang } from '../../../languages/validation-messages.lang';
import { IdExists } from '../../../commons/validators/id-exists.validator';
import { IsUnique } from '../../../commons/validators/is-unique.validator';
import { GroupEntity } from '../../../entities/group.entity';
import { BaseUpdateInputDto } from '../../../commons/bases/base.input';
import { DepartmentEntity } from '../../../entities/department.entity';
import { ItemStatus } from '../../../commons/enums.common';
import { WorkflowEntity } from '../../../entities/workflow.entity';
import { Type } from 'class-transformer';
import { ValidateWorkflowTransitions } from '../../../commons/validators/validate-workflow-transitions';
import { AutoSanitize } from '../../../commons/decorators/sanitize.decorators';

@InputType()
@AutoSanitize()
export class WorkflowTransitionInput {
    @Field(() => Int, { nullable: true })
    @IsOptional()
    from: number | null;

    @Field(() => Int)
    @IsInt()
    to: number;
}

@InputType()
@AutoSanitize()
export class WorkflowPermissionSaveInputDto extends BaseUpdateInputDto {
    @Field()
    @IsNotEmpty({ message: validationMessagesLang.REQUIRED })
    @IsString()
    @IsUnique(GroupEntity, { message: groupValidationMessages.IS_EXISTS })
    name: string;

    @Field(() => Int)
    @IsEnum(ItemStatus)
    status_id: ItemStatus;

    @Field({ nullable: true })
    @IsOptional()
    @IsString()
    desc?: string;

    @Field(() => Int, { nullable: true })
    @IsOptional()
    @IsNotEmpty()
    @IdExists(DepartmentEntity)
    department_id: number;

    @Field(() => [Int])
    @IsArray()
    @ArrayNotEmpty()
    @IdExists(WorkflowEntity, { each: true })
    workflow_ids: number[];

    @Field(() => [WorkflowTransitionInput])
    @IsArray()
    @ArrayNotEmpty()
    @ValidateWorkflowTransitions()
    @ValidateNested({ each: true })
    @Type(() => WorkflowTransitionInput)
    workflow_transitions: WorkflowTransitionInput[];
}
