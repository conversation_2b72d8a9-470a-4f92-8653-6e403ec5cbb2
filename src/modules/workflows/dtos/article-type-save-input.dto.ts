import { Field, InputType, Int } from '@nestjs/graphql';
import { IsEnum, IsNotEmpty } from 'class-validator';
import { validationMessagesLang } from '../../../languages/validation-messages.lang';
import { IdExists } from '../../../commons/validators/id-exists.validator';
import { BaseUpdateInputDto } from '../../../commons/bases/base.input';
import { WorkflowPermissionEntity } from '../../../entities/workflow-permission.entity';
import {
    ArticleTypes,
    WorkflowPermissionArticleTypeEntity,
} from '../../../entities/workflow-permission-article-type.entity';
import { IsUnique } from '../../../commons/validators/is-unique.validator';
import { AutoSanitize } from '../../../commons/decorators/sanitize.decorators';
import { UserDepartmentEntity } from '../../../entities/user-department.entity';

@InputType()
@AutoSanitize()
export class ArticleTypeSaveInputDto extends BaseUpdateInputDto {
    @Field()
    @IsNotEmpty({ message: validationMessagesLang.REQUIRED })
    @IdExists(WorkflowPermissionEntity)
    workflow_permission_id: number;

    @Field()
    @IsNotEmpty({ message: validationMessagesLang.REQUIRED })
    @IdExists(UserDepartmentEntity)
    user_department_id: number;

    @Field(() => Int)
    @IsNotEmpty({ message: validationMessagesLang.REQUIRED })
    @IsEnum(ArticleTypes)
    article_type_id: ArticleTypes;
}
