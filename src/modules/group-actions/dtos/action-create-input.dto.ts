import { Field, InputType, Int } from '@nestjs/graphql';
import { IsInt, IsNotEmpty, IsOptional, IsPositive, IsString } from 'class-validator';
import { IdExists } from '../../../commons/validators/id-exists.validator';
import { AutoSanitize } from '../../../commons/decorators/sanitize.decorators';

@InputType()
@AutoSanitize()
export class ActionCreateInputDto {
    @Field(() => String)
    @IsNotEmpty()
    @IsString()
    name!: string;

    @Field(() => String, { nullable: true })
    @IsOptional()
    @IsString()
    url?: string;

    @Field(() => String, { nullable: true })
    @IsOptional()
    @IsString()
    icon?: string;

    @Field(() => Int, { nullable: true })
    @IsOptional()
    @IdExists('actions')
    parent_id?: number;

    @Field(() => Int)
    @IsInt()
    @IsPositive()
    display_order: number;
}
