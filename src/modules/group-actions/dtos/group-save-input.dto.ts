import { Field, InputType, Int } from '@nestjs/graphql';
import { IsArray, IsEnum, IsNotEmpty, IsOptional, IsString } from 'class-validator';
import { groupValidationMessages, validationMessagesLang } from '../../../languages/validation-messages.lang';
import { IdExists } from '../../../commons/validators/id-exists.validator';
import { IsUnique } from '../../../commons/validators/is-unique.validator';
import { GroupEntity, GroupType } from '../../../entities/group.entity';
import { BaseUpdateInputDto } from '../../../commons/bases/base.input';
import { DepartmentEntity } from '../../../entities/department.entity';
import { AutoSanitize } from '../../../commons/decorators/sanitize.decorators';
import { ItemStatus } from '../../../commons/enums.common';

@InputType()
@AutoSanitize()
export class GroupSaveInputDto extends BaseUpdateInputDto {
    @Field()
    @IsNotEmpty({ message: validationMessagesLang.REQUIRED })
    @IsString()
    @IsUnique(GroupEntity, { message: groupValidationMessages.IS_EXISTS })
    name: string;

    @Field(() => Int)
    @IsNotEmpty()
    @IdExists(DepartmentEntity)
    department_id: number;

    @Field(() => [Number], { nullable: true })
    @IsOptional()
    @IsArray()
    @IsNotEmpty({ message: validationMessagesLang.REQUIRED, each: true })
    @IdExists('actions', { each: true })
    action_ids?: number[];

    @Field(() => Int, { nullable: true })
    @IsOptional()
    @IsNotEmpty()
    parent_id?: number;

    @Field(() => String, { nullable: true })
    @IsOptional()
    @IsString()
    desc?: string;

    @Field(() => Int)
    @IsEnum(GroupType)
    type_id?: GroupType;

    @Field(() => Int)
    @IsEnum(ItemStatus)
    status_id?: ItemStatus;
}

export interface IGroupSaveInput {
    name: string;
    desc?: string;
    parent_id?: number;
    action_ids?: number[];
    created_by?: number;
    updated_by?: number;
    type_id?: GroupType;
    status_id?: ItemStatus;
}
