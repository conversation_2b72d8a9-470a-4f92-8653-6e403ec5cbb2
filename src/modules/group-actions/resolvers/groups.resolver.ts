import { Args, Int, Mutation, Parent, Query, ResolveField } from '@nestjs/graphql';
import { GroupsService } from '../services/groups.service';
import { GroupEntity } from '../../../entities/group.entity';
import { AuthResolver } from '../../../commons/decorators/graphql.decorators';
import { BasePaginationInput } from '../../../commons/bases/base.input';
import { AuthUser } from '../../auth/auth.decorator';
import { UserEntity, UserRoles } from '../../../entities/user.entity';
import { GroupSaveInputDto } from '../dtos/group-save-input.dto';
import { GroupsModel } from '../models/groups.model';
import { IPaginatedType } from '../../../commons/bases/base.model';
import { Roles } from '../../../commons/decorators/roles.decorator';
import { ActionEntity } from '../../../entities/action.entity';
import { DataLoaderService } from '../../../data-loaders/data-loaders.service';
import { SaveActionsDto } from '../dtos/save-actions.dto';
import { DepartmentEntity } from '../../../entities/department.entity';
import { UserDepartmentEntity } from '../../../entities/user-department.entity';

@AuthResolver(GroupEntity)
@Roles(UserRoles.ADMIN)
export class GroupsResolver {
    constructor(
        private readonly groupsService: GroupsService,
        private readonly dataLoader: DataLoaderService
    ) {}

    @ResolveField(() => [ActionEntity], { nullable: true })
    async actions(@Parent() group: GroupEntity): Promise<ActionEntity[]> {
        return this.dataLoader.relationBatchManyMany(ActionEntity, 'groups').load(group.id);
    }

    @ResolveField(() => [UserEntity], { nullable: true })
    async users(@Parent() group: GroupEntity): Promise<UserEntity[]> {
        return this.dataLoader.relationBatchManyMany(UserEntity, 'groups').load(group.id);
    }

    @ResolveField(() => DepartmentEntity, { nullable: true })
    async department(@Parent() group: GroupEntity): Promise<DepartmentEntity | null> {
        if (!group.department_id) return null;
        return this.dataLoader.relationBatchOne(DepartmentEntity).load(group.id);
    }

    @ResolveField(() => [UserDepartmentEntity], { nullable: true })
    async userDepartments(@Parent() group: GroupEntity): Promise<UserDepartmentEntity[]> {
        return this.dataLoader.relationBatchManyMany(UserDepartmentEntity, 'groups').load(group.id);
    }

    @ResolveField(() => GroupEntity, { nullable: true })
    async parent(@Parent() parent: GroupEntity): Promise<GroupEntity | null> {
        if (!parent.parent_id) return null;
        return this.dataLoader.relationBatchOne(GroupEntity).load(parent.parent_id);
    }

    @ResolveField(() => [GroupEntity], { nullable: true })
    async children(@Parent() parent: GroupEntity): Promise<GroupEntity[]> {
        return this.dataLoader.relationBatchOneMany(GroupEntity, 'parent').load(parent.id);
    }

    @Query(() => GroupsModel, { name: 'groups_list' })
    async list(@Args('body') body: BasePaginationInput): Promise<IPaginatedType<GroupEntity>> {
        return this.groupsService.search(body);
    }

    @Mutation(() => GroupEntity, { name: 'groups_create' })
    async store(@Args('body') body: GroupSaveInputDto, @AuthUser() auth: UserEntity): Promise<GroupEntity> {
        return this.groupsService.saveGroup({ ...body, created_by: auth.id });
    }

    @Mutation(() => GroupEntity, { name: 'groups_update' })
    async update(
        @Args('id', { type: () => Int }) id: number,
        @Args('body') body: GroupSaveInputDto,
        @AuthUser() auth: UserEntity
    ): Promise<GroupEntity> {
        return this.groupsService.saveGroup({ ...body, updated_by: auth.id }, id);
    }

    @Mutation(() => Boolean, { name: 'groups_delete' })
    async destroy(@Args('id', { type: () => Int }) id: number, @AuthUser() auth: UserEntity): Promise<boolean> {
        await this.groupsService.softDelete(id, auth.id);
        return true;
    }

    @Mutation(() => Boolean, { name: 'groups_actions' })
    async changeActions(
        @Args('id', { type: () => Int }) id: number,
        @Args('body') body: SaveActionsDto,
        @AuthUser() auth: UserEntity
    ): Promise<boolean> {
        await this.groupsService.saveActions({ ...body, group_id: id, created_by: auth.id });
        return true;
    }
}
