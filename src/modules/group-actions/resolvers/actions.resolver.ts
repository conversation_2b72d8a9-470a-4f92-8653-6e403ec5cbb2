import { Args, Int, Mutation, Parent, Query, ResolveField } from '@nestjs/graphql';
import { AuthResolver } from '../../../commons/decorators/graphql.decorators';
import { BasePaginationInput } from '../../../commons/bases/base.input';
import { AuthUser } from '../../auth/auth.decorator';
import { UserEntity, UserRoles } from '../../../entities/user.entity';
import { ActionEntity } from '../../../entities/action.entity';
import { ActionsService } from '../services/actions.service';
import { ActionCreateInputDto } from '../dtos/action-create-input.dto';
import { ActionsModel } from '../models/actions.model';
import { IPaginatedType } from '../../../commons/bases/base.model';
import { ActionUpdateInputDto } from '../dtos/action-update-input.dto';
import { Roles } from '../../../commons/decorators/roles.decorator';
import { GroupEntity } from '../../../entities/group.entity';
import { DataLoaderService } from '../../../data-loaders/data-loaders.service';

@AuthResolver(ActionEntity)
@Roles(UserRoles.ADMIN)
export class ActionsResolver {
    constructor(
        private readonly actionsService: ActionsService,
        private readonly dataLoader: DataLoaderService
    ) {}

    @ResolveField(() => [GroupEntity], { nullable: true })
    async groups(@Parent() action: ActionEntity): Promise<GroupEntity[]> {
        return this.dataLoader.relationBatchManyMany(GroupEntity, 'actions').load(action.id);
    }

    @ResolveField(() => [ActionEntity], { nullable: true })
    async children(@Parent() action: ActionEntity): Promise<ActionEntity[]> {
        return this.dataLoader.relationBatchOneMany(ActionEntity, 'parent').load(action.id);
    }

    @ResolveField(() => ActionEntity, { nullable: true })
    async parent(@Parent() action: ActionEntity): Promise<ActionEntity | null> {
        if (!action.parent_id) return null;
        return this.dataLoader.relationBatchOne(ActionEntity).load(action.parent_id);
    }

    @Query(() => ActionsModel, { name: 'actions_list' })
    async list(@Args('body') body: BasePaginationInput): Promise<IPaginatedType<ActionEntity>> {
        return this.actionsService.search(body);
    }

    @Mutation(() => ActionEntity, { name: 'actions_create' })
    async store(@Args('body') body: ActionCreateInputDto, @AuthUser() auth: UserEntity): Promise<ActionEntity> {
        return this.actionsService.create({ ...body, created_by: auth.id });
    }

    @Mutation(() => ActionEntity, { name: 'actions_update' })
    async update(
        @Args('id', { type: () => Int }) id: number,
        @Args('body') body: ActionUpdateInputDto,
        @AuthUser() auth: UserEntity
    ): Promise<ActionEntity> {
        return this.actionsService.updateOne(id, { ...body, updated_by: auth.id });
    }

    @Mutation(() => Boolean, { name: 'actions_delete' })
    async destroy(@Args('id', { type: () => Int }) id: number, @AuthUser() auth: UserEntity): Promise<boolean> {
        await this.actionsService.softDelete(id, auth.id);
        return true;
    }
}
