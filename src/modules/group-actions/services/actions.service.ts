import { Injectable } from '@nestjs/common';
import { BaseService } from '../../../commons/bases/base.service';
import { ActionEntity } from '../../../entities/action.entity';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';

@Injectable()
export class ActionsService extends BaseService<ActionEntity> {
    constructor(
        @InjectRepository(ActionEntity)
        public readonly repo: Repository<ActionEntity>
    ) {
        super(repo);
    }
}
