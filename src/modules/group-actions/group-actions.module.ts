import { Module } from '@nestjs/common';
import { ActionsService } from './services/actions.service';
import { GroupsService } from './services/groups.service';
import { GroupsResolver } from './resolvers/groups.resolver';
import { TypeOrmModule } from '@nestjs/typeorm';
import { GroupEntity } from '../../entities/group.entity';
import { ActionEntity } from '../../entities/action.entity';
import { ActionsResolver } from './resolvers/actions.resolver';

@Module({
    imports: [TypeOrmModule.forFeature([GroupEntity, ActionEntity])],
    providers: [ActionsService, GroupsService, GroupsResolver, ActionsResolver],
})
export class GroupActionsModule {}
