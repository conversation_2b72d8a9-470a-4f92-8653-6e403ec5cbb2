import { Field, InputType, Int } from '@nestjs/graphql';
import { IsEnum, IsInt, IsNotEmpty, IsOptional, IsPositive, IsString } from 'class-validator';
import { validationMessagesLang } from '../../../languages/validation-messages.lang';
import { BaseUpdateInputDto } from '../../../commons/bases/base.input';
import { ItemStatus } from '../../../commons/enums.common';
import { AdvertiseEntity, AdvType, DisplayType } from '../../../entities/advertise.entity';
import { IdExists } from '../../../commons/validators/id-exists.validator';
import { DepartmentEntity } from '../../../entities/department.entity';
import { AutoSanitize } from '../../../commons/decorators/sanitize.decorators';
import { IsUniqueBy } from '../../../commons/validators/is-unique-by.validator';

@InputType()
@AutoSanitize()
export class AdvertiseSaveInputDto extends BaseUpdateInputDto {
    @Field()
    @IsNotEmpty({ message: validationMessagesLang.REQUIRED })
    @IsString()
    @IsUniqueBy(AdvertiseEntity, 'department_id')
    code: string;

    @Field()
    @IsNotEmpty({ message: validationMessagesLang.REQUIRED })
    @IsString()
    name: string;

    @Field({ nullable: true })
    @IsOptional()
    @IsString()
    desc?: string;

    @Field(() => Int)
    @IsEnum(AdvType)
    type_id: AdvType;

    @Field(() => Int)
    @IsInt()
    @IsPositive()
    speed: number;

    @Field(() => Int)
    @IsEnum(DisplayType)
    display_type_id: DisplayType;

    @Field(() => Int)
    @IsEnum(ItemStatus)
    status_id: ItemStatus;

    @Field(() => Int)
    @IsNotEmpty()
    @IdExists(DepartmentEntity)
    department_id: number;
}
