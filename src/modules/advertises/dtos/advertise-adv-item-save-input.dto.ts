import { Field, InputType, Int } from '@nestjs/graphql';
import { IsInt, IsNotEmpty, IsPositive } from 'class-validator';
import { BaseUpdateInputDto } from '../../../commons/bases/base.input';
import { IdExists } from '../../../commons/validators/id-exists.validator';
import { AdvertiseEntity } from '../../../entities/advertise.entity';
import { AdvItemEntity } from '../../../entities/adv-item.entity';
import { AutoSanitize } from '../../../commons/decorators/sanitize.decorators';

@InputType()
@AutoSanitize()
export class AdvertiseAdvItemSaveInputDto extends BaseUpdateInputDto {
    @Field(() => Int)
    @IsNotEmpty()
    @IdExists(AdvertiseEntity)
    advertise_id: number;

    @Field(() => Int)
    @IsNotEmpty()
    @IdExists(AdvItemEntity)
    adv_item_id: number;

    @Field(() => Int)
    @IsInt()
    @IsPositive()
    display_order: number;
}
