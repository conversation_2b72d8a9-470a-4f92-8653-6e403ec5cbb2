import { Field, InputType, Int } from '@nestjs/graphql';
import { IsDate, IsEnum, IsInt, IsNotEmpty, IsOptional, IsPositive, IsString } from 'class-validator';
import { validationMessagesLang } from '../../../languages/validation-messages.lang';
import { BaseUpdateInputDto } from '../../../commons/bases/base.input';
import { ItemStatus } from '../../../commons/enums.common';
import { IdExists } from '../../../commons/validators/id-exists.validator';
import { DepartmentEntity } from '../../../entities/department.entity';
import { AutoSanitize } from '../../../commons/decorators/sanitize.decorators';
import { FileEntity } from '../../../entities/file.entity';
import { Type } from 'class-transformer';
import { AdvItemType } from '../../../entities/adv-item.entity';

@InputType()
@AutoSanitize()
export class AdvItemSaveInputDto extends BaseUpdateInputDto {
    @Field()
    @IsNotEmpty({ message: validationMessagesLang.REQUIRED })
    @IsString()
    name: string;

    @Field({ nullable: true })
    @IsOptional()
    @IsString()
    desc?: string;

    @Field()
    @IsNotEmpty({ message: validationMessagesLang.REQUIRED })
    @IsString()
    content: string;

    @Field(() => Int)
    @IsEnum(AdvItemType)
    type_id: AdvItemType;

    @Field(() => Int, { nullable: true })
    @IsOptional()
    @IdExists(FileEntity)
    file_id?: number;

    @Field(() => Int)
    @IsEnum(ItemStatus)
    status_id: ItemStatus;

    @Field(() => Int)
    @IsInt()
    @IsPositive()
    width: number;

    @Field(() => Int)
    @IsInt()
    @IsPositive()
    height: number;

    @Field()
    @IsNotEmpty()
    @IsDate()
    @Type(() => Date)
    start_date: Date;

    @Field({ nullable: true })
    @IsOptional()
    @IsDate()
    @Type(() => Date)
    end_date?: Date;

    @Field(() => Int)
    @IsNotEmpty()
    @IdExists(DepartmentEntity)
    department_id: number;
}
