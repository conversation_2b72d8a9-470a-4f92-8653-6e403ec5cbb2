import { Args, Int, Mutation, Parent, Query, ResolveField } from '@nestjs/graphql';
import { NotFoundException } from '@nestjs/common';
import { AuthResolver } from '../../../commons/decorators/graphql.decorators';
import { AdvItemEntity } from '../../../entities/adv-item.entity';
import { AdvItemsService } from '../services/adv-items.service';
import { DataLoaderService } from '../../../data-loaders/data-loaders.service';
import { DepartmentEntity } from '../../../entities/department.entity';
import { BasePaginationInput } from '../../../commons/bases/base.input';
import { AdvItemsModel } from '../models/adv-items.model';
import { IPaginatedType } from '../../../commons/bases/base.model';
import { AuthUser } from '../../../modules/auth/auth.decorator';
import { UserEntity } from '../../../entities/user.entity';
import { AdvItemSaveInputDto } from '../dtos/adv-item-save-input.dto';
import { FileEntity } from '../../../entities/file.entity';
import { AdvertiseAdvItemEntity } from '../../../entities/advertise-adv-item.entity';

@AuthResolver(AdvItemEntity)
export class AdvItemsResolver {
    constructor(
        private readonly advItemsService: AdvItemsService,
        private readonly dataLoader: DataLoaderService
    ) {}

    @ResolveField(() => DepartmentEntity, { nullable: true })
    async department(@Parent() parent: AdvItemEntity): Promise<DepartmentEntity | null> {
        if (!parent.department_id) return null;
        return this.dataLoader.relationBatchOne(DepartmentEntity).load(parent.department_id);
    }

    @ResolveField(() => FileEntity, { nullable: true })
    async file(@Parent() parent: AdvItemEntity): Promise<FileEntity | null> {
        if (!parent.file_id) return null;
        return this.dataLoader.relationBatchOne(FileEntity).load(parent.file_id);
    }

    @ResolveField(() => [AdvertiseAdvItemEntity], { nullable: true })
    async advertiseAdvItems(@Parent() parent: AdvItemEntity): Promise<AdvertiseAdvItemEntity[]> {
        return this.dataLoader.relationBatchOneMany(AdvertiseAdvItemEntity, 'advItem').load(parent.id);
    }

    @Query(() => AdvItemsModel, { name: 'adv_items_list' })
    async index(@Args('body') body: BasePaginationInput): Promise<IPaginatedType<AdvItemEntity>> {
        return this.advItemsService.search(body);
    }

    @Query(() => AdvItemEntity, { name: 'adv_items_detail' })
    async view(@Args('id', { type: () => Int }) id: number): Promise<AdvItemEntity> {
        return this.advItemsService.findOne(id).then((res) => {
            if (!res) throw new NotFoundException();
            return res;
        });
    }

    @Mutation(() => AdvItemEntity, { name: 'adv_items_create' })
    async store(@Args('body') body: AdvItemSaveInputDto, @AuthUser() auth: UserEntity): Promise<AdvItemEntity> {
        return this.advItemsService.create({ ...body, created_by: auth.id });
    }

    @Mutation(() => AdvItemEntity, { name: 'adv_items_update' })
    async update(
        @Args('id', { type: () => Int }) id: number,
        @Args('body') body: AdvItemSaveInputDto,
        @AuthUser() auth: UserEntity
    ): Promise<AdvItemEntity> {
        return this.advItemsService.updateOne(id, { ...body, updated_by: auth.id });
    }

    @Mutation(() => Boolean, { name: 'adv_items_delete' })
    async destroy(@Args('id', { type: () => Int }) id: number, @AuthUser() auth: UserEntity): Promise<boolean> {
        await this.advItemsService.softDelete(id, auth.id);
        return true;
    }
}
