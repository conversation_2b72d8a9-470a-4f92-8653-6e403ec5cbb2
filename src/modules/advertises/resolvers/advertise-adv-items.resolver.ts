import { Args, Int, Mutation, Parent, Query, ResolveField } from '@nestjs/graphql';
import { NotFoundException } from '@nestjs/common';
import { AuthResolver } from '../../../commons/decorators/graphql.decorators';
import { AdvertiseAdvItemEntity } from '../../../entities/advertise-adv-item.entity';
import { AdvertiseAdvItemsService } from '../services/advertise-adv-items.service';
import { DataLoaderService } from '../../../data-loaders/data-loaders.service';
import { BasePaginationInput } from '../../../commons/bases/base.input';
import { AdvertiseAdvItemsModel } from '../models/advertise-adv-items.model';
import { IPaginatedType } from '../../../commons/bases/base.model';
import { AuthUser } from '../../../modules/auth/auth.decorator';
import { UserEntity } from '../../../entities/user.entity';
import { AdvertiseAdvItemSaveInputDto } from '../dtos/advertise-adv-item-save-input.dto';
import { AdvertiseEntity } from '../../../entities/advertise.entity';
import { AdvItemEntity } from '../../../entities/adv-item.entity';

@AuthResolver(AdvertiseAdvItemEntity)
export class AdvertiseAdvItemsResolver {
    constructor(
        private readonly advertiseAdvItemsService: AdvertiseAdvItemsService,
        private readonly dataLoader: DataLoaderService
    ) {}

    @ResolveField(() => AdvertiseEntity, { nullable: true })
    async advertise(@Parent() parent: AdvertiseAdvItemEntity): Promise<AdvertiseEntity | null> {
        if (!parent.advertise_id) return null;
        return this.dataLoader.relationBatchOne(AdvertiseEntity).load(parent.advertise_id);
    }

    @ResolveField(() => AdvItemEntity, { nullable: true })
    async advItem(@Parent() parent: AdvertiseAdvItemEntity): Promise<AdvItemEntity | null> {
        if (!parent.adv_item_id) return null;
        return this.dataLoader.relationBatchOne(AdvItemEntity).load(parent.adv_item_id);
    }

    @Query(() => AdvertiseAdvItemsModel, { name: 'advertise_adv_items_list' })
    async index(@Args('body') body: BasePaginationInput): Promise<IPaginatedType<AdvertiseAdvItemEntity>> {
        return this.advertiseAdvItemsService.search(body);
    }

    @Query(() => AdvertiseAdvItemEntity, { name: 'advertise_adv_items_detail' })
    async view(@Args('id', { type: () => Int }) id: number): Promise<AdvertiseAdvItemEntity> {
        return this.advertiseAdvItemsService.findOne(id).then((res) => {
            if (!res) throw new NotFoundException();
            return res;
        });
    }

    @Mutation(() => AdvertiseAdvItemEntity, { name: 'advertise_adv_items_create' })
    async store(
        @Args('body') body: AdvertiseAdvItemSaveInputDto,
        @AuthUser() auth: UserEntity
    ): Promise<AdvertiseAdvItemEntity> {
        return this.advertiseAdvItemsService.create({ ...body, created_by: auth.id });
    }

    @Mutation(() => AdvertiseAdvItemEntity, { name: 'advertise_adv_items_update' })
    async update(
        @Args('id', { type: () => Int }) id: number,
        @Args('body') body: AdvertiseAdvItemSaveInputDto,
        @AuthUser() auth: UserEntity
    ): Promise<AdvertiseAdvItemEntity> {
        return this.advertiseAdvItemsService.updateOne(id, { ...body, updated_by: auth.id });
    }

    @Mutation(() => Boolean, { name: 'advertise_adv_items_delete' })
    async destroy(@Args('id', { type: () => Int }) id: number, @AuthUser() auth: UserEntity): Promise<boolean> {
        await this.advertiseAdvItemsService.softDelete(id, auth.id);
        return true;
    }
}
