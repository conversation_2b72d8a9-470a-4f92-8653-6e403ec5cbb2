import { Args, Int, Mutation, Parent, Query, ResolveField } from '@nestjs/graphql';
import { NotFoundException } from '@nestjs/common';
import { AuthResolver } from '../../../commons/decorators/graphql.decorators';
import { AdvertiseEntity } from '../../../entities/advertise.entity';
import { AdvertisesService } from '../services/advertises.service';
import { DataLoaderService } from '../../../data-loaders/data-loaders.service';
import { DepartmentEntity } from '../../../entities/department.entity';
import { BasePaginationInput } from '../../../commons/bases/base.input';
import { AdvertisesModel } from '../models/advertises.model';
import { IPaginatedType } from '../../../commons/bases/base.model';
import { AuthUser } from '../../auth/auth.decorator';
import { UserEntity } from '../../../entities/user.entity';
import { AdvertiseSaveInputDto } from '../dtos/advertise-save-input.dto';
import { AdvertiseAdvItemEntity } from '../../../entities/advertise-adv-item.entity';
import { CategoryEntity } from '../../../entities/category.entity';

@AuthResolver(AdvertiseEntity)
export class AdvertisesResolver {
    constructor(
        private readonly advertisesService: AdvertisesService,
        private readonly dataLoader: DataLoaderService
    ) {}

    @ResolveField(() => DepartmentEntity, { nullable: true })
    async department(@Parent() parent: AdvertiseEntity): Promise<DepartmentEntity | null> {
        if (!parent.department_id) return null;
        return this.dataLoader.relationBatchOne(DepartmentEntity).load(parent.department_id);
    }

    @ResolveField(() => [AdvertiseAdvItemEntity], { nullable: true })
    async advertiseAdvItems(@Parent() parent: AdvertiseEntity): Promise<AdvertiseAdvItemEntity[]> {
        return this.dataLoader.relationBatchOneMany(AdvertiseAdvItemEntity, 'advertise').load(parent.id);
    }

    @ResolveField(() => [CategoryEntity], { nullable: true })
    async categories(@Parent() parent: AdvertiseEntity): Promise<CategoryEntity[]> {
        return this.dataLoader.relationBatchManyMany(CategoryEntity, 'advertises').load(parent.id);
    }

    @Query(() => AdvertisesModel, { name: 'advertises_list' })
    async index(@Args('body') body: BasePaginationInput): Promise<IPaginatedType<AdvertiseEntity>> {
        return this.advertisesService.search(body);
    }

    @Query(() => AdvertiseEntity, { name: 'advertises_detail' })
    async view(@Args('id', { type: () => Int }) id: number): Promise<AdvertiseEntity> {
        return this.advertisesService.findOne(id).then((res) => {
            if (!res) throw new NotFoundException();
            return res;
        });
    }

    @Mutation(() => AdvertiseEntity, { name: 'advertises_create' })
    async store(@Args('body') body: AdvertiseSaveInputDto, @AuthUser() auth: UserEntity): Promise<AdvertiseEntity> {
        return this.advertisesService.create({ ...body, created_by: auth.id });
    }

    @Mutation(() => AdvertiseEntity, { name: 'advertises_update' })
    async update(
        @Args('id', { type: () => Int }) id: number,
        @Args('body') body: AdvertiseSaveInputDto,
        @AuthUser() auth: UserEntity
    ): Promise<AdvertiseEntity> {
        return this.advertisesService.updateOne(id, { ...body, updated_by: auth.id });
    }

    @Mutation(() => Boolean, { name: 'advertises_delete' })
    async destroy(@Args('id', { type: () => Int }) id: number, @AuthUser() auth: UserEntity): Promise<boolean> {
        await this.advertisesService.softDelete(id, auth.id);
        return true;
    }
}
