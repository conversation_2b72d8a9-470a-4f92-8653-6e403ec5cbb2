import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { AdvertiseEntity } from '../../entities/advertise.entity';
import { AdvertisesService } from './services/advertises.service';
import { AdvertisesResolver } from './resolvers/advertises.resolver';
import { AdvItemEntity } from '../../entities/adv-item.entity';
import { AdvertiseAdvItemEntity } from '../../entities/advertise-adv-item.entity';
import { AdvItemsService } from './services/adv-items.service';
import { AdvertiseAdvItemsService } from './services/advertise-adv-items.service';
import { AdvItemsResolver } from './resolvers/adv-items.resolver';
import { AdvertiseAdvItemsResolver } from './resolvers/advertise-adv-items.resolver';

@Module({
    imports: [TypeOrmModule.forFeature([AdvertiseEntity, AdvItemEntity, AdvertiseAdvItemEntity])],
    providers: [
        AdvertisesService,
        AdvertisesResolver,
        AdvItemsService,
        AdvItemsResolver,
        AdvertiseAdvItemsService,
        AdvertiseAdvItemsResolver,
    ],
})
export class AdvertisesModule {}
