import { Injectable } from '@nestjs/common';
import { BaseService } from '../../../commons/bases/base.service';
import { AdvertiseAdvItemEntity } from '../../../entities/advertise-adv-item.entity';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';

@Injectable()
export class AdvertiseAdvItemsService extends BaseService<AdvertiseAdvItemEntity> {
    constructor(@InjectRepository(AdvertiseAdvItemEntity) public readonly repo: Repository<AdvertiseAdvItemEntity>) {
        super(repo);
    }
}
