import { Injectable } from '@nestjs/common';
import { BaseService } from '../../../commons/bases/base.service';
import { AdvItemEntity } from '../../../entities/adv-item.entity';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';

@Injectable()
export class AdvItemsService extends BaseService<AdvItemEntity> {
    constructor(@InjectRepository(AdvItemEntity) public readonly repo: Repository<AdvItemEntity>) {
        super(repo);
    }
}
