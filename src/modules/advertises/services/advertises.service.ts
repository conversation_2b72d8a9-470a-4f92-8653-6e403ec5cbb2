import { Injectable } from '@nestjs/common';
import { BaseService } from '../../../commons/bases/base.service';
import { AdvertiseEntity } from '../../../entities/advertise.entity';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';

@Injectable()
export class AdvertisesService extends BaseService<AdvertiseEntity> {
    constructor(@InjectRepository(AdvertiseEntity) public readonly repo: Repository<AdvertiseEntity>) {
        super(repo);
    }
}
