import { Args, Int, Mutation, Parent, Query, ResolveField } from '@nestjs/graphql';
import { NotFoundException } from '@nestjs/common';
import { AuthResolver } from '../../../commons/decorators/graphql.decorators';
import { DataLoaderService } from '../../../data-loaders/data-loaders.service';
import { PressPublicationEntity } from '../../../entities/press-publication.entity';
import { DepartmentEntity } from '../../../entities/department.entity';
import { FileEntity } from '../../../entities/file.entity';
import { IssueEntity } from '../../../entities/issue.entity';
import { UserEntity } from '../../../entities/user.entity';
import { PressPublicationsService } from '../services/press-publications.service';
import { PressPublicationsModel } from '../models/press-publications.model';
import { PressPublicationSaveInputDto } from '../dtos/press-publication-save-input.dto';
import { BasePaginationInput } from '../../../commons/bases/base.input';
import { IPaginatedType } from '../../../commons/bases/base.model';
import { AuthUser } from '../../auth/auth.decorator';
import { IssuePageEntity } from '../../../entities/issue-page.entity';
import { ArticleEntity } from '../../../entities/article.entity';

@AuthResolver(PressPublicationEntity)
export class PressPublicationsResolver {
    constructor(
        private readonly pressPublicationsService: PressPublicationsService,
        private readonly dataLoader: DataLoaderService
    ) {}

    @ResolveField(() => DepartmentEntity, { nullable: true })
    async department(@Parent() parent: PressPublicationEntity): Promise<DepartmentEntity | null> {
        if (!parent.department_id) return null;
        return this.dataLoader.relationBatchOne(DepartmentEntity).load(parent.department_id);
    }

    @ResolveField(() => FileEntity, { nullable: true })
    async avatar(@Parent() parent: PressPublicationEntity): Promise<FileEntity | null> {
        if (!parent.avatar_id) return null;
        return this.dataLoader.relationBatchOne(FileEntity).load(parent.avatar_id);
    }

    @ResolveField(() => [IssueEntity], { nullable: true })
    async issues(@Parent() parent: PressPublicationEntity): Promise<IssueEntity[]> {
        return this.dataLoader.relationBatchOneMany(IssueEntity, 'pressPublication').load(parent.id);
    }

    @ResolveField(() => [IssuePageEntity], { nullable: true })
    async issuePages(@Parent() parent: PressPublicationEntity): Promise<IssuePageEntity[]> {
        return this.dataLoader.relationBatchOneMany(IssuePageEntity, 'pressPublication').load(parent.id);
    }

    @ResolveField(() => [ArticleEntity], { nullable: true })
    async articles(@Parent() parent: PressPublicationEntity): Promise<ArticleEntity[]> {
        return this.dataLoader.relationBatchOneMany(ArticleEntity, 'pressPublication').load(parent.id);
    }

    @Query(() => PressPublicationsModel, { name: 'press_publications_list' })
    async index(@Args('body') body: BasePaginationInput): Promise<IPaginatedType<PressPublicationEntity>> {
        return this.pressPublicationsService.search(body);
    }

    @Query(() => PressPublicationEntity, { name: 'press_publications_detail' })
    async view(@Args('id', { type: () => Int }) id: number): Promise<PressPublicationEntity> {
        return this.pressPublicationsService.findOne(id).then((res) => {
            if (!res) throw new NotFoundException();
            return res;
        });
    }

    @Mutation(() => PressPublicationEntity, { name: 'press_publications_create' })
    async store(
        @Args('body') body: PressPublicationSaveInputDto,
        @AuthUser() auth: UserEntity
    ): Promise<PressPublicationEntity> {
        return this.pressPublicationsService.create({ ...body, created_by: auth.id });
    }

    @Mutation(() => PressPublicationEntity, { name: 'press_publications_update' })
    async update(
        @Args('id', { type: () => Int }) id: number,
        @Args('body') body: PressPublicationSaveInputDto,
        @AuthUser() auth: UserEntity
    ): Promise<PressPublicationEntity> {
        return this.pressPublicationsService.update(id, body, auth.id);
    }

    @Mutation(() => Boolean, { name: 'press_publications_delete' })
    async destroy(@Args('id', { type: () => Int }) id: number, @AuthUser() auth: UserEntity): Promise<boolean> {
        await this.pressPublicationsService.softDelete(id, auth.id);
        return true;
    }
}
