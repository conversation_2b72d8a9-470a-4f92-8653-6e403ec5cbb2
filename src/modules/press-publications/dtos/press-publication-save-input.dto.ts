import { Field, InputType, Int } from '@nestjs/graphql';
import { IsArray, IsEnum, IsInt, IsNotEmpty, IsOptional, IsPositive, IsString } from 'class-validator';
import { validationMessagesLang } from '../../../languages/validation-messages.lang';
import { BaseUpdateInputDto } from '../../../commons/bases/base.input';
import { ItemStatus } from '../../../commons/enums.common';
import { IdExists } from '../../../commons/validators/id-exists.validator';
import { DepartmentEntity } from '../../../entities/department.entity';
import { FileEntity } from '../../../entities/file.entity';
import { AutoSanitize } from '../../../commons/decorators/sanitize.decorators';
import { IsUnique } from '../../../commons/validators/is-unique.validator';
import { PressPublicationEntity, IssueType, PageSize } from '../../../entities/press-publication.entity';

@InputType()
@AutoSanitize()
export class PressPublicationSaveInputDto extends BaseUpdateInputDto {
    @Field()
    @IsNotEmpty({ message: validationMessagesLang.REQUIRED })
    @IsString()
    @IsUnique(PressPublicationEntity)
    name: string;

    @Field(() => Int)
    @IsEnum(ItemStatus)
    status_id: ItemStatus;

    @Field(() => Int)
    @IsNotEmpty({ message: validationMessagesLang.REQUIRED })
    @IsInt()
    @IsPositive()
    display_order: number;

    @Field(() => Int, { nullable: true })
    @IsOptional()
    @IdExists(FileEntity)
    avatar_id?: number;

    @Field({ nullable: true })
    @IsOptional()
    @IsString()
    issue_title?: string;

    @Field(() => Int)
    @IsEnum(ItemStatus)
    issue_status_id: ItemStatus;

    @Field(() => Int)
    @IsEnum(IssueType)
    issue_type_id: IssueType;

    @Field(() => [Int])
    @IsArray()
    @IsInt({ each: true })
    issue_days: number[];

    @Field(() => Int)
    @IsNotEmpty({ message: validationMessagesLang.REQUIRED })
    @IsInt()
    issue_offset: number;

    @Field(() => Int)
    @IsNotEmpty({ message: validationMessagesLang.REQUIRED })
    @IdExists(DepartmentEntity)
    department_id: number;

    @Field(() => Int)
    @IsNotEmpty({ message: validationMessagesLang.REQUIRED })
    @IsInt()
    page_count: number;

    @Field(() => Int)
    @IsNotEmpty({ message: validationMessagesLang.REQUIRED })
    @IsInt()
    issue_pre_created: number;

    @Field(() => Int, { nullable: true })
    @IsOptional()
    @IsEnum(PageSize)
    page_size_id?: PageSize;
}
