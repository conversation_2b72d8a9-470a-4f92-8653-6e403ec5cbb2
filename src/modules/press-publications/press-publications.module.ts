import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { PressPublicationEntity } from '../../entities/press-publication.entity';
import { PressPublicationsService } from './services/press-publications.service';
import { PressPublicationsResolver } from './resolvers/press-publications.resolver';

@Module({
    imports: [TypeOrmModule.forFeature([PressPublicationEntity])],
    providers: [PressPublicationsService, PressPublicationsResolver],
    exports: [PressPublicationsService],
})
export class PressPublicationsModule {}
