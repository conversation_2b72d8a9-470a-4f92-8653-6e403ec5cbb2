export const validationMessagesLang = {
    IS_ENUM: (enumType: object) =>
        `The value provided is not valid. Allowed values: ${Object.values(enumType).join(', ')}`,
    REQUIRED: 'This field is required.',
    MIN_LENGTH: (length: number) => `The minimum length is ${length} characters.`,
    MAX_LENGTH: (length: number) => `The maximum length is ${length} characters.`,
    INVALID_NUMBER: 'This field must be a number.',
    INVALID_DATE: 'Invalid date format.',
};

export const userValidationMessages = {
    REQUIRED_PASSWORD: 'Please enter a password.',
    REQUIRED_CONFIRM_PASSWORD: 'Please confirm your password.',
    CONFIRM_PASSWORD: 'The passwords do not match.',
    INVALID_EMAIL: 'Please enter a valid email address.',
    REQUIRED_EMAIL: 'Please enter your email.',
    REQUIRED_CONFIRM_EMAIL: 'Please confirm your email.',
    CONFIRM_EMAIL: 'The email addresses do not match.',
};

export const groupValidationMessages = {
    IS_EXISTS: 'A group with this name already exists.',
};
