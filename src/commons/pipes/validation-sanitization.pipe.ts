import { ArgumentMetadata, BadRequestException, Injectable, PipeTransform } from '@nestjs/common';
import { plainToInstance } from 'class-transformer';
import { validate } from 'class-validator';
import { sanitize } from 'class-sanitizer';
import 'reflect-metadata';

@Injectable()
export class ValidationSanitizationPipe implements PipeTransform<any> {
    async transform(value: any, metadata: ArgumentMetadata) {
        const { metatype, type } = metadata;

        // Bỏ qua các ResolveField (type === 'custom')
        if (!metatype || !this.toValidate(metatype) || type === 'custom') {
            return value;
        }

        const object = plainToInstance(metatype, value);

        // Sanitize the object first
        sanitize(object);

        // Then validate it
        const errors = await validate(object, {
            whitelist: true,
            forbidNonWhitelisted: false, // Cho phép các thuộc tính không được khai báo trong DTO
            forbidUnknownValues: false, // Cho phép các giá trị không xác <PERSON>
        });

        if (errors.length > 0) {
            const messages = errors.map((error) => {
                const constraints = error.constraints ? Object.values(error.constraints) : [];
                return `${error.property}: ${constraints.join(', ')}`;
            });

            throw new BadRequestException({
                message: 'Validation failed',
                errors: messages,
            });
        }

        return object;
    }

    private toValidate(metatype: any): boolean {
        const types = [String, Boolean, Number, Array, Object];
        return !types.includes(metatype);
    }
}
