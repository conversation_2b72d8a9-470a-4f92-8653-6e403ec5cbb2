import { Column, CreateDate<PERSON>olumn, DeleteDate<PERSON><PERSON>umn, Join<PERSON><PERSON>um<PERSON>, ManyToOne, UpdateDateColumn } from 'typeorm';
import { Field, GraphQLISODateTime, ObjectType } from '@nestjs/graphql';
import { BaseIdEntity } from './base-id.entity';

@ObjectType()
export class BaseEntity extends BaseIdEntity {
    @Column({ nullable: true })
    @Field({ nullable: true })
    created_by?: number;

    @Column({ nullable: true })
    @Field({ nullable: true })
    updated_by?: number;

    @Column({ nullable: true })
    @Field({ nullable: true })
    deleted_by?: number;

    @CreateDateColumn({ type: 'timestamptz' })
    @Field(() => GraphQLISODateTime)
    created_at: Date;

    @UpdateDateColumn({ type: 'timestamptz', nullable: true })
    @Field(() => GraphQLISODateTime, { nullable: true })
    updated_at?: Date;

    @DeleteDateColumn({ type: 'timestamptz', nullable: true })
    @Field(() => GraphQLISODateTime, { nullable: true })
    deleted_at?: Date;

    @ManyToOne('UserEntity', { onDelete: 'RESTRICT' })
    @JoinColumn({ name: 'created_by' })
    createdByUser: any;

    @ManyToOne('UserEntity', { nullable: true, onDelete: 'RESTRICT' })
    @JoinColumn({ name: 'updated_by' })
    updatedByUser?: any;

    @ManyToOne('UserEntity', { nullable: true, onDelete: 'RESTRICT' })
    @JoinColumn({ name: 'deleted_by' })
    deletedByUser?: any;
}
