import { CallH<PERSON>ler, ExecutionContext, Injectable, NestInterceptor } from '@nestjs/common';
import { GqlExecutionContext } from '@nestjs/graphql';
import { Observable } from 'rxjs';
import { map } from 'rxjs/operators';

@Injectable()
export class AssignIdToBodyInterceptor implements NestInterceptor {
    intercept(context: ExecutionContext, next: CallHandler): Observable<any> {
        const gqlContext = GqlExecutionContext.create(context);
        const info = gqlContext.getInfo();
        const args = gqlContext.getArgs();
        const body = args?.body;
        const id = args?.id;
        const auth = gqlContext.getContext().req?.user;

        if (info.fieldName === 'auth_update' && auth?.id && body && typeof body === 'object') {
            body.id = auth.id;
            return next.handle().pipe(map((data) => data));
        }

        if (!id || !body || typeof body !== 'object') {
            return next.handle();
        }
        // Gán id vào body
        body.id = args.id;

        return next.handle().pipe(map((data) => data));
    }
}
