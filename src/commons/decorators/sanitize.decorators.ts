import { Escape, NormalizeEmail, StripLow, Trim } from 'class-sanitizer';
import 'reflect-metadata';

// Metadata key để đánh dấu field đã có sanitize decorator
export const SANITIZE_FIELD_KEY = 'sanitize:field';

// Metadata key để đánh dấu class đã có AutoSanitize decorator
export const AUTO_SANITIZE_CLASS_KEY = 'sanitize:auto';

// Tạo các decorator tùy chỉnh cho toLowerCase và toUpperCase
export function ToLowerCase() {
    return function (target: any, propertyKey: string) {
        // Đánh dấu field đã có sanitize decorator
        Reflect.defineMetadata(SANITIZE_FIELD_KEY, true, target, propertyKey);

        // Thêm metadata cho sanitize
        const sanitize = (value: any): any => {
            if (value === null || value === undefined) return value;
            return String(value).toLowerCase();
        };

        // Lưu sanitize function vào metadata
        const key = `sanitize:${propertyKey}`;
        Reflect.defineMetadata(key, sanitize, target);
    };
}

export function ToUpperCase() {
    return function (target: any, propertyKey: string) {
        // Đánh dấu field đã có sanitize decorator
        Reflect.defineMetadata(SANITIZE_FIELD_KEY, true, target, propertyKey);

        // Thêm metadata cho sanitize
        const sanitize = (value: any): any => {
            if (value === null || value === undefined) return value;
            return String(value).toUpperCase();
        };

        // Lưu sanitize function vào metadata
        const key = `sanitize:${propertyKey}`;
        Reflect.defineMetadata(key, sanitize, target);
    };
}

/**
 * Sanitize chuỗi cơ bản: Trim, Escape HTML, StripLow
 * Sử dụng cho hầu hết các trường văn bản
 */
export function SanitizeBasic() {
    return function (target: any, propertyKey: string) {
        // Đánh dấu field đã có sanitize decorator
        Reflect.defineMetadata(SANITIZE_FIELD_KEY, true, target, propertyKey);

        // Áp dụng các decorator sanitize
        Trim()(target, propertyKey);
        Escape()(target, propertyKey);
        StripLow()(target, propertyKey);
    };
}

/**
 * Sanitize chuỗi an toàn: Trim, Escape HTML, StripLow, ToLowerCase
 * Sử dụng cho các trường như username, slug, v.v.
 */
export function SanitizeSafe() {
    return function (target: any, propertyKey: string) {
        // Đánh dấu field đã có sanitize decorator
        Reflect.defineMetadata(SANITIZE_FIELD_KEY, true, target, propertyKey);

        // Áp dụng các decorator sanitize
        Trim()(target, propertyKey);
        Escape()(target, propertyKey);
        StripLow()(target, propertyKey);
        ToLowerCase()(target, propertyKey); // Chuyển thành chữ thường
    };
}

/**
 * Sanitize email: Trim, NormalizeEmail, ToLowerCase
 * Sử dụng cho các trường email
 */
export function SanitizeEmail() {
    return function (target: any, propertyKey: string) {
        // Đánh dấu field đã có sanitize decorator
        Reflect.defineMetadata(SANITIZE_FIELD_KEY, true, target, propertyKey);

        // Áp dụng các decorator sanitize
        Trim()(target, propertyKey);
        NormalizeEmail()(target, propertyKey); // Chuẩn hóa email
        ToLowerCase()(target, propertyKey);
    };
}

/**
 * Sanitize mã: Trim, ToUpperCase
 * Sử dụng cho các trường mã, ví dụ: mã sản phẩm, mã đơn hàng
 */
export function SanitizeCode() {
    return function (target: any, propertyKey: string) {
        // Đánh dấu field đã có sanitize decorator
        Reflect.defineMetadata(SANITIZE_FIELD_KEY, true, target, propertyKey);

        // Áp dụng các decorator sanitize
        Trim()(target, propertyKey);
        ToUpperCase()(target, propertyKey); // Chuyển thành chữ hoa
    };
}

/**
 * Sanitize nội dung: Trim, StripLow
 * Sử dụng cho các trường nội dung dài, cho phép HTML
 */
export function SanitizeContent() {
    return function (target: any, propertyKey: string) {
        // Đánh dấu field đã có sanitize decorator
        Reflect.defineMetadata(SANITIZE_FIELD_KEY, true, target, propertyKey);

        // Áp dụng các decorator sanitize
        Trim()(target, propertyKey);
        StripLow()(target, propertyKey);
    };
}

/**
 * Decorator để tự động áp dụng SanitizeBasic cho các field không có decorator sanitize nào
 * Sử dụng ở mức class
 */
export function AutoSanitize() {
    return function (target: any) {
        // Đánh dấu class đã có AutoSanitize decorator
        Reflect.defineMetadata(AUTO_SANITIZE_CLASS_KEY, true, target);

        // Lấy tất cả các property của class
        const properties = Object.getOwnPropertyNames(target.prototype);

        // Lọc ra các property là method (không phải field)
        const methods = properties.filter((prop) => {
            return typeof target.prototype[prop] === 'function' && prop !== 'constructor';
        });

        // Lọc ra các property là field (không phải method)
        const fields = properties.filter((prop) => {
            return !methods.includes(prop) && prop !== 'constructor';
        });

        // Áp dụng SanitizeBasic cho các field chưa có decorator sanitize nào
        fields.forEach((field) => {
            // Kiểm tra xem field đã có decorator sanitize nào chưa
            const hasSanitize = Reflect.getMetadata(SANITIZE_FIELD_KEY, target.prototype, field);

            // Nếu chưa có decorator sanitize nào, áp dụng SanitizeBasic
            if (!hasSanitize) {
                // Đánh dấu field đã có sanitize decorator
                Reflect.defineMetadata(SANITIZE_FIELD_KEY, true, target.prototype, field);

                // Áp dụng các decorator sanitize cơ bản
                Trim()(target.prototype, field);
                Escape()(target.prototype, field);
                StripLow()(target.prototype, field);
            }
        });
    };
}
