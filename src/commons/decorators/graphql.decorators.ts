import { applyDecorators, UseGuards } from '@nestjs/common';
import { Mutation, Query, Resolver } from '@nestjs/graphql';
import { ReturnTypeFunc } from '@nestjs/graphql/dist/interfaces/return-type-func.interface';
import { MutationOptions } from '@nestjs/graphql/dist/decorators/mutation.decorator';
import { QueryOptions } from '@nestjs/graphql/dist/decorators/query.decorator';
import { JwtAuthGuard } from '../../modules/auth/jwt-auth.guard';
import { StaticTokenAuthGuard } from '../../modules/auth/static-token-auth.guard';

export function AuthResolver(model?: Function) {
    return applyDecorators(model ? Resolver(() => model) : Resolver(), UseGuards(JwtAuthGuard));
}

export const AuthQuery = (typeFunc: ReturnTypeFunc, options?: QueryOptions) => {
    return applyDecorators(UseGuards(JwtAuthGuard), Query(typeFunc, options));
};
export const AuthMutation = (typeFunc: ReturnTypeFunc, options?: MutationOptions) => {
    return applyDecorators(UseGuards(JwtAuthGuard), Mutation(typeFunc, options));
};

// Static Token Authentication Decorators
export function StaticAuthResolver(model?: Function) {
    return applyDecorators(model ? Resolver(() => model) : Resolver(), UseGuards(StaticTokenAuthGuard));
}

export const StaticAuthQuery = (typeFunc: ReturnTypeFunc, options?: QueryOptions) => {
    return applyDecorators(UseGuards(StaticTokenAuthGuard), Query(typeFunc, options));
};

export const StaticAuthMutation = (typeFunc: ReturnTypeFunc, options?: MutationOptions) => {
    return applyDecorators(UseGuards(StaticTokenAuthGuard), Mutation(typeFunc, options));
};
