import { ArgumentsHost, Catch, ExceptionFilter, HttpException, HttpStatus } from '@nestjs/common';
import { GqlArgumentsHost } from '@nestjs/graphql';
import { GraphQLError } from 'graphql';
import appConf from '../../configs/app.conf';

@Catch()
export class HttpExceptionFilter implements ExceptionFilter {
    catch(exception: any, host: ArgumentsHost) {
        // Tạo GqlArgumentsHost để kiểm tra kiểu context (GraphQL hay HTTP)
        const gqlHost = GqlArgumentsHost.create(host);
        if (appConf.NODE_ENV !== 'production') console.log('🔥 Errors:', exception); // Debug lỗi

        // Nếu request đến từ GraphQL
        // @ts-ignore
        if (gqlHost.getType() === 'graphql') {
            // Xác định status code: nếu exception là HttpException thì dùng getStatus(), nếu không thì dùng INTERNAL_SERVER_ERROR
            const status =
                exception instanceof HttpException ? exception.getStatus() : HttpStatus.INTERNAL_SERVER_ERROR;
            // Ném ra GraphQLError với thông tin lỗi trong phần extensions
            throw new GraphQLError(exception.message, {
                extensions: {
                    code: status,
                    errors: exception.response?.errors || null,
                    // Bạn có thể thêm thông tin stacktrace nếu cần, ví dụ:
                    // stacktrace: exception.stack,
                },
            });
        }

        // Nếu request là HTTP (REST API)
        const ctx = host.switchToHttp();
        const response = ctx.getResponse();
        const status = exception instanceof HttpException ? exception.getStatus() : HttpStatus.INTERNAL_SERVER_ERROR;

        response.status(status).json({
            errors: [
                {
                    statusCode: status,
                    message: exception.message,
                },
            ],
        });
    }
}
