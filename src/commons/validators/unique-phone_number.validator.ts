import {
    registerDecorator,
    ValidationArguments,
    ValidationOptions,
    ValidatorConstraint,
    ValidatorConstraintInterface,
} from 'class-validator';
import { Injectable } from '@nestjs/common';
import { AuthService } from '../../modules/auth/services/auth.service';

@ValidatorConstraint({ async: true })
@Injectable()
export class UniquePhoneNumberByRoleConstraint implements ValidatorConstraintInterface {
    constructor(private readonly authService: AuthService) {}

    async validate(phoneNumber: string, args: ValidationArguments) {
        if (!phoneNumber) return true;

        const { object } = args;
        let roleId = (object as any).role_id;
        const userId = (object as any).id;
        if (userId) {
            const user = await this.authService.findOne(userId);
            roleId = roleId ?? user?.role_id;
        }
        const query = this.authService.repo
            .createQueryBuilder('user')
            .where('user.phone_number = :phoneNumber', { phoneNumber })
            .andWhere('user.role_id = :roleId', { roleId });

        if (userId) {
            query.andWhere('user.id != :userId', { userId });
        }

        const count = await query.getCount();
        return count === 0;
    }

    defaultMessage(args: ValidationArguments) {
        return `Phone number already exists!`;
    }
}

export function IsUniquePhoneNumberByRole(validationOptions?: ValidationOptions) {
    return function (object: Object, propertyName: string) {
        registerDecorator({
            target: object.constructor,
            propertyName: propertyName,
            options: {
                ...validationOptions,
                message: validationOptions?.message || 'Phone number already exists!',
            },
            constraints: [],
            validator: UniquePhoneNumberByRoleConstraint,
        });
    };
}
