import {
    registerDecorator,
    ValidationArguments,
    ValidationOptions,
    ValidatorConstraint,
    ValidatorConstraintInterface,
} from 'class-validator';
import { BadRequestException, Injectable } from '@nestjs/common';
import { DataSource, Not } from 'typeorm';
import { WorkflowEntity, WorkflowType } from '../../entities/workflow.entity';

@ValidatorConstraint({ async: true })
@Injectable()
export class UniqueWorkflowByTypeConstraint implements ValidatorConstraintInterface {
    constructor(private readonly dataSource: DataSource) {}

    async validate(typeId: number, args: ValidationArguments) {
        if (!typeId) throw new BadRequestException('Type is required');
        if (typeId === WorkflowType.EDITING) return true;
        const { object } = args;
        const workflowId = (object as any).id;
        const departmentId = (object as any).department_id;

        if (!departmentId) throw new BadRequestException('Department ID is required');

        const repo = this.dataSource.getRepository(WorkflowEntity);
        const where = {
            workflow_type_id: typeId,
            department_id: departmentId,
        };

        if (workflowId) {
            where['id'] = Not(workflowId);
        }

        return !(await repo.existsBy(where));
    }

    defaultMessage(args: ValidationArguments) {
        return `Workflow type already exists in this department!`;
    }
}

export function UniqueWorkflowByType(validationOptions?: ValidationOptions) {
    return function (object: Object, propertyName: string) {
        registerDecorator({
            target: object.constructor,
            propertyName: propertyName,
            options: {
                ...validationOptions,
                message: validationOptions?.message || 'Workflow type already exists in this department!',
            },
            constraints: [],
            validator: UniqueWorkflowByTypeConstraint,
        });
    };
}
