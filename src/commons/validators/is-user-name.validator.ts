import {
    registerDecorator,
    ValidationArguments,
    ValidationOptions,
    ValidatorConstraint,
    ValidatorConstraintInterface,
} from 'class-validator';

@ValidatorConstraint({ async: false })
export class IsUserNameConstraint implements ValidatorConstraintInterface {
    validate(userName: string, _: ValidationArguments) {
        return /^(?![_-])[a-z0-9_-]+(?<![_-])$/.test(userName);
    }

    defaultMessage(_: ValidationArguments) {
        return 'Username can only contain letters (a-z) and numbers (0-9)';
    }
}

export function IsUserName(validationOptions?: ValidationOptions) {
    return function (object: Object, propertyName: string) {
        registerDecorator({
            target: object.constructor,
            propertyName: propertyName,
            options: validationOptions,
            constraints: [],
            validator: IsUserNameConstraint,
        });
    };
}
