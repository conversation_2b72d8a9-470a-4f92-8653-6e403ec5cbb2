import { Injectable } from '@nestjs/common';
import {
    registerDecorator,
    ValidationArguments,
    ValidationOptions,
    ValidatorConstraint,
    ValidatorConstraintInterface,
} from 'class-validator';
import { AuthService } from '../../modules/auth/services/auth.service';
import { Not } from 'typeorm';

@ValidatorConstraint({ async: true })
@Injectable()
export class IsUniqueConstraint implements ValidatorConstraintInterface {
    constructor(private authService: AuthService) {}

    async validate(value: any, args: ValidationArguments) {
        const [entityClass, property] = args.constraints;
        if (!entityClass) {
            return false;
        }

        const { object } = args;
        const id = (object as any).id;

        const propertyName = property || args.property;

        const repository = this.authService.repo.manager.getRepository(entityClass);
        const where = { [propertyName]: value };
        if (id) {
            const oldValue = await repository.findOneBy({ id });
            if (oldValue) where['id'] = Not(id);
        }
        const record = await repository.existsBy(where);

        // ✅ True nếu không có record trùng
        return !record;
    }

    defaultMessage(args: ValidationArguments) {
        const [_, property] = args.constraints;
        const propertyName = property || args.property;

        return `${propertyName} already exists.`;
    }
}

export function IsUnique(
    entity: Function | string,
    propertyOrOptions?: string | ValidationOptions,
    validationOptions?: ValidationOptions
) {
    return function (object: Object, propertyName: string) {
        let property: string | undefined;
        let options: ValidationOptions | undefined;

        // Kiểm tra kiểu dữ liệu của tham số thứ 2
        if (typeof propertyOrOptions === 'string') {
            property = propertyOrOptions; // Nếu là string => Đây là property
            options = validationOptions; // Tham số thứ 3 là options
        } else {
            options = propertyOrOptions; // Nếu là object => Đây là options
        }

        registerDecorator({
            target: object.constructor,
            propertyName: propertyName,
            options: options,
            constraints: [entity, property],
            validator: IsUniqueConstraint,
        });
    };
}
