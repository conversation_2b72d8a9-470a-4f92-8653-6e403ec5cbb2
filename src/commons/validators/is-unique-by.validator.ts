import { Injectable } from '@nestjs/common';
import {
    registerDecorator,
    ValidationArguments,
    ValidationOptions,
    ValidatorConstraint,
    ValidatorConstraintInterface,
} from 'class-validator';
import { AuthService } from '../../modules/auth/services/auth.service';
import { Not } from 'typeorm';

@ValidatorConstraint({ async: true })
@Injectable()
export class IsUniqueByConstraint implements ValidatorConstraintInterface {
    constructor(private authService: AuthService) {}

    async validate(value: any, args: ValidationArguments) {
        const [entityClass, property] = args.constraints;
        if (!entityClass) {
            return false;
        }

        const { object } = args;
        const id = (object as any).id;
        const propertyValue = (object as any)[property];
        if (propertyValue === undefined) {
            return false;
        }

        const valueName = args.property;

        const repository = this.authService.repo.manager.getRepository(entityClass);
        const where = { [valueName]: value, [property]: propertyValue };
        if (id) {
            const oldValue = await repository.findOneBy({ id });
            if (oldValue && oldValue[property] === propertyValue) where['id'] = Not(id);
        }
        const record = await repository.existsBy(where);

        // ✅ True nếu không có record trùng
        return !record;
    }

    defaultMessage(args: ValidationArguments) {
        const [_, property] = args.constraints;
        const propertyName = property || args.property;

        return `${propertyName} already exists.`;
    }
}

export function IsUniqueBy(entity: Function | string, property: string, validationOptions?: ValidationOptions) {
    return function (object: Object, propertyName: string) {
        registerDecorator({
            target: object.constructor,
            propertyName: propertyName,
            options: validationOptions,
            constraints: [entity, property],
            validator: IsUniqueByConstraint,
        });
    };
}
