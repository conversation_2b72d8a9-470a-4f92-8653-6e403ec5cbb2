import { ValidationArguments, ValidationOptions, registerDecorator } from 'class-validator';

export function EqualField(property: string, validationOptions?: ValidationOptions) {
    return function (object: Object, propertyName: string) {
        registerDecorator({
            name: 'equalField',
            target: object.constructor,
            propertyName,
            options: {
                ...validationOptions,
                message: validationOptions?.message || `$property must be equal to ${property}`,
            },
            constraints: [property],
            validator: {
                validate(value: any, args: ValidationArguments) {
                    const [relatedPropertyName] = args.constraints;
                    const relatedValue = (args.object as any)[relatedPropertyName];
                    if (relatedValue === undefined || value === undefined) {
                        return false;
                    }
                    return value === relatedValue;
                },
            },
        });
    };
}
