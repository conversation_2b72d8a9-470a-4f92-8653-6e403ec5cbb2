import { Injectable } from '@nestjs/common';
import {
    registerDecorator,
    ValidationArguments,
    ValidationOptions,
    ValidatorConstraint,
    ValidatorConstraintInterface,
} from 'class-validator';
import { AuthService } from '../../modules/auth/services/auth.service';

@ValidatorConstraint({ async: true })
@Injectable()
export class IsSureUniqueConstraint implements ValidatorConstraintInterface {
    constructor(private authService: AuthService) {}

    async validate(value: any, args: ValidationArguments) {
        const [entityClass, property] = args.constraints;
        if (!entityClass) {
            return false;
        }

        const propertyName = property || args.property;

        const repository = this.authService.repo.manager.getRepository(entityClass);
        const where = { [propertyName]: value };
        const record = await repository.existsBy(where);

        // ✅ True nếu không có record trùng
        return !record;
    }

    defaultMessage(args: ValidationArguments) {
        const [_, property] = args.constraints;
        const propertyName = property || args.property;

        return `${propertyName} already exists.`;
    }
}

export function IsSureUnique(
    entity: Function | string,
    propertyOrOptions?: string | ValidationOptions,
    validationOptions?: ValidationOptions
) {
    return function (object: Object, propertyName: string) {
        let property: string | undefined;
        let options: ValidationOptions | undefined;

        // Kiểm tra kiểu dữ liệu của tham số thứ 2
        if (typeof propertyOrOptions === 'string') {
            property = propertyOrOptions; // Nếu là string => Đây là property
            options = validationOptions; // Tham số thứ 3 là options
        } else {
            options = propertyOrOptions; // Nếu là object => Đây là options
        }

        registerDecorator({
            target: object.constructor,
            propertyName: propertyName,
            options: options,
            constraints: [entity, property || undefined],
            validator: IsSureUniqueConstraint,
        });
    };
}
