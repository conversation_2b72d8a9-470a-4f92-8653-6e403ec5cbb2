import { registerDecorator, ValidationOptions, ValidationArguments } from 'class-validator';

export function LessThanEqualField(property: string, validationOptions?: ValidationOptions) {
    return function (object: Object, propertyName: string) {
        registerDecorator({
            name: 'lessThanEqualField',
            target: object.constructor,
            propertyName: propertyName,
            constraints: [property],
            options: {
                ...validationOptions,
                message:
                    validationOptions?.message ||
                    `The value of ${propertyName} must be less than or equal to ${property}.`,
            },
            validator: {
                validate(value: any, args: ValidationArguments) {
                    const [relatedPropertyName] = args.constraints;
                    const relatedValue = (args.object as any)[relatedPropertyName];
                    if (relatedValue === undefined || value === undefined) {
                        return false;
                    }

                    const valueToCompare = !isNaN(Date.parse(value)) ? new Date(value) : value;
                    const relatedValueToCompare = !isNaN(Date.parse(relatedValue))
                        ? new Date(relatedValue)
                        : relatedValue;
                    return valueToCompare <= relatedValueToCompare;
                },
            },
        });
    };
}
