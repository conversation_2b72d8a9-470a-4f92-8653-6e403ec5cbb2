import {
    registerDecorator,
    ValidationArguments,
    ValidationOptions,
    ValidatorConstraint,
    ValidatorConstraintInterface,
} from 'class-validator';

@ValidatorConstraint({ async: false })
export class IsPasswordConstraint implements ValidatorConstraintInterface {
    validate(password: string, args: ValidationArguments) {
        return (
            password.length >= 12 &&
            /[A-Z]/.test(password) &&
            /[a-z]/.test(password) &&
            /\d/.test(password) &&
            /[!@#$%^&*(),.?":{}|<>]/.test(password)
        );
    }

    defaultMessage(args: ValidationArguments) {
        const password = args.value as string;
        const errors: string[] = [];

        if (password.length < 12) {
            errors.push('Use 12 characters or more for your password.');
        }
        if (!/[A-Z]/.test(password)) {
            errors.push('The password should contain at least one uppercase letter.');
        }
        if (!/[a-z]/.test(password)) {
            errors.push('The password should contain at least one lowercase letter.');
        }
        if (!/\d/.test(password)) {
            errors.push('The password should contain at least one numeric character.');
        }
        if (!/[!@#$%^&*(),.?":{}|<>]/.test(password)) {
            errors.push('The password should contain at least one special character.');
        }

        return errors.join('\n');
    }
}

export function IsPassword(validationOptions?: ValidationOptions) {
    return function (object: Object, propertyName: string) {
        registerDecorator({
            target: object.constructor,
            propertyName: propertyName,
            options: validationOptions,
            constraints: [],
            validator: IsPasswordConstraint,
        });
    };
}
