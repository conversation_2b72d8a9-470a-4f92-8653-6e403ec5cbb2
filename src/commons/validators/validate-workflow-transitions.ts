import { registerDecorator, ValidationArguments, ValidationOptions } from 'class-validator';
import { WorkflowTransitionInput } from '../../modules/workflows/dtos/workflow-permission-save-input.dto';

export function ValidateWorkflowTransitions(validationOptions?: ValidationOptions) {
    return function (object: Object, propertyName: string) {
        registerDecorator({
            name: 'equalField',
            target: object.constructor,
            propertyName,
            options: {
                ...validationOptions,
                message: validationOptions?.message || `$property is not valid`,
            },
            constraints: [],
            validator: {
                validate(value: WorkflowTransitionInput[], args: ValidationArguments) {
                    const relatedValue: number[] = (args.object as any)['workflow_ids'];
                    if (relatedValue === undefined || value === undefined) {
                        return false;
                    }
                    if (!!value[0].from) return false;
                    const transitionValues = new Set<number>();
                    value.forEach((item) => {
                        if (item.from) transitionValues.add(item.from);
                        transitionValues.add(item.to);
                    });
                    return Array.from(transitionValues).every((item) => relatedValue.includes(item));
                },
            },
        });
    };
}
