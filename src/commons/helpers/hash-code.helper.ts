import { promisify } from 'util';
import { randomBytes, scrypt as _scrypt, createHmac, createHash } from 'node:crypto';
import * as bcrypt from 'bcrypt';

const scrypt = promisify(_scrypt);

export const getHash = async (userPass: string, salt: string): Promise<Buffer> => {
    return (await scrypt(userPass, salt, 32)) as Buffer;
};

export const genPassword = async (password: string): Promise<string> => {
    const salt = await bcrypt.genSalt(10);
    return bcrypt.hash(password, salt);
};

export const comparePassword = async (comparePassword: string, dbPassword: string): Promise<boolean> => {
    return bcrypt.compare(comparePassword, dbPassword);
};

export const encryptToken = async (token: string) => {
    const secretKey = randomBytes(16);
    const hash = createHmac('sha256', secretKey).update(token).digest('hex');
    return hash + '.' + secretKey.toString('hex');
};

export const compareToken = async (token: string, encryptString: string) => {
    const [hashToken, secretEncrypt] = encryptString.split('.');
    const secretKey = Buffer.from(secretEncrypt, 'hex');
    const hash = createHmac('sha256', secretKey).update(token).digest('hex');
    return hashToken === hash;
};

export const md5Encode = (input: string): string => {
    const hash = createHash('md5');
    hash.update(input);
    return hash.digest('hex');
};

export const sha256Encode = (input: string): string => {
    const hash = createHash('sha256');
    hash.update(input);
    return hash.digest('hex');
};
