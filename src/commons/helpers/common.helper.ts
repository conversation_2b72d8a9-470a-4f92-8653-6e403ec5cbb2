import { existsSync, mkdirSync, PathLike } from 'fs';
import { ResponseSuccess } from '../interfaces.common';

export const delay = (ms: number) => new Promise((resolve) => setTimeout(resolve, ms));

export const mkdir = (path: PathLike) => {
    if (!existsSync(path)) mkdirSync(path, { recursive: true });
};

export const formatResponse = <T>(data: T, message: string): ResponseSuccess<T> => ({
    data,
    message,
});
