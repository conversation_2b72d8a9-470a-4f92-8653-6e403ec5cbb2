import appConf from '../../configs/app.conf';

export const CONTROLLER_PREFIX = 'files';
export const UPLOAD_DIR = 'uploads';
export const IMAGES_PATH = 'images';
export const VIDEOS_PATH = 'videos';
export const AUDIOS_PATH = 'audios';
export const DOCUMENTS_PATH = 'documents';
export const OTHERS_PATH = 'others';

export const normalizePath = (path: string) => path.replace(/\\/g, '/');

export const convertUploadPath = (filePath: string) => {
    filePath = normalizePath(filePath);
    return filePath.replace(`${UPLOAD_DIR}/`, `${CONTROLLER_PREFIX}/`);
};

export const pathToUrl = (filePath: string) => appConf.API_URL + convertUploadPath(filePath);
