import { ApolloServerPlugin, GraphQLRequestListener, GraphQLServiceContext } from 'apollo-server-plugin-base';
import { GraphQLError, GraphQLSchema } from 'graphql';
import { fieldExtensionsEstimator, getComplexity, simpleEstimator } from 'graphql-query-complexity';
import { Plugin } from '@nestjs/apollo';
import appConf from '../../configs/app.conf';
import constantConf from '../../configs/constant.conf';

@Plugin()
export class ComplexityPlugin implements ApolloServerPlugin {
    private schema: GraphQLSchema;
    constructor() {}
    async serverWillStart(service: GraphQLServiceContext) {
        this.schema = service.schema;
    }

    async requestDidStart(): Promise<GraphQLRequestListener> {
        const { maxComplexity, defaultComplexity, excludedOperations } = constantConf.graphql;
        const schema = this.schema;

        return {
            async didResolveOperation({ request, document }) {
                const operationName = request.operationName;

                if (operationName && excludedOperations.includes(operationName)) {
                    return; // Skip excluded operations
                }

                const complexity = getComplexity({
                    schema,
                    operationName: request.operationName,
                    query: document,
                    variables: request.variables,
                    estimators: [fieldExtensionsEstimator(), simpleEstimator({ defaultComplexity })],
                });

                if (complexity > maxComplexity) {
                    throw new GraphQLError(
                        `Query is too complex: ${complexity}. Maximum allowed complexity: ${maxComplexity}`
                    );
                }

                // Chỉ ghi log trong môi trường phát triển
                if (appConf.NODE_ENV !== 'production') {
                    console.log('Query Complexity:', complexity);
                }
            },
        };
    }
}
