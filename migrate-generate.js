const { spawn } = require('child_process');
const readline = require('readline');
const path = require('path');
const { AppDataSource } = require('./src/configs/data-source.conf');

const rl = readline.createInterface({
    input: process.stdin,
    output: process.stdout
});

// Get migrations directory from AppDataSource
const migrationsDir = path.resolve(
    __dirname,
    AppDataSource.options.migrations[0].replace('/*{.ts,.js}', '')
);
console.log(`Using migrations directory: ${migrationsDir}`);

// Get DataSource file path
const dataSourcePath = path.relative(__dirname, require.resolve('./src/configs/data-source.conf'));
console.log(`Using DataSource: ${dataSourcePath}`);

const migrationName = process.argv[2];

if (migrationName) {
    generateMigration(migrationName);
} else {
    rl.question('Enter migration name: ', (name) => {
        generateMigration(name);
        rl.close();
    });
}

function generateMigration(name) {
    console.log(`🔹 Generating migration: ${name}...`);
    const typeormProcess = spawn(
        'ts-node',
        [
            '-r',
            'tsconfig-paths/register',
            'node_modules/typeorm/cli.js',
            'migration:generate',
            `${migrationsDir}/${name}`,
            '--dataSource',
            dataSourcePath
        ],
        { stdio: 'pipe', shell: true }
    );

    let output = '';

    typeormProcess.stdout.on('data', (data) => {
        output += data.toString();
        process.stdout.write(data);
    });

    typeormProcess.stderr.on('data', (data) => {
        output += data.toString();
        process.stderr.write(data);
    });

    typeormProcess.on('close', (code) => {
        if (output.includes('No changes in database schema were found')) {
            console.log('⚠️ No changes detected in the database schema. Migration was not created.');
            process.exit(0);
        } else {
            console.log(`✅ Migration "${name}" has been successfully generated.`);
            process.exit(code);
        }
    });
}
