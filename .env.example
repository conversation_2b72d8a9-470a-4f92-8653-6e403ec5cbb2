
NODE_ENV=development

#Database
PG_HOST=localhost
PG_PORT=5432
PG_USER=postgres
PG_PASS=postgres
PG_DB=mediasoft_api

#App
CORS_ORIGIN=*
APP_PORT=8003
AT_SECRET=
AT_TIMEOUT=24h
RT_SECRET=
RT_TIMEOUT=30d
LIMIT_SEND_FORGOT_PASS=5
OTP_EXPIRATION_TIME=1#hours
PAGE_DEFAULT=20
LIMIT_UPLOAD_SIZE=2#GB
MAX_FILES_PER_UPLOAD=5
REQUEST_TIMEOUT=15#minutes
ALLOWED_FILE_TYPES=jpg,jpeg,png,gif,webp,bmp,mp4,avi,mov,wmv,mpg,mpeg,mkv,flv,webm,3gp,m4v,zip,rar,pdf,mp3,aac,doc,docx,txt
ALLOWED_MIME_TYPES=image/jpeg,image/png,image/gif,image/webp,image/bmp,video/mp4,video/x-msvideo,video/quicktime,video/x-ms-wmv,video/mpeg,video/x-matroska,video/x-flv,video/webm,video/3gpp,video/x-m4v,application/zip,application/x-rar-compressed,application/vnd.rar,application/pdf,audio/mpeg,audio/aac,audio/x-aac,application/msword,application/vnd.openxmlformats-officedocument.wordprocessingml.document,text/plain
API_URL=http://localhost:8003/
STATIC_BEARER_TOKEN=your_static_token_here
ROOT_PATH=#Leave empty to use current working directory

#Mail
MAIL_HOST=
MAIL_PORT=
MAIL_USER=
MAIL_PASSWORD=
MAIL_FROM=
SUPPORT_MAIL=

#Article revalidate
ARTICLE_REVALIDATE_URL=
ARTICLE_REVALIDATE_TOKEN=