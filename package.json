{"name": "nest-grapql", "version": "0.0.1", "description": "", "author": "", "private": true, "license": "UNLICENSED", "scripts": {"build": "nest build", "format": "prettier --write \"src/**/*.ts\" \"test/**/*.ts\"", "start": "nest start", "start:dev": "nest start --watch", "start:debug": "nest start --debug --watch", "start:prod": "node dist/main", "lint": "eslint \"{src,apps,libs,test}/**/*.ts\" --fix", "test": "jest", "test:watch": "jest --watch", "test:cov": "jest --coverage", "test:debug": "node --inspect-brk -r tsconfig-paths/register -r ts-node/register node_modules/.bin/jest --runInBand", "test:e2e": "jest --config ./test/jest-e2e.json", "migrate:generate": "ts-node -r tsconfig-paths/register migrate-generate.js", "migrate:create": "ts-node migrate-create.js", "migrate:up": "ts-node ./node_modules/typeorm/cli.js migration:run -d src/configs/data-source.conf.ts", "migrate:down": "ts-node ./node_modules/typeorm/cli.js migration:revert -d src/configs/data-source.conf.ts", "migrate:down:all": "node migrate-rollback-all.js"}, "dependencies": {"@apollo/server": "^4.12.0", "@nestjs-modules/mailer": "^2.0.2", "@nestjs/apollo": "^13.1.0", "@nestjs/axios": "^4.0.1", "@nestjs/common": "^11.0.20", "@nestjs/config": "^4.0.2", "@nestjs/core": "^11.0.20", "@nestjs/graphql": "^13.1.0", "@nestjs/jwt": "^11.0.0", "@nestjs/passport": "^11.0.5", "@nestjs/platform-express": "^11.0.20", "@nestjs/schedule": "^6.0.0", "@nestjs/typeorm": "^11.0.0", "@types/compression": "^1.7.5", "@types/connect-timeout": "^1.9.0", "@types/lodash": "^4.17.16", "@types/multer": "^1.4.12", "@types/nodemailer": "^6.4.17", "apollo-server-express": "^3.13.0", "axios": "^1.11.0", "bcrypt": "^5.1.1", "class-sanitizer": "^1.0.1", "class-transformer": "^0.5.1", "class-validator": "^0.14.1", "compression": "^1.8.0", "connect-timeout": "^1.9.0", "dataloader": "^2.2.3", "date-fns": "^4.1.0", "dotenv": "^16.5.0", "express-rate-limit": "^7.5.0", "express-useragent": "^1.0.15", "file-type": "^20.5.0", "graphql": "^16.10.0", "graphql-depth-limit": "^1.1.0", "graphql-query-complexity": "^1.1.0", "graphql-tools": "^9.0.18", "graphql-type-json": "^0.3.2", "helmet": "^8.1.0", "jsonwebtoken": "^9.0.2", "lodash": "^4.17.21", "nodemailer": "^6.10.1", "passport": "^0.7.0", "passport-jwt": "^4.0.1", "passport-local": "^1.0.0", "pg": "^8.14.1", "reflect-metadata": "^0.2.2", "rxjs": "^7.8.2", "slugify": "^1.6.6", "typeorm": "^0.3.22"}, "devDependencies": {"@eslint/eslintrc": "^3.3.1", "@eslint/js": "^9.24.0", "@nestjs/cli": "^11.0.6", "@nestjs/schematics": "^11.0.5", "@nestjs/testing": "^11.0.20", "@swc/cli": "^0.7.3", "@swc/core": "^1.11.21", "@types/bcrypt": "^5.0.2", "@types/express": "^5.0.1", "@types/express-useragent": "^1.0.5", "@types/graphql-depth-limit": "^1.1.6", "@types/jest": "^29.5.14", "@types/node": "^22.14.1", "@types/passport-jwt": "^4.0.1", "@types/supertest": "^6.0.3", "eslint": "^9.24.0", "eslint-config-prettier": "^10.1.2", "eslint-plugin-prettier": "^5.2.6", "globals": "^16.0.0", "jest": "^29.7.0", "prettier": "^3.5.3", "source-map-support": "^0.5.21", "supertest": "^7.1.0", "ts-jest": "^29.3.2", "ts-loader": "^9.5.2", "ts-node": "^10.9.2", "tsconfig-paths": "^4.2.0", "typescript": "^5.8.3", "typescript-eslint": "^8.30.1"}, "jest": {"moduleFileExtensions": ["js", "json", "ts"], "rootDir": "src", "testRegex": ".*\\.spec\\.ts$", "transform": {"^.+\\.(t|j)s$": "ts-jest"}, "collectCoverageFrom": ["**/*.(t|j)s"], "coverageDirectory": "../coverage", "testEnvironment": "node"}, "packageManager": "yarn@1.22.22+sha512.a6b2f7906b721bba3d67d4aff083df04dad64c399707841b7acf00f6b133b7ac24255f2652fa22ae3534329dc6180534e98d17432037ff6fd140556e2bb3137e"}